"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-visually-hi_da21897b5bc909f873add44d7cbc4eba";
exports.ids = ["vendor-chunks/@radix-ui+react-visually-hi_da21897b5bc909f873add44d7cbc4eba"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-visually-hi_da21897b5bc909f873add44d7cbc4eba/node_modules/@radix-ui/react-visually-hidden/dist/index.mjs":
/*!*****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-visually-hi_da21897b5bc909f873add44d7cbc4eba/node_modules/@radix-ui/react-visually-hidden/dist/index.mjs ***!
  \*****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   VisuallyHidden: () => (/* binding */ VisuallyHidden)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_997b35f2e2aa9d3174fc03a0f79e437b/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/visually-hidden/src/VisuallyHidden.tsx\n\n\n\nvar NAME = \"VisuallyHidden\";\nvar VisuallyHidden = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.span,\n      {\n        ...props,\n        ref: forwardedRef,\n        style: {\n          // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n          position: \"absolute\",\n          border: 0,\n          width: 1,\n          height: 1,\n          padding: 0,\n          margin: -1,\n          overflow: \"hidden\",\n          clip: \"rect(0, 0, 0, 0)\",\n          whiteSpace: \"nowrap\",\n          wordWrap: \"normal\",\n          ...props.style\n        }\n      }\n    );\n  }\n);\nVisuallyHidden.displayName = NAME;\nvar Root = VisuallyHidden;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-visually-hi_da21897b5bc909f873add44d7cbc4eba/node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\n");

/***/ })

};
;