"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-zoom-pan-pinch@3.7.0__ddc0427b0adf89251254484d2dac3217";
exports.ids = ["vendor-chunks/react-zoom-pan-pinch@3.7.0__ddc0427b0adf89251254484d2dac3217"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/react-zoom-pan-pinch@3.7.0__ddc0427b0adf89251254484d2dac3217/node_modules/react-zoom-pan-pinch/dist/index.esm.js":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-zoom-pan-pinch@3.7.0__ddc0427b0adf89251254484d2dac3217/node_modules/react-zoom-pan-pinch/dist/index.esm.js ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Context: () => (/* binding */ Context),\n/* harmony export */   KeepScale: () => (/* binding */ KeepScale),\n/* harmony export */   MiniMap: () => (/* binding */ MiniMap),\n/* harmony export */   TransformComponent: () => (/* binding */ TransformComponent),\n/* harmony export */   TransformWrapper: () => (/* binding */ TransformWrapper),\n/* harmony export */   getCenterPosition: () => (/* binding */ getCenterPosition),\n/* harmony export */   getMatrixTransformStyles: () => (/* binding */ getMatrixTransformStyles),\n/* harmony export */   getTransformStyles: () => (/* binding */ getTransformStyles),\n/* harmony export */   useControls: () => (/* binding */ useControls),\n/* harmony export */   useTransformComponent: () => (/* binding */ useTransformComponent),\n/* harmony export */   useTransformContext: () => (/* binding */ useTransformContext),\n/* harmony export */   useTransformEffect: () => (/* binding */ useTransformEffect),\n/* harmony export */   useTransformInit: () => (/* binding */ useTransformInit)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\n/**\n * Rounds number to given decimal\n * eg. roundNumber(2.34343, 1) => 2.3\n */\nvar roundNumber = function (num, decimal) {\n    return Number(num.toFixed(decimal));\n};\n/**\n * Checks if value is number, if not it returns default value\n * 1# eg. checkIsNumber(2, 30) => 2\n * 2# eg. checkIsNumber(null, 30) => 30\n */\nvar checkIsNumber = function (num, defaultValue) {\n    return typeof num === \"number\" ? num : defaultValue;\n};\n\nvar handleCallback = function (context, event, callback) {\n    if (callback && typeof callback === \"function\") {\n        callback(context, event);\n    }\n};\n\n/* eslint-disable no-plusplus */\n/* eslint-disable no-param-reassign */\n/**\n * Functions should return denominator of the target value, which is the next animation step.\n * t is a value from 0 to 1, reflecting the percentage of animation status.\n */\nvar easeOut = function (t) {\n    return -Math.cos(t * Math.PI) / 2 + 0.5;\n};\n// linear\nvar linear = function (t) {\n    return t;\n};\n// accelerating from zero velocity\nvar easeInQuad = function (t) {\n    return t * t;\n};\n// decelerating to zero velocity\nvar easeOutQuad = function (t) {\n    return t * (2 - t);\n};\n// acceleration until halfway, then deceleration\nvar easeInOutQuad = function (t) {\n    return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;\n};\n// accelerating from zero velocity\nvar easeInCubic = function (t) {\n    return t * t * t;\n};\n// decelerating to zero velocity\nvar easeOutCubic = function (t) {\n    return --t * t * t + 1;\n};\n// acceleration until halfway, then deceleration\nvar easeInOutCubic = function (t) {\n    return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;\n};\n// accelerating from zero velocity\nvar easeInQuart = function (t) {\n    return t * t * t * t;\n};\n// decelerating to zero velocity\nvar easeOutQuart = function (t) {\n    return 1 - --t * t * t * t;\n};\n// acceleration until halfway, then deceleration\nvar easeInOutQuart = function (t) {\n    return t < 0.5 ? 8 * t * t * t * t : 1 - 8 * --t * t * t * t;\n};\n// accelerating from zero velocity\nvar easeInQuint = function (t) {\n    return t * t * t * t * t;\n};\n// decelerating to zero velocity\nvar easeOutQuint = function (t) {\n    return 1 + --t * t * t * t * t;\n};\n// acceleration until halfway, then deceleration\nvar easeInOutQuint = function (t) {\n    return t < 0.5 ? 16 * t * t * t * t * t : 1 + 16 * --t * t * t * t * t;\n};\nvar animations = {\n    easeOut: easeOut,\n    linear: linear,\n    easeInQuad: easeInQuad,\n    easeOutQuad: easeOutQuad,\n    easeInOutQuad: easeInOutQuad,\n    easeInCubic: easeInCubic,\n    easeOutCubic: easeOutCubic,\n    easeInOutCubic: easeInOutCubic,\n    easeInQuart: easeInQuart,\n    easeOutQuart: easeOutQuart,\n    easeInOutQuart: easeInOutQuart,\n    easeInQuint: easeInQuint,\n    easeOutQuint: easeOutQuint,\n    easeInOutQuint: easeInOutQuint,\n};\n\n/* eslint-disable no-param-reassign */\nvar handleCancelAnimationFrame = function (animation) {\n    if (typeof animation === \"number\") {\n        cancelAnimationFrame(animation);\n    }\n};\nvar handleCancelAnimation = function (contextInstance) {\n    if (!contextInstance.mounted)\n        return;\n    handleCancelAnimationFrame(contextInstance.animation);\n    // Clear animation state\n    contextInstance.animate = false;\n    contextInstance.animation = null;\n    contextInstance.velocity = null;\n};\nfunction handleSetupAnimation(contextInstance, animationName, animationTime, callback) {\n    if (!contextInstance.mounted)\n        return;\n    var startTime = new Date().getTime();\n    var lastStep = 1;\n    // if another animation is active\n    handleCancelAnimation(contextInstance);\n    // new animation\n    contextInstance.animation = function () {\n        if (!contextInstance.mounted) {\n            return handleCancelAnimationFrame(contextInstance.animation);\n        }\n        var frameTime = new Date().getTime() - startTime;\n        var animationProgress = frameTime / animationTime;\n        var animationType = animations[animationName];\n        var step = animationType(animationProgress);\n        if (frameTime >= animationTime) {\n            callback(lastStep);\n            contextInstance.animation = null;\n        }\n        else if (contextInstance.animation) {\n            callback(step);\n            requestAnimationFrame(contextInstance.animation);\n        }\n    };\n    requestAnimationFrame(contextInstance.animation);\n}\nfunction isValidTargetState(targetState) {\n    var scale = targetState.scale, positionX = targetState.positionX, positionY = targetState.positionY;\n    if (Number.isNaN(scale) ||\n        Number.isNaN(positionX) ||\n        Number.isNaN(positionY)) {\n        return false;\n    }\n    return true;\n}\nfunction animate(contextInstance, targetState, animationTime, animationName) {\n    var isValid = isValidTargetState(targetState);\n    if (!contextInstance.mounted || !isValid)\n        return;\n    var setTransformState = contextInstance.setTransformState;\n    var _a = contextInstance.transformState, scale = _a.scale, positionX = _a.positionX, positionY = _a.positionY;\n    var scaleDiff = targetState.scale - scale;\n    var positionXDiff = targetState.positionX - positionX;\n    var positionYDiff = targetState.positionY - positionY;\n    if (animationTime === 0) {\n        setTransformState(targetState.scale, targetState.positionX, targetState.positionY);\n    }\n    else {\n        // animation start timestamp\n        handleSetupAnimation(contextInstance, animationName, animationTime, function (step) {\n            var newScale = scale + scaleDiff * step;\n            var newPositionX = positionX + positionXDiff * step;\n            var newPositionY = positionY + positionYDiff * step;\n            setTransformState(newScale, newPositionX, newPositionY);\n        });\n    }\n}\n\n/* eslint-disable no-param-reassign */\nfunction getComponentsSizes(wrapperComponent, contentComponent, newScale) {\n    var wrapperWidth = wrapperComponent.offsetWidth;\n    var wrapperHeight = wrapperComponent.offsetHeight;\n    var contentWidth = contentComponent.offsetWidth;\n    var contentHeight = contentComponent.offsetHeight;\n    var newContentWidth = contentWidth * newScale;\n    var newContentHeight = contentHeight * newScale;\n    var newDiffWidth = wrapperWidth - newContentWidth;\n    var newDiffHeight = wrapperHeight - newContentHeight;\n    return {\n        wrapperWidth: wrapperWidth,\n        wrapperHeight: wrapperHeight,\n        newContentWidth: newContentWidth,\n        newDiffWidth: newDiffWidth,\n        newContentHeight: newContentHeight,\n        newDiffHeight: newDiffHeight,\n    };\n}\nvar getBounds = function (wrapperWidth, newContentWidth, diffWidth, wrapperHeight, newContentHeight, diffHeight, centerZoomedOut) {\n    var scaleWidthFactor = wrapperWidth > newContentWidth\n        ? diffWidth * (centerZoomedOut ? 1 : 0.5)\n        : 0;\n    var scaleHeightFactor = wrapperHeight > newContentHeight\n        ? diffHeight * (centerZoomedOut ? 1 : 0.5)\n        : 0;\n    var minPositionX = wrapperWidth - newContentWidth - scaleWidthFactor;\n    var maxPositionX = scaleWidthFactor;\n    var minPositionY = wrapperHeight - newContentHeight - scaleHeightFactor;\n    var maxPositionY = scaleHeightFactor;\n    return { minPositionX: minPositionX, maxPositionX: maxPositionX, minPositionY: minPositionY, maxPositionY: maxPositionY };\n};\nvar calculateBounds = function (contextInstance, newScale) {\n    var wrapperComponent = contextInstance.wrapperComponent, contentComponent = contextInstance.contentComponent;\n    var centerZoomedOut = contextInstance.setup.centerZoomedOut;\n    if (!wrapperComponent || !contentComponent) {\n        throw new Error(\"Components are not mounted\");\n    }\n    var _a = getComponentsSizes(wrapperComponent, contentComponent, newScale), wrapperWidth = _a.wrapperWidth, wrapperHeight = _a.wrapperHeight, newContentWidth = _a.newContentWidth, newDiffWidth = _a.newDiffWidth, newContentHeight = _a.newContentHeight, newDiffHeight = _a.newDiffHeight;\n    var bounds = getBounds(wrapperWidth, newContentWidth, newDiffWidth, wrapperHeight, newContentHeight, newDiffHeight, Boolean(centerZoomedOut));\n    return bounds;\n};\n/**\n * Keeps value between given bounds, used for limiting view to given boundaries\n * 1# eg. boundLimiter(2, 0, 3, true) => 2\n * 2# eg. boundLimiter(4, 0, 3, true) => 3\n * 3# eg. boundLimiter(-2, 0, 3, true) => 0\n * 4# eg. boundLimiter(10, 0, 3, false) => 10\n */\nvar boundLimiter = function (value, minBound, maxBound, isActive) {\n    if (!isActive)\n        return roundNumber(value, 2);\n    if (value < minBound)\n        return roundNumber(minBound, 2);\n    if (value > maxBound)\n        return roundNumber(maxBound, 2);\n    return roundNumber(value, 2);\n};\nvar handleCalculateBounds = function (contextInstance, newScale) {\n    var bounds = calculateBounds(contextInstance, newScale);\n    // Save bounds\n    contextInstance.bounds = bounds;\n    return bounds;\n};\nfunction getMouseBoundedPosition(positionX, positionY, bounds, limitToBounds, paddingValueX, paddingValueY, wrapperComponent) {\n    var minPositionX = bounds.minPositionX, minPositionY = bounds.minPositionY, maxPositionX = bounds.maxPositionX, maxPositionY = bounds.maxPositionY;\n    var paddingX = 0;\n    var paddingY = 0;\n    if (wrapperComponent) {\n        paddingX = paddingValueX;\n        paddingY = paddingValueY;\n    }\n    var x = boundLimiter(positionX, minPositionX - paddingX, maxPositionX + paddingX, limitToBounds);\n    var y = boundLimiter(positionY, minPositionY - paddingY, maxPositionY + paddingY, limitToBounds);\n    return { x: x, y: y };\n}\n\nfunction handleCalculateZoomPositions(contextInstance, mouseX, mouseY, newScale, bounds, limitToBounds) {\n    var _a = contextInstance.transformState, scale = _a.scale, positionX = _a.positionX, positionY = _a.positionY;\n    var scaleDifference = newScale - scale;\n    if (typeof mouseX !== \"number\" || typeof mouseY !== \"number\") {\n        console.error(\"Mouse X and Y position were not provided!\");\n        return { x: positionX, y: positionY };\n    }\n    var calculatedPositionX = positionX - mouseX * scaleDifference;\n    var calculatedPositionY = positionY - mouseY * scaleDifference;\n    // do not limit to bounds when there is padding animation,\n    // it causes animation strange behaviour\n    var newPositions = getMouseBoundedPosition(calculatedPositionX, calculatedPositionY, bounds, limitToBounds, 0, 0, null);\n    return newPositions;\n}\nfunction checkZoomBounds(zoom, minScale, maxScale, zoomPadding, enablePadding) {\n    var scalePadding = enablePadding ? zoomPadding : 0;\n    var minScaleWithPadding = minScale - scalePadding;\n    if (!Number.isNaN(maxScale) && zoom >= maxScale)\n        return maxScale;\n    if (!Number.isNaN(minScale) && zoom <= minScaleWithPadding)\n        return minScaleWithPadding;\n    return zoom;\n}\n\nvar isPanningStartAllowed = function (contextInstance, event) {\n    var excluded = contextInstance.setup.panning.excluded;\n    var isInitialized = contextInstance.isInitialized, wrapperComponent = contextInstance.wrapperComponent;\n    var target = event.target;\n    var targetIsShadowDom = \"shadowRoot\" in target && \"composedPath\" in event;\n    var isWrapperChild = targetIsShadowDom\n        ? event.composedPath().some(function (el) {\n            if (!(el instanceof Element)) {\n                return false;\n            }\n            return wrapperComponent === null || wrapperComponent === void 0 ? void 0 : wrapperComponent.contains(el);\n        })\n        : wrapperComponent === null || wrapperComponent === void 0 ? void 0 : wrapperComponent.contains(target);\n    var isAllowed = isInitialized && target && isWrapperChild;\n    if (!isAllowed)\n        return false;\n    var isExcluded = isExcludedNode(target, excluded);\n    if (isExcluded)\n        return false;\n    return true;\n};\nvar isPanningAllowed = function (contextInstance) {\n    var isInitialized = contextInstance.isInitialized, isPanning = contextInstance.isPanning, setup = contextInstance.setup;\n    var disabled = setup.panning.disabled;\n    var isAllowed = isInitialized && isPanning && !disabled;\n    if (!isAllowed)\n        return false;\n    return true;\n};\nvar handlePanningSetup = function (contextInstance, event) {\n    var _a = contextInstance.transformState, positionX = _a.positionX, positionY = _a.positionY;\n    contextInstance.isPanning = true;\n    // Panning with mouse\n    var x = event.clientX;\n    var y = event.clientY;\n    contextInstance.startCoords = { x: x - positionX, y: y - positionY };\n};\nvar handleTouchPanningSetup = function (contextInstance, event) {\n    var touches = event.touches;\n    var _a = contextInstance.transformState, positionX = _a.positionX, positionY = _a.positionY;\n    contextInstance.isPanning = true;\n    // Panning with touch\n    var oneFingerTouch = touches.length === 1;\n    if (oneFingerTouch) {\n        var x = touches[0].clientX;\n        var y = touches[0].clientY;\n        contextInstance.startCoords = { x: x - positionX, y: y - positionY };\n    }\n};\nfunction handlePanToBounds(contextInstance) {\n    var _a = contextInstance.transformState, positionX = _a.positionX, positionY = _a.positionY, scale = _a.scale;\n    var _b = contextInstance.setup, disabled = _b.disabled, limitToBounds = _b.limitToBounds, centerZoomedOut = _b.centerZoomedOut;\n    var wrapperComponent = contextInstance.wrapperComponent;\n    if (disabled || !wrapperComponent || !contextInstance.bounds)\n        return;\n    var _c = contextInstance.bounds, maxPositionX = _c.maxPositionX, minPositionX = _c.minPositionX, maxPositionY = _c.maxPositionY, minPositionY = _c.minPositionY;\n    var xChanged = positionX > maxPositionX || positionX < minPositionX;\n    var yChanged = positionY > maxPositionY || positionY < minPositionY;\n    var mousePosX = positionX > maxPositionX\n        ? wrapperComponent.offsetWidth\n        : contextInstance.setup.minPositionX || 0;\n    var mousePosY = positionY > maxPositionY\n        ? wrapperComponent.offsetHeight\n        : contextInstance.setup.minPositionY || 0;\n    var _d = handleCalculateZoomPositions(contextInstance, mousePosX, mousePosY, scale, contextInstance.bounds, limitToBounds || centerZoomedOut), x = _d.x, y = _d.y;\n    return {\n        scale: scale,\n        positionX: xChanged ? x : positionX,\n        positionY: yChanged ? y : positionY,\n    };\n}\nfunction handleNewPosition(contextInstance, newPositionX, newPositionY, paddingValueX, paddingValueY) {\n    var limitToBounds = contextInstance.setup.limitToBounds;\n    var wrapperComponent = contextInstance.wrapperComponent, bounds = contextInstance.bounds;\n    var _a = contextInstance.transformState, scale = _a.scale, positionX = _a.positionX, positionY = _a.positionY;\n    if (wrapperComponent === null ||\n        bounds === null ||\n        (newPositionX === positionX && newPositionY === positionY)) {\n        return;\n    }\n    var _b = getMouseBoundedPosition(newPositionX, newPositionY, bounds, limitToBounds, paddingValueX, paddingValueY, wrapperComponent), x = _b.x, y = _b.y;\n    contextInstance.setTransformState(scale, x, y);\n}\nvar getPanningClientPosition = function (contextInstance, clientX, clientY) {\n    var startCoords = contextInstance.startCoords, transformState = contextInstance.transformState;\n    var panning = contextInstance.setup.panning;\n    var lockAxisX = panning.lockAxisX, lockAxisY = panning.lockAxisY;\n    var positionX = transformState.positionX, positionY = transformState.positionY;\n    if (!startCoords) {\n        return { x: positionX, y: positionY };\n    }\n    var mouseX = clientX - startCoords.x;\n    var mouseY = clientY - startCoords.y;\n    var newPositionX = lockAxisX ? positionX : mouseX;\n    var newPositionY = lockAxisY ? positionY : mouseY;\n    return { x: newPositionX, y: newPositionY };\n};\nvar getPaddingValue = function (contextInstance, size) {\n    var setup = contextInstance.setup, transformState = contextInstance.transformState;\n    var scale = transformState.scale;\n    var minScale = setup.minScale, disablePadding = setup.disablePadding;\n    if (size > 0 && scale >= minScale && !disablePadding) {\n        return size;\n    }\n    return 0;\n};\n\nvar isVelocityCalculationAllowed = function (contextInstance) {\n    var mounted = contextInstance.mounted;\n    var _a = contextInstance.setup, disabled = _a.disabled, velocityAnimation = _a.velocityAnimation;\n    var scale = contextInstance.transformState.scale;\n    var disabledVelocity = velocityAnimation.disabled;\n    var isAllowed = !disabledVelocity || scale > 1 || !disabled || mounted;\n    if (!isAllowed)\n        return false;\n    return true;\n};\nvar isVelocityAllowed = function (contextInstance) {\n    var mounted = contextInstance.mounted, velocity = contextInstance.velocity, bounds = contextInstance.bounds;\n    var _a = contextInstance.setup, disabled = _a.disabled, velocityAnimation = _a.velocityAnimation;\n    var scale = contextInstance.transformState.scale;\n    var disabledVelocity = velocityAnimation.disabled;\n    var isAllowed = !disabledVelocity || scale > 1 || !disabled || mounted;\n    if (!isAllowed)\n        return false;\n    if (!velocity || !bounds)\n        return false;\n    return true;\n};\nfunction getVelocityMoveTime(contextInstance, velocity) {\n    var velocityAnimation = contextInstance.setup.velocityAnimation;\n    var equalToMove = velocityAnimation.equalToMove, animationTime = velocityAnimation.animationTime, sensitivity = velocityAnimation.sensitivity;\n    if (equalToMove) {\n        return animationTime * velocity * sensitivity;\n    }\n    return animationTime;\n}\nfunction getVelocityPosition(newPosition, startPosition, currentPosition, isLocked, limitToBounds, minPosition, maxPosition, minTarget, maxTarget, step) {\n    if (limitToBounds) {\n        if (startPosition > maxPosition && currentPosition > maxPosition) {\n            var calculatedPosition = maxPosition + (newPosition - maxPosition) * step;\n            if (calculatedPosition > maxTarget)\n                return maxTarget;\n            if (calculatedPosition < maxPosition)\n                return maxPosition;\n            return calculatedPosition;\n        }\n        if (startPosition < minPosition && currentPosition < minPosition) {\n            var calculatedPosition = minPosition + (newPosition - minPosition) * step;\n            if (calculatedPosition < minTarget)\n                return minTarget;\n            if (calculatedPosition > minPosition)\n                return minPosition;\n            return calculatedPosition;\n        }\n    }\n    if (isLocked)\n        return startPosition;\n    return boundLimiter(newPosition, minPosition, maxPosition, limitToBounds);\n}\n\nfunction getSizeMultiplier(wrapperComponent, equalToMove) {\n    var defaultMultiplier = 1;\n    if (equalToMove) {\n        return Math.min(defaultMultiplier, wrapperComponent.offsetWidth / window.innerWidth);\n    }\n    return defaultMultiplier;\n}\nfunction handleCalculateVelocity(contextInstance, position) {\n    var isAllowed = isVelocityCalculationAllowed(contextInstance);\n    if (!isAllowed) {\n        return;\n    }\n    var lastMousePosition = contextInstance.lastMousePosition, velocityTime = contextInstance.velocityTime, setup = contextInstance.setup;\n    var wrapperComponent = contextInstance.wrapperComponent;\n    var equalToMove = setup.velocityAnimation.equalToMove;\n    var now = Date.now();\n    if (lastMousePosition && velocityTime && wrapperComponent) {\n        var sizeMultiplier = getSizeMultiplier(wrapperComponent, equalToMove);\n        var distanceX = position.x - lastMousePosition.x;\n        var distanceY = position.y - lastMousePosition.y;\n        var velocityX = distanceX / sizeMultiplier;\n        var velocityY = distanceY / sizeMultiplier;\n        var interval = now - velocityTime;\n        var speed = distanceX * distanceX + distanceY * distanceY;\n        var velocity = Math.sqrt(speed) / interval;\n        contextInstance.velocity = { velocityX: velocityX, velocityY: velocityY, total: velocity };\n    }\n    contextInstance.lastMousePosition = position;\n    contextInstance.velocityTime = now;\n}\nfunction handleVelocityPanning(contextInstance) {\n    var velocity = contextInstance.velocity, bounds = contextInstance.bounds, setup = contextInstance.setup, wrapperComponent = contextInstance.wrapperComponent;\n    var isAllowed = isVelocityAllowed(contextInstance);\n    if (!isAllowed || !velocity || !bounds || !wrapperComponent) {\n        return;\n    }\n    var velocityX = velocity.velocityX, velocityY = velocity.velocityY, total = velocity.total;\n    var maxPositionX = bounds.maxPositionX, minPositionX = bounds.minPositionX, maxPositionY = bounds.maxPositionY, minPositionY = bounds.minPositionY;\n    var limitToBounds = setup.limitToBounds, alignmentAnimation = setup.alignmentAnimation;\n    var zoomAnimation = setup.zoomAnimation, panning = setup.panning;\n    var lockAxisY = panning.lockAxisY, lockAxisX = panning.lockAxisX;\n    var animationType = zoomAnimation.animationType;\n    var sizeX = alignmentAnimation.sizeX, sizeY = alignmentAnimation.sizeY, velocityAlignmentTime = alignmentAnimation.velocityAlignmentTime;\n    var alignAnimationTime = velocityAlignmentTime;\n    var moveAnimationTime = getVelocityMoveTime(contextInstance, total);\n    var finalAnimationTime = Math.max(moveAnimationTime, alignAnimationTime);\n    var paddingValueX = getPaddingValue(contextInstance, sizeX);\n    var paddingValueY = getPaddingValue(contextInstance, sizeY);\n    var paddingX = (paddingValueX * wrapperComponent.offsetWidth) / 100;\n    var paddingY = (paddingValueY * wrapperComponent.offsetHeight) / 100;\n    var maxTargetX = maxPositionX + paddingX;\n    var minTargetX = minPositionX - paddingX;\n    var maxTargetY = maxPositionY + paddingY;\n    var minTargetY = minPositionY - paddingY;\n    var startState = contextInstance.transformState;\n    var startTime = new Date().getTime();\n    handleSetupAnimation(contextInstance, animationType, finalAnimationTime, function (step) {\n        var _a = contextInstance.transformState, scale = _a.scale, positionX = _a.positionX, positionY = _a.positionY;\n        var frameTime = new Date().getTime() - startTime;\n        var animationProgress = frameTime / alignAnimationTime;\n        var alignAnimation = animations[alignmentAnimation.animationType];\n        var alignStep = 1 - alignAnimation(Math.min(1, animationProgress));\n        var customStep = 1 - step;\n        var newPositionX = positionX + velocityX * customStep;\n        var newPositionY = positionY + velocityY * customStep;\n        var currentPositionX = getVelocityPosition(newPositionX, startState.positionX, positionX, lockAxisX, limitToBounds, minPositionX, maxPositionX, minTargetX, maxTargetX, alignStep);\n        var currentPositionY = getVelocityPosition(newPositionY, startState.positionY, positionY, lockAxisY, limitToBounds, minPositionY, maxPositionY, minTargetY, maxTargetY, alignStep);\n        if (positionX !== newPositionX || positionY !== newPositionY) {\n            contextInstance.setTransformState(scale, currentPositionX, currentPositionY);\n        }\n    });\n}\n\nfunction handlePanningStart(contextInstance, event) {\n    var scale = contextInstance.transformState.scale;\n    handleCancelAnimation(contextInstance);\n    handleCalculateBounds(contextInstance, scale);\n    if (window.TouchEvent !== undefined && event instanceof TouchEvent) {\n        handleTouchPanningSetup(contextInstance, event);\n    }\n    else {\n        handlePanningSetup(contextInstance, event);\n    }\n}\nfunction handleAlignToBounds(contextInstance, customAnimationTime) {\n    var scale = contextInstance.transformState.scale;\n    var _a = contextInstance.setup, minScale = _a.minScale, alignmentAnimation = _a.alignmentAnimation;\n    var disabled = alignmentAnimation.disabled, sizeX = alignmentAnimation.sizeX, sizeY = alignmentAnimation.sizeY, animationTime = alignmentAnimation.animationTime, animationType = alignmentAnimation.animationType;\n    var isDisabled = disabled || scale < minScale || (!sizeX && !sizeY);\n    if (isDisabled)\n        return;\n    var targetState = handlePanToBounds(contextInstance);\n    if (targetState) {\n        animate(contextInstance, targetState, customAnimationTime !== null && customAnimationTime !== void 0 ? customAnimationTime : animationTime, animationType);\n    }\n}\nfunction handlePanning(contextInstance, clientX, clientY) {\n    var startCoords = contextInstance.startCoords, setup = contextInstance.setup;\n    var _a = setup.alignmentAnimation, sizeX = _a.sizeX, sizeY = _a.sizeY;\n    if (!startCoords)\n        return;\n    var _b = getPanningClientPosition(contextInstance, clientX, clientY), x = _b.x, y = _b.y;\n    var paddingValueX = getPaddingValue(contextInstance, sizeX);\n    var paddingValueY = getPaddingValue(contextInstance, sizeY);\n    handleCalculateVelocity(contextInstance, { x: x, y: y });\n    handleNewPosition(contextInstance, x, y, paddingValueX, paddingValueY);\n}\nfunction handlePanningEnd(contextInstance) {\n    if (contextInstance.isPanning) {\n        var velocityDisabled = contextInstance.setup.panning.velocityDisabled;\n        var velocity = contextInstance.velocity, wrapperComponent = contextInstance.wrapperComponent, contentComponent = contextInstance.contentComponent;\n        contextInstance.isPanning = false;\n        contextInstance.animate = false;\n        contextInstance.animation = null;\n        var wrapperRect = wrapperComponent === null || wrapperComponent === void 0 ? void 0 : wrapperComponent.getBoundingClientRect();\n        var contentRect = contentComponent === null || contentComponent === void 0 ? void 0 : contentComponent.getBoundingClientRect();\n        var wrapperWidth = (wrapperRect === null || wrapperRect === void 0 ? void 0 : wrapperRect.width) || 0;\n        var wrapperHeight = (wrapperRect === null || wrapperRect === void 0 ? void 0 : wrapperRect.height) || 0;\n        var contentWidth = (contentRect === null || contentRect === void 0 ? void 0 : contentRect.width) || 0;\n        var contentHeight = (contentRect === null || contentRect === void 0 ? void 0 : contentRect.height) || 0;\n        var isZoomed = wrapperWidth < contentWidth || wrapperHeight < contentHeight;\n        var shouldAnimate = !velocityDisabled && velocity && (velocity === null || velocity === void 0 ? void 0 : velocity.total) > 0.1 && isZoomed;\n        if (shouldAnimate) {\n            handleVelocityPanning(contextInstance);\n        }\n        else {\n            handleAlignToBounds(contextInstance);\n        }\n    }\n}\n\nfunction handleZoomToPoint(contextInstance, scale, mouseX, mouseY) {\n    var _a = contextInstance.setup, minScale = _a.minScale, maxScale = _a.maxScale, limitToBounds = _a.limitToBounds;\n    var newScale = checkZoomBounds(roundNumber(scale, 2), minScale, maxScale, 0, false);\n    var bounds = handleCalculateBounds(contextInstance, newScale);\n    var _b = handleCalculateZoomPositions(contextInstance, mouseX, mouseY, newScale, bounds, limitToBounds), x = _b.x, y = _b.y;\n    return { scale: newScale, positionX: x, positionY: y };\n}\nfunction handleAlignToScaleBounds(contextInstance, mousePositionX, mousePositionY) {\n    var scale = contextInstance.transformState.scale;\n    var wrapperComponent = contextInstance.wrapperComponent;\n    var _a = contextInstance.setup, minScale = _a.minScale, limitToBounds = _a.limitToBounds, zoomAnimation = _a.zoomAnimation;\n    var disabled = zoomAnimation.disabled, animationTime = zoomAnimation.animationTime, animationType = zoomAnimation.animationType;\n    var isDisabled = disabled || scale >= minScale;\n    if (scale >= 1 || limitToBounds) {\n        // fire fit to bounds animation\n        handleAlignToBounds(contextInstance);\n    }\n    if (isDisabled || !wrapperComponent || !contextInstance.mounted)\n        return;\n    var mouseX = mousePositionX || wrapperComponent.offsetWidth / 2;\n    var mouseY = mousePositionY || wrapperComponent.offsetHeight / 2;\n    var targetState = handleZoomToPoint(contextInstance, minScale, mouseX, mouseY);\n    if (targetState) {\n        animate(contextInstance, targetState, animationTime, animationType);\n    }\n}\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\n\r\nfunction __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nfunction __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\nvar initialState = {\n    previousScale: 1,\n    scale: 1,\n    positionX: 0,\n    positionY: 0,\n};\nvar initialSetup = {\n    disabled: false,\n    minPositionX: null,\n    maxPositionX: null,\n    minPositionY: null,\n    maxPositionY: null,\n    minScale: 1,\n    maxScale: 8,\n    limitToBounds: true,\n    centerZoomedOut: false,\n    centerOnInit: false,\n    disablePadding: false,\n    smooth: true,\n    wheel: {\n        step: 0.2,\n        disabled: false,\n        smoothStep: 0.001,\n        wheelDisabled: false,\n        touchPadDisabled: false,\n        activationKeys: [],\n        excluded: [],\n    },\n    panning: {\n        disabled: false,\n        velocityDisabled: false,\n        lockAxisX: false,\n        lockAxisY: false,\n        allowLeftClickPan: true,\n        allowMiddleClickPan: true,\n        allowRightClickPan: true,\n        wheelPanning: false,\n        activationKeys: [],\n        excluded: [],\n    },\n    pinch: {\n        step: 5,\n        disabled: false,\n        excluded: [],\n    },\n    doubleClick: {\n        disabled: false,\n        step: 0.7,\n        mode: \"zoomIn\",\n        animationType: \"easeOut\",\n        animationTime: 200,\n        excluded: [],\n    },\n    zoomAnimation: {\n        disabled: false,\n        size: 0.4,\n        animationTime: 200,\n        animationType: \"easeOut\",\n    },\n    alignmentAnimation: {\n        disabled: false,\n        sizeX: 100,\n        sizeY: 100,\n        animationTime: 200,\n        velocityAlignmentTime: 400,\n        animationType: \"easeOut\",\n    },\n    velocityAnimation: {\n        disabled: false,\n        sensitivity: 1,\n        animationTime: 400,\n        animationType: \"easeOut\",\n        equalToMove: true,\n    },\n};\nvar baseClasses = {\n    wrapperClass: \"react-transform-wrapper\",\n    contentClass: \"react-transform-component\",\n};\n\nvar createState = function (props) {\n    var _a, _b, _c, _d;\n    return {\n        previousScale: (_a = props.initialScale) !== null && _a !== void 0 ? _a : initialState.scale,\n        scale: (_b = props.initialScale) !== null && _b !== void 0 ? _b : initialState.scale,\n        positionX: (_c = props.initialPositionX) !== null && _c !== void 0 ? _c : initialState.positionX,\n        positionY: (_d = props.initialPositionY) !== null && _d !== void 0 ? _d : initialState.positionY,\n    };\n};\nvar createSetup = function (props) {\n    var newSetup = __assign({}, initialSetup);\n    Object.keys(props).forEach(function (key) {\n        var validValue = typeof props[key] !== \"undefined\";\n        var validParameter = typeof initialSetup[key] !== \"undefined\";\n        if (validParameter && validValue) {\n            var dataType = Object.prototype.toString.call(initialSetup[key]);\n            var isObject = dataType === \"[object Object]\";\n            var isArray = dataType === \"[object Array]\";\n            if (isObject) {\n                newSetup[key] = __assign(__assign({}, initialSetup[key]), props[key]);\n            }\n            else if (isArray) {\n                newSetup[key] = __spreadArray(__spreadArray([], initialSetup[key], true), props[key], true);\n            }\n            else {\n                newSetup[key] = props[key];\n            }\n        }\n    });\n    return newSetup;\n};\n\nvar handleCalculateButtonZoom = function (contextInstance, delta, step) {\n    var scale = contextInstance.transformState.scale;\n    var wrapperComponent = contextInstance.wrapperComponent, setup = contextInstance.setup;\n    var maxScale = setup.maxScale, minScale = setup.minScale, zoomAnimation = setup.zoomAnimation, smooth = setup.smooth;\n    var size = zoomAnimation.size;\n    if (!wrapperComponent) {\n        throw new Error(\"Wrapper is not mounted\");\n    }\n    var targetScale = smooth\n        ? scale * Math.exp(delta * step)\n        : scale + delta * step;\n    var newScale = checkZoomBounds(roundNumber(targetScale, 3), minScale, maxScale, size, false);\n    return newScale;\n};\nfunction handleZoomToViewCenter(contextInstance, delta, step, animationTime, animationType) {\n    var wrapperComponent = contextInstance.wrapperComponent;\n    var _a = contextInstance.transformState, scale = _a.scale, positionX = _a.positionX, positionY = _a.positionY;\n    if (!wrapperComponent)\n        return console.error(\"No WrapperComponent found\");\n    var wrapperWidth = wrapperComponent.offsetWidth;\n    var wrapperHeight = wrapperComponent.offsetHeight;\n    var mouseX = (wrapperWidth / 2 - positionX) / scale;\n    var mouseY = (wrapperHeight / 2 - positionY) / scale;\n    var newScale = handleCalculateButtonZoom(contextInstance, delta, step);\n    var targetState = handleZoomToPoint(contextInstance, newScale, mouseX, mouseY);\n    if (!targetState) {\n        return console.error(\"Error during zoom event. New transformation state was not calculated.\");\n    }\n    animate(contextInstance, targetState, animationTime, animationType);\n}\nfunction resetTransformations(contextInstance, animationTime, animationType, onResetTransformation) {\n    var setup = contextInstance.setup, wrapperComponent = contextInstance.wrapperComponent;\n    var limitToBounds = setup.limitToBounds;\n    var initialTransformation = createState(contextInstance.props);\n    var _a = contextInstance.transformState, scale = _a.scale, positionX = _a.positionX, positionY = _a.positionY;\n    if (!wrapperComponent)\n        return;\n    var newBounds = calculateBounds(contextInstance, initialTransformation.scale);\n    var boundedPositions = getMouseBoundedPosition(initialTransformation.positionX, initialTransformation.positionY, newBounds, limitToBounds, 0, 0, wrapperComponent);\n    var newState = {\n        scale: initialTransformation.scale,\n        positionX: boundedPositions.x,\n        positionY: boundedPositions.y,\n    };\n    if (scale === initialTransformation.scale &&\n        positionX === initialTransformation.positionX &&\n        positionY === initialTransformation.positionY) {\n        return;\n    }\n    onResetTransformation === null || onResetTransformation === void 0 ? void 0 : onResetTransformation();\n    animate(contextInstance, newState, animationTime, animationType);\n}\nfunction getOffset(element, wrapper, content, state) {\n    var offset = element.getBoundingClientRect();\n    var wrapperOffset = wrapper.getBoundingClientRect();\n    var contentOffset = content.getBoundingClientRect();\n    var xOff = wrapperOffset.x * state.scale;\n    var yOff = wrapperOffset.y * state.scale;\n    return {\n        x: (offset.x - contentOffset.x + xOff) / state.scale,\n        y: (offset.y - contentOffset.y + yOff) / state.scale,\n    };\n}\nfunction calculateZoomToNode(contextInstance, node, customZoom) {\n    var wrapperComponent = contextInstance.wrapperComponent, contentComponent = contextInstance.contentComponent, transformState = contextInstance.transformState;\n    var _a = contextInstance.setup, limitToBounds = _a.limitToBounds, minScale = _a.minScale, maxScale = _a.maxScale;\n    if (!wrapperComponent || !contentComponent)\n        return transformState;\n    var wrapperRect = wrapperComponent.getBoundingClientRect();\n    var nodeRect = node.getBoundingClientRect();\n    var nodeOffset = getOffset(node, wrapperComponent, contentComponent, transformState);\n    var nodeLeft = nodeOffset.x;\n    var nodeTop = nodeOffset.y;\n    var nodeWidth = nodeRect.width / transformState.scale;\n    var nodeHeight = nodeRect.height / transformState.scale;\n    var scaleX = wrapperComponent.offsetWidth / nodeWidth;\n    var scaleY = wrapperComponent.offsetHeight / nodeHeight;\n    var newScale = checkZoomBounds(customZoom || Math.min(scaleX, scaleY), minScale, maxScale, 0, false);\n    var offsetX = (wrapperRect.width - nodeWidth * newScale) / 2;\n    var offsetY = (wrapperRect.height - nodeHeight * newScale) / 2;\n    var newPositionX = (wrapperRect.left - nodeLeft) * newScale + offsetX;\n    var newPositionY = (wrapperRect.top - nodeTop) * newScale + offsetY;\n    var bounds = calculateBounds(contextInstance, newScale);\n    var _b = getMouseBoundedPosition(newPositionX, newPositionY, bounds, limitToBounds, 0, 0, wrapperComponent), x = _b.x, y = _b.y;\n    return { positionX: x, positionY: y, scale: newScale };\n}\n\nvar zoomIn = function (contextInstance) {\n    return function (step, animationTime, animationType) {\n        if (step === void 0) { step = 0.5; }\n        if (animationTime === void 0) { animationTime = 300; }\n        if (animationType === void 0) { animationType = \"easeOut\"; }\n        handleZoomToViewCenter(contextInstance, 1, step, animationTime, animationType);\n    };\n};\nvar zoomOut = function (contextInstance) {\n    return function (step, animationTime, animationType) {\n        if (step === void 0) { step = 0.5; }\n        if (animationTime === void 0) { animationTime = 300; }\n        if (animationType === void 0) { animationType = \"easeOut\"; }\n        handleZoomToViewCenter(contextInstance, -1, step, animationTime, animationType);\n    };\n};\nvar setTransform = function (contextInstance) {\n    return function (newPositionX, newPositionY, newScale, animationTime, animationType) {\n        if (animationTime === void 0) { animationTime = 300; }\n        if (animationType === void 0) { animationType = \"easeOut\"; }\n        var _a = contextInstance.transformState, positionX = _a.positionX, positionY = _a.positionY, scale = _a.scale;\n        var wrapperComponent = contextInstance.wrapperComponent, contentComponent = contextInstance.contentComponent;\n        var disabled = contextInstance.setup.disabled;\n        if (disabled || !wrapperComponent || !contentComponent)\n            return;\n        var targetState = {\n            positionX: Number.isNaN(newPositionX) ? positionX : newPositionX,\n            positionY: Number.isNaN(newPositionY) ? positionY : newPositionY,\n            scale: Number.isNaN(newScale) ? scale : newScale,\n        };\n        animate(contextInstance, targetState, animationTime, animationType);\n    };\n};\nvar resetTransform = function (contextInstance) {\n    return function (animationTime, animationType) {\n        if (animationTime === void 0) { animationTime = 200; }\n        if (animationType === void 0) { animationType = \"easeOut\"; }\n        resetTransformations(contextInstance, animationTime, animationType);\n    };\n};\nvar centerView = function (contextInstance) {\n    return function (scale, animationTime, animationType) {\n        if (animationTime === void 0) { animationTime = 200; }\n        if (animationType === void 0) { animationType = \"easeOut\"; }\n        var transformState = contextInstance.transformState, wrapperComponent = contextInstance.wrapperComponent, contentComponent = contextInstance.contentComponent;\n        if (wrapperComponent && contentComponent) {\n            var targetState = getCenterPosition(scale || transformState.scale, wrapperComponent, contentComponent);\n            animate(contextInstance, targetState, animationTime, animationType);\n        }\n    };\n};\nvar zoomToElement = function (contextInstance) {\n    return function (node, scale, animationTime, animationType) {\n        if (animationTime === void 0) { animationTime = 600; }\n        if (animationType === void 0) { animationType = \"easeOut\"; }\n        handleCancelAnimation(contextInstance);\n        var wrapperComponent = contextInstance.wrapperComponent;\n        var target = typeof node === \"string\" ? document.getElementById(node) : node;\n        if (wrapperComponent && target && wrapperComponent.contains(target)) {\n            var targetState = calculateZoomToNode(contextInstance, target, scale);\n            animate(contextInstance, targetState, animationTime, animationType);\n        }\n    };\n};\n\nvar getControls = function (contextInstance) {\n    return {\n        instance: contextInstance,\n        zoomIn: zoomIn(contextInstance),\n        zoomOut: zoomOut(contextInstance),\n        setTransform: setTransform(contextInstance),\n        resetTransform: resetTransform(contextInstance),\n        centerView: centerView(contextInstance),\n        zoomToElement: zoomToElement(contextInstance),\n    };\n};\nvar getState = function (contextInstance) {\n    return {\n        instance: contextInstance,\n        state: contextInstance.transformState,\n    };\n};\nvar getContext = function (contextInstance) {\n    var ref = {};\n    Object.assign(ref, getState(contextInstance));\n    Object.assign(ref, getControls(contextInstance));\n    return ref;\n};\n\n// We want to make event listeners non-passive, and to do so have to check\n// that browsers support EventListenerOptions in the first place.\n// https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#Safely_detecting_option_support\nvar passiveSupported = false;\nfunction makePassiveEventOption() {\n    try {\n        var options = {\n            get passive() {\n                // This function will be called when the browser\n                //   attempts to access the passive property.\n                passiveSupported = true;\n                return false;\n            },\n        };\n        return options;\n    }\n    catch (err) {\n        passiveSupported = false;\n        return passiveSupported;\n    }\n}\n\nvar matchPrefix = \".\".concat(baseClasses.wrapperClass);\nvar isExcludedNode = function (node, excluded) {\n    return excluded.some(function (exclude) {\n        return node.matches(\"\".concat(matchPrefix, \" \").concat(exclude, \", \").concat(matchPrefix, \" .\").concat(exclude, \", \").concat(matchPrefix, \" \").concat(exclude, \" *, \").concat(matchPrefix, \" .\").concat(exclude, \" *\"));\n    });\n};\nvar cancelTimeout = function (timeout) {\n    if (timeout) {\n        clearTimeout(timeout);\n    }\n};\n\nvar getTransformStyles = function (x, y, scale) {\n    // Standard translate prevents blurry svg on the safari\n    return \"translate(\".concat(x, \"px, \").concat(y, \"px) scale(\").concat(scale, \")\");\n};\nvar getMatrixTransformStyles = function (x, y, scale) {\n    // The shorthand for matrix does not work for Safari hence the need to explicitly use matrix3d\n    // Refer to https://developer.mozilla.org/en-US/docs/Web/CSS/transform-function/matrix\n    var a = scale;\n    var b = 0;\n    var c = 0;\n    var d = scale;\n    var tx = x;\n    var ty = y;\n    return \"matrix3d(\".concat(a, \", \").concat(b, \", 0, 0, \").concat(c, \", \").concat(d, \", 0, 0, 0, 0, 1, 0, \").concat(tx, \", \").concat(ty, \", 0, 1)\");\n};\nvar getCenterPosition = function (scale, wrapperComponent, contentComponent) {\n    var contentWidth = contentComponent.offsetWidth * scale;\n    var contentHeight = contentComponent.offsetHeight * scale;\n    var centerPositionX = (wrapperComponent.offsetWidth - contentWidth) / 2;\n    var centerPositionY = (wrapperComponent.offsetHeight - contentHeight) / 2;\n    return {\n        scale: scale,\n        positionX: centerPositionX,\n        positionY: centerPositionY,\n    };\n};\n\nfunction mergeRefs(refs) {\n    return function (value) {\n        refs.forEach(function (ref) {\n            if (typeof ref === \"function\") {\n                ref(value);\n            }\n            else if (ref != null) {\n                ref.current = value;\n            }\n        });\n    };\n}\n\nvar isWheelAllowed = function (contextInstance, event) {\n    var _a = contextInstance.setup.wheel, disabled = _a.disabled, wheelDisabled = _a.wheelDisabled, touchPadDisabled = _a.touchPadDisabled, excluded = _a.excluded;\n    var isInitialized = contextInstance.isInitialized, isPanning = contextInstance.isPanning;\n    var target = event.target;\n    var isAllowed = isInitialized && !isPanning && !disabled && target;\n    if (!isAllowed)\n        return false;\n    // Event ctrlKey detects if touchpad action is executing wheel or pinch gesture\n    if (wheelDisabled && !event.ctrlKey)\n        return false;\n    if (touchPadDisabled && event.ctrlKey)\n        return false;\n    var isExcluded = isExcludedNode(target, excluded);\n    if (isExcluded)\n        return false;\n    return true;\n};\nvar getDeltaY = function (event) {\n    if (event) {\n        return event.deltaY < 0 ? 1 : -1;\n    }\n    return 0;\n};\nfunction getDelta(event, customDelta) {\n    var deltaY = getDeltaY(event);\n    var delta = checkIsNumber(customDelta, deltaY);\n    return delta;\n}\nfunction getMousePosition(event, contentComponent, scale) {\n    var contentRect = contentComponent.getBoundingClientRect();\n    var mouseX = 0;\n    var mouseY = 0;\n    if (\"clientX\" in event) {\n        // mouse position x, y over wrapper component\n        mouseX = (event.clientX - contentRect.left) / scale;\n        mouseY = (event.clientY - contentRect.top) / scale;\n    }\n    else {\n        var touch = event.touches[0];\n        mouseX = (touch.clientX - contentRect.left) / scale;\n        mouseY = (touch.clientY - contentRect.top) / scale;\n    }\n    if (Number.isNaN(mouseX) || Number.isNaN(mouseY))\n        console.error(\"No mouse or touch offset found\");\n    return {\n        x: mouseX,\n        y: mouseY,\n    };\n}\nvar handleCalculateWheelZoom = function (contextInstance, delta, step, disable, getTarget) {\n    var scale = contextInstance.transformState.scale;\n    var wrapperComponent = contextInstance.wrapperComponent, setup = contextInstance.setup;\n    var maxScale = setup.maxScale, minScale = setup.minScale, zoomAnimation = setup.zoomAnimation, disablePadding = setup.disablePadding;\n    var size = zoomAnimation.size, disabled = zoomAnimation.disabled;\n    if (!wrapperComponent) {\n        throw new Error(\"Wrapper is not mounted\");\n    }\n    var targetScale = scale + delta * step;\n    if (getTarget)\n        return targetScale;\n    var paddingEnabled = disable ? false : !disabled;\n    var newScale = checkZoomBounds(roundNumber(targetScale, 3), minScale, maxScale, size, paddingEnabled && !disablePadding);\n    return newScale;\n};\nvar handleWheelZoomStop = function (contextInstance, event) {\n    var previousWheelEvent = contextInstance.previousWheelEvent;\n    var scale = contextInstance.transformState.scale;\n    var _a = contextInstance.setup, maxScale = _a.maxScale, minScale = _a.minScale;\n    if (!previousWheelEvent)\n        return false;\n    if (scale < maxScale || scale > minScale)\n        return true;\n    if (Math.sign(previousWheelEvent.deltaY) !== Math.sign(event.deltaY))\n        return true;\n    if (previousWheelEvent.deltaY > 0 && previousWheelEvent.deltaY < event.deltaY)\n        return true;\n    if (previousWheelEvent.deltaY < 0 && previousWheelEvent.deltaY > event.deltaY)\n        return true;\n    if (Math.sign(previousWheelEvent.deltaY) !== Math.sign(event.deltaY))\n        return true;\n    return false;\n};\n\nvar isPinchStartAllowed = function (contextInstance, event) {\n    var _a = contextInstance.setup.pinch, disabled = _a.disabled, excluded = _a.excluded;\n    var isInitialized = contextInstance.isInitialized;\n    var target = event.target;\n    var isAllowed = isInitialized && !disabled && target;\n    if (!isAllowed)\n        return false;\n    var isExcluded = isExcludedNode(target, excluded);\n    if (isExcluded)\n        return false;\n    return true;\n};\nvar isPinchAllowed = function (contextInstance) {\n    var disabled = contextInstance.setup.pinch.disabled;\n    var isInitialized = contextInstance.isInitialized, pinchStartDistance = contextInstance.pinchStartDistance;\n    var isAllowed = isInitialized && !disabled && pinchStartDistance;\n    if (!isAllowed)\n        return false;\n    return true;\n};\nvar calculateTouchMidPoint = function (event, scale, contentComponent) {\n    var contentRect = contentComponent.getBoundingClientRect();\n    var touches = event.touches;\n    var firstPointX = roundNumber(touches[0].clientX - contentRect.left, 5);\n    var firstPointY = roundNumber(touches[0].clientY - contentRect.top, 5);\n    var secondPointX = roundNumber(touches[1].clientX - contentRect.left, 5);\n    var secondPointY = roundNumber(touches[1].clientY - contentRect.top, 5);\n    return {\n        x: (firstPointX + secondPointX) / 2 / scale,\n        y: (firstPointY + secondPointY) / 2 / scale,\n    };\n};\nvar getTouchDistance = function (event) {\n    return Math.sqrt(Math.pow((event.touches[0].pageX - event.touches[1].pageX), 2) +\n        Math.pow((event.touches[0].pageY - event.touches[1].pageY), 2));\n};\nvar calculatePinchZoom = function (contextInstance, currentDistance) {\n    var pinchStartScale = contextInstance.pinchStartScale, pinchStartDistance = contextInstance.pinchStartDistance, setup = contextInstance.setup;\n    var maxScale = setup.maxScale, minScale = setup.minScale, zoomAnimation = setup.zoomAnimation, disablePadding = setup.disablePadding;\n    var size = zoomAnimation.size, disabled = zoomAnimation.disabled;\n    if (!pinchStartScale || pinchStartDistance === null || !currentDistance) {\n        throw new Error(\"Pinch touches distance was not provided\");\n    }\n    if (currentDistance < 0) {\n        return contextInstance.transformState.scale;\n    }\n    var touchProportion = currentDistance / pinchStartDistance;\n    var scaleDifference = touchProportion * pinchStartScale;\n    return checkZoomBounds(roundNumber(scaleDifference, 2), minScale, maxScale, size, !disabled && !disablePadding);\n};\n\nvar wheelStopEventTime = 160;\nvar wheelAnimationTime = 100;\nvar handleWheelStart = function (contextInstance, event) {\n    var _a = contextInstance.props, onWheelStart = _a.onWheelStart, onZoomStart = _a.onZoomStart;\n    if (!contextInstance.wheelStopEventTimer) {\n        handleCancelAnimation(contextInstance);\n        handleCallback(getContext(contextInstance), event, onWheelStart);\n        handleCallback(getContext(contextInstance), event, onZoomStart);\n    }\n};\nvar handleWheelZoom = function (contextInstance, event) {\n    var _a = contextInstance.props, onWheel = _a.onWheel, onZoom = _a.onZoom;\n    var contentComponent = contextInstance.contentComponent, setup = contextInstance.setup, transformState = contextInstance.transformState;\n    var scale = transformState.scale;\n    var limitToBounds = setup.limitToBounds, centerZoomedOut = setup.centerZoomedOut, zoomAnimation = setup.zoomAnimation, wheel = setup.wheel, disablePadding = setup.disablePadding, smooth = setup.smooth;\n    var size = zoomAnimation.size, disabled = zoomAnimation.disabled;\n    var step = wheel.step, smoothStep = wheel.smoothStep;\n    if (!contentComponent) {\n        throw new Error(\"Component not mounted\");\n    }\n    event.preventDefault();\n    event.stopPropagation();\n    var delta = getDelta(event, null);\n    var zoomStep = smooth ? smoothStep * Math.abs(event.deltaY) : step;\n    var newScale = handleCalculateWheelZoom(contextInstance, delta, zoomStep, !event.ctrlKey);\n    // if scale not change\n    if (scale === newScale)\n        return;\n    var bounds = handleCalculateBounds(contextInstance, newScale);\n    var mousePosition = getMousePosition(event, contentComponent, scale);\n    var isPaddingDisabled = disabled || size === 0 || centerZoomedOut || disablePadding;\n    var isLimitedToBounds = limitToBounds && isPaddingDisabled;\n    var _b = handleCalculateZoomPositions(contextInstance, mousePosition.x, mousePosition.y, newScale, bounds, isLimitedToBounds), x = _b.x, y = _b.y;\n    contextInstance.previousWheelEvent = event;\n    contextInstance.setTransformState(newScale, x, y);\n    handleCallback(getContext(contextInstance), event, onWheel);\n    handleCallback(getContext(contextInstance), event, onZoom);\n};\nvar handleWheelStop = function (contextInstance, event) {\n    var _a = contextInstance.props, onWheelStop = _a.onWheelStop, onZoomStop = _a.onZoomStop;\n    // fire animation\n    cancelTimeout(contextInstance.wheelAnimationTimer);\n    contextInstance.wheelAnimationTimer = setTimeout(function () {\n        if (!contextInstance.mounted)\n            return;\n        handleAlignToScaleBounds(contextInstance, event.x, event.y);\n        contextInstance.wheelAnimationTimer = null;\n    }, wheelAnimationTime);\n    // Wheel stop event\n    var hasStoppedZooming = handleWheelZoomStop(contextInstance, event);\n    if (hasStoppedZooming) {\n        cancelTimeout(contextInstance.wheelStopEventTimer);\n        contextInstance.wheelStopEventTimer = setTimeout(function () {\n            if (!contextInstance.mounted)\n                return;\n            contextInstance.wheelStopEventTimer = null;\n            handleCallback(getContext(contextInstance), event, onWheelStop);\n            handleCallback(getContext(contextInstance), event, onZoomStop);\n        }, wheelStopEventTime);\n    }\n};\n\nvar getTouchCenter = function (event) {\n    var totalX = 0;\n    var totalY = 0;\n    // Sum up the positions of all touches\n    for (var i = 0; i < 2; i += 1) {\n        totalX += event.touches[i].clientX;\n        totalY += event.touches[i].clientY;\n    }\n    // Calculate the average position\n    var x = totalX / 2;\n    var y = totalY / 2;\n    return { x: x, y: y };\n};\nvar handlePinchStart = function (contextInstance, event) {\n    var distance = getTouchDistance(event);\n    contextInstance.pinchStartDistance = distance;\n    contextInstance.lastDistance = distance;\n    contextInstance.pinchStartScale = contextInstance.transformState.scale;\n    contextInstance.isPanning = false;\n    var center = getTouchCenter(event);\n    contextInstance.pinchLastCenterX = center.x;\n    contextInstance.pinchLastCenterY = center.y;\n    handleCancelAnimation(contextInstance);\n};\nvar handlePinchZoom = function (contextInstance, event) {\n    var contentComponent = contextInstance.contentComponent, pinchStartDistance = contextInstance.pinchStartDistance, wrapperComponent = contextInstance.wrapperComponent;\n    var scale = contextInstance.transformState.scale;\n    var _a = contextInstance.setup, limitToBounds = _a.limitToBounds, centerZoomedOut = _a.centerZoomedOut, zoomAnimation = _a.zoomAnimation, alignmentAnimation = _a.alignmentAnimation;\n    var disabled = zoomAnimation.disabled, size = zoomAnimation.size;\n    // if one finger starts from outside of wrapper\n    if (pinchStartDistance === null || !contentComponent)\n        return;\n    var midPoint = calculateTouchMidPoint(event, scale, contentComponent);\n    // if touches goes off of the wrapper element\n    if (!Number.isFinite(midPoint.x) || !Number.isFinite(midPoint.y))\n        return;\n    var currentDistance = getTouchDistance(event);\n    var newScale = calculatePinchZoom(contextInstance, currentDistance);\n    var center = getTouchCenter(event);\n    // pan should be scale invariant.\n    var panX = center.x - (contextInstance.pinchLastCenterX || 0);\n    var panY = center.y - (contextInstance.pinchLastCenterY || 0);\n    if (newScale === scale && panX === 0 && panY === 0)\n        return;\n    contextInstance.pinchLastCenterX = center.x;\n    contextInstance.pinchLastCenterY = center.y;\n    var bounds = handleCalculateBounds(contextInstance, newScale);\n    var isPaddingDisabled = disabled || size === 0 || centerZoomedOut;\n    var isLimitedToBounds = limitToBounds && isPaddingDisabled;\n    var _b = handleCalculateZoomPositions(contextInstance, midPoint.x, midPoint.y, newScale, bounds, isLimitedToBounds), x = _b.x, y = _b.y;\n    contextInstance.pinchMidpoint = midPoint;\n    contextInstance.lastDistance = currentDistance;\n    var sizeX = alignmentAnimation.sizeX, sizeY = alignmentAnimation.sizeY;\n    var paddingValueX = getPaddingValue(contextInstance, sizeX);\n    var paddingValueY = getPaddingValue(contextInstance, sizeY);\n    var newPositionX = x + panX;\n    var newPositionY = y + panY;\n    var _c = getMouseBoundedPosition(newPositionX, newPositionY, bounds, limitToBounds, paddingValueX, paddingValueY, wrapperComponent), finalX = _c.x, finalY = _c.y;\n    contextInstance.setTransformState(newScale, finalX, finalY);\n};\nvar handlePinchStop = function (contextInstance) {\n    var pinchMidpoint = contextInstance.pinchMidpoint;\n    contextInstance.velocity = null;\n    contextInstance.lastDistance = null;\n    contextInstance.pinchMidpoint = null;\n    contextInstance.pinchStartScale = null;\n    contextInstance.pinchStartDistance = null;\n    handleAlignToScaleBounds(contextInstance, pinchMidpoint === null || pinchMidpoint === void 0 ? void 0 : pinchMidpoint.x, pinchMidpoint === null || pinchMidpoint === void 0 ? void 0 : pinchMidpoint.y);\n};\n\nvar handleDoubleClickStop = function (contextInstance, event) {\n    var onZoomStop = contextInstance.props.onZoomStop;\n    var animationTime = contextInstance.setup.doubleClick.animationTime;\n    cancelTimeout(contextInstance.doubleClickStopEventTimer);\n    contextInstance.doubleClickStopEventTimer = setTimeout(function () {\n        contextInstance.doubleClickStopEventTimer = null;\n        handleCallback(getContext(contextInstance), event, onZoomStop);\n    }, animationTime);\n};\nvar handleDoubleClickResetMode = function (contextInstance, event) {\n    var _a = contextInstance.props, onZoomStart = _a.onZoomStart, onZoom = _a.onZoom;\n    var _b = contextInstance.setup.doubleClick, animationTime = _b.animationTime, animationType = _b.animationType;\n    handleCallback(getContext(contextInstance), event, onZoomStart);\n    resetTransformations(contextInstance, animationTime, animationType, function () {\n        return handleCallback(getContext(contextInstance), event, onZoom);\n    });\n    handleDoubleClickStop(contextInstance, event);\n};\nfunction getDoubleClickScale(mode, scale) {\n    if (mode === \"toggle\") {\n        return scale === 1 ? 1 : -1;\n    }\n    return mode === \"zoomOut\" ? -1 : 1;\n}\nfunction handleDoubleClick(contextInstance, event) {\n    var setup = contextInstance.setup, doubleClickStopEventTimer = contextInstance.doubleClickStopEventTimer, transformState = contextInstance.transformState, contentComponent = contextInstance.contentComponent;\n    var scale = transformState.scale;\n    var _a = contextInstance.props, onZoomStart = _a.onZoomStart, onZoom = _a.onZoom;\n    var _b = setup.doubleClick, disabled = _b.disabled, mode = _b.mode, step = _b.step, animationTime = _b.animationTime, animationType = _b.animationType;\n    if (disabled)\n        return;\n    if (doubleClickStopEventTimer)\n        return;\n    if (mode === \"reset\") {\n        return handleDoubleClickResetMode(contextInstance, event);\n    }\n    if (!contentComponent)\n        return console.error(\"No ContentComponent found\");\n    var delta = getDoubleClickScale(mode, contextInstance.transformState.scale);\n    var newScale = handleCalculateButtonZoom(contextInstance, delta, step);\n    // stop execution when scale didn't change\n    if (scale === newScale)\n        return;\n    handleCallback(getContext(contextInstance), event, onZoomStart);\n    var mousePosition = getMousePosition(event, contentComponent, scale);\n    var targetState = handleZoomToPoint(contextInstance, newScale, mousePosition.x, mousePosition.y);\n    if (!targetState) {\n        return console.error(\"Error during zoom event. New transformation state was not calculated.\");\n    }\n    handleCallback(getContext(contextInstance), event, onZoom);\n    animate(contextInstance, targetState, animationTime, animationType);\n    handleDoubleClickStop(contextInstance, event);\n}\nvar isDoubleClickAllowed = function (contextInstance, event) {\n    var isInitialized = contextInstance.isInitialized, setup = contextInstance.setup, wrapperComponent = contextInstance.wrapperComponent;\n    var _a = setup.doubleClick, disabled = _a.disabled, excluded = _a.excluded;\n    var target = event.target;\n    var isWrapperChild = wrapperComponent === null || wrapperComponent === void 0 ? void 0 : wrapperComponent.contains(target);\n    var isAllowed = isInitialized && target && isWrapperChild && !disabled;\n    if (!isAllowed)\n        return false;\n    var isExcluded = isExcludedNode(target, excluded);\n    if (isExcluded)\n        return false;\n    return true;\n};\n\nvar ZoomPanPinch = /** @class */ (function () {\n    function ZoomPanPinch(props) {\n        var _this = this;\n        this.mounted = true;\n        this.pinchLastCenterX = null;\n        this.pinchLastCenterY = null;\n        this.onChangeCallbacks = new Set();\n        this.onInitCallbacks = new Set();\n        // Components\n        this.wrapperComponent = null;\n        this.contentComponent = null;\n        // Initialization\n        this.isInitialized = false;\n        this.bounds = null;\n        // wheel helpers\n        this.previousWheelEvent = null;\n        this.wheelStopEventTimer = null;\n        this.wheelAnimationTimer = null;\n        // panning helpers\n        this.isPanning = false;\n        this.isWheelPanning = false;\n        this.startCoords = null;\n        this.lastTouch = null;\n        // pinch helpers\n        this.distance = null;\n        this.lastDistance = null;\n        this.pinchStartDistance = null;\n        this.pinchStartScale = null;\n        this.pinchMidpoint = null;\n        // double click helpers\n        this.doubleClickStopEventTimer = null;\n        // velocity helpers\n        this.velocity = null;\n        this.velocityTime = null;\n        this.lastMousePosition = null;\n        // animations helpers\n        this.animate = false;\n        this.animation = null;\n        this.maxBounds = null;\n        // key press\n        this.pressedKeys = {};\n        this.mount = function () {\n            _this.initializeWindowEvents();\n        };\n        this.unmount = function () {\n            _this.cleanupWindowEvents();\n        };\n        this.update = function (newProps) {\n            _this.props = newProps;\n            handleCalculateBounds(_this, _this.transformState.scale);\n            _this.setup = createSetup(newProps);\n        };\n        this.initializeWindowEvents = function () {\n            var _a, _b;\n            var passive = makePassiveEventOption();\n            var currentDocument = (_a = _this.wrapperComponent) === null || _a === void 0 ? void 0 : _a.ownerDocument;\n            var currentWindow = currentDocument === null || currentDocument === void 0 ? void 0 : currentDocument.defaultView;\n            (_b = _this.wrapperComponent) === null || _b === void 0 ? void 0 : _b.addEventListener(\"wheel\", _this.onWheelPanning, passive);\n            // Panning on window to allow panning when mouse is out of component wrapper\n            currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.addEventListener(\"mousedown\", _this.onPanningStart, passive);\n            currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.addEventListener(\"mousemove\", _this.onPanning, passive);\n            currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.addEventListener(\"mouseup\", _this.onPanningStop, passive);\n            currentDocument === null || currentDocument === void 0 ? void 0 : currentDocument.addEventListener(\"mouseleave\", _this.clearPanning, passive);\n            currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.addEventListener(\"keyup\", _this.setKeyUnPressed, passive);\n            currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.addEventListener(\"keydown\", _this.setKeyPressed, passive);\n        };\n        this.cleanupWindowEvents = function () {\n            var _a, _b;\n            var passive = makePassiveEventOption();\n            var currentDocument = (_a = _this.wrapperComponent) === null || _a === void 0 ? void 0 : _a.ownerDocument;\n            var currentWindow = currentDocument === null || currentDocument === void 0 ? void 0 : currentDocument.defaultView;\n            currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.removeEventListener(\"mousedown\", _this.onPanningStart, passive);\n            currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.removeEventListener(\"mousemove\", _this.onPanning, passive);\n            currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.removeEventListener(\"mouseup\", _this.onPanningStop, passive);\n            currentDocument === null || currentDocument === void 0 ? void 0 : currentDocument.removeEventListener(\"mouseleave\", _this.clearPanning, passive);\n            currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.removeEventListener(\"keyup\", _this.setKeyUnPressed, passive);\n            currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.removeEventListener(\"keydown\", _this.setKeyPressed, passive);\n            document.removeEventListener(\"mouseleave\", _this.clearPanning, passive);\n            handleCancelAnimation(_this);\n            (_b = _this.observer) === null || _b === void 0 ? void 0 : _b.disconnect();\n        };\n        this.handleInitializeWrapperEvents = function (wrapper) {\n            // Zooming events on wrapper\n            var passive = makePassiveEventOption();\n            wrapper.addEventListener(\"wheel\", _this.onWheelZoom, passive);\n            wrapper.addEventListener(\"dblclick\", _this.onDoubleClick, passive);\n            wrapper.addEventListener(\"touchstart\", _this.onTouchPanningStart, passive);\n            wrapper.addEventListener(\"touchmove\", _this.onTouchPanning, passive);\n            wrapper.addEventListener(\"touchend\", _this.onTouchPanningStop, passive);\n        };\n        this.handleInitialize = function (wrapper, contentComponent) {\n            var isCentered = false;\n            var centerOnInit = _this.setup.centerOnInit;\n            var hasTarget = function (entries, target) {\n                // eslint-disable-next-line no-restricted-syntax\n                for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {\n                    var entry = entries_1[_i];\n                    if (entry.target === target) {\n                        return true;\n                    }\n                }\n                return false;\n            };\n            _this.applyTransformation();\n            _this.onInitCallbacks.forEach(function (callback) {\n                callback(getContext(_this));\n            });\n            _this.observer = new ResizeObserver(function (entries) {\n                if (hasTarget(entries, wrapper) || hasTarget(entries, contentComponent)) {\n                    if (centerOnInit && !isCentered) {\n                        var currentWidth = contentComponent.offsetWidth;\n                        var currentHeight = contentComponent.offsetHeight;\n                        if (currentWidth > 0 || currentHeight > 0) {\n                            isCentered = true;\n                            _this.setCenter();\n                        }\n                    }\n                    else {\n                        handleCancelAnimation(_this);\n                        handleCalculateBounds(_this, _this.transformState.scale);\n                        handleAlignToBounds(_this, 0);\n                    }\n                }\n            });\n            // Start observing the target node for configured mutations\n            _this.observer.observe(wrapper);\n            _this.observer.observe(contentComponent);\n        };\n        /// ///////\n        // Zoom\n        /// ///////\n        this.onWheelZoom = function (event) {\n            var disabled = _this.setup.disabled;\n            if (disabled)\n                return;\n            var isAllowed = isWheelAllowed(_this, event);\n            if (!isAllowed)\n                return;\n            var keysPressed = _this.isPressingKeys(_this.setup.wheel.activationKeys);\n            if (!keysPressed)\n                return;\n            handleWheelStart(_this, event);\n            handleWheelZoom(_this, event);\n            handleWheelStop(_this, event);\n        };\n        /// ///////\n        // Pan\n        /// ///////\n        this.onWheelPanning = function (event) {\n            var _a = _this.setup, disabled = _a.disabled, wheel = _a.wheel, panning = _a.panning;\n            if (!_this.wrapperComponent ||\n                !_this.contentComponent ||\n                disabled ||\n                !wheel.wheelDisabled ||\n                panning.disabled ||\n                !panning.wheelPanning ||\n                event.ctrlKey) {\n                return;\n            }\n            event.preventDefault();\n            event.stopPropagation();\n            var _b = _this.transformState, positionX = _b.positionX, positionY = _b.positionY;\n            var mouseX = positionX - event.deltaX;\n            var mouseY = positionY - event.deltaY;\n            var newPositionX = panning.lockAxisX ? positionX : mouseX;\n            var newPositionY = panning.lockAxisY ? positionY : mouseY;\n            var _c = _this.setup.alignmentAnimation, sizeX = _c.sizeX, sizeY = _c.sizeY;\n            var paddingValueX = getPaddingValue(_this, sizeX);\n            var paddingValueY = getPaddingValue(_this, sizeY);\n            if (newPositionX === positionX && newPositionY === positionY)\n                return;\n            handleNewPosition(_this, newPositionX, newPositionY, paddingValueX, paddingValueY);\n        };\n        this.onPanningStart = function (event) {\n            var disabled = _this.setup.disabled;\n            var onPanningStart = _this.props.onPanningStart;\n            if (disabled)\n                return;\n            var isAllowed = isPanningStartAllowed(_this, event);\n            if (!isAllowed)\n                return;\n            var keysPressed = _this.isPressingKeys(_this.setup.panning.activationKeys);\n            if (!keysPressed)\n                return;\n            if (event.button === 0 && !_this.setup.panning.allowLeftClickPan)\n                return;\n            if (event.button === 1 && !_this.setup.panning.allowMiddleClickPan)\n                return;\n            if (event.button === 2 && !_this.setup.panning.allowRightClickPan)\n                return;\n            event.preventDefault();\n            event.stopPropagation();\n            handleCancelAnimation(_this);\n            handlePanningStart(_this, event);\n            handleCallback(getContext(_this), event, onPanningStart);\n        };\n        this.onPanning = function (event) {\n            var disabled = _this.setup.disabled;\n            var onPanning = _this.props.onPanning;\n            if (disabled)\n                return;\n            var isAllowed = isPanningAllowed(_this);\n            if (!isAllowed)\n                return;\n            var keysPressed = _this.isPressingKeys(_this.setup.panning.activationKeys);\n            if (!keysPressed)\n                return;\n            event.preventDefault();\n            event.stopPropagation();\n            handlePanning(_this, event.clientX, event.clientY);\n            handleCallback(getContext(_this), event, onPanning);\n        };\n        this.onPanningStop = function (event) {\n            var onPanningStop = _this.props.onPanningStop;\n            if (_this.isPanning) {\n                handlePanningEnd(_this);\n                handleCallback(getContext(_this), event, onPanningStop);\n            }\n        };\n        /// ///////\n        // Pinch\n        /// ///////\n        this.onPinchStart = function (event) {\n            var disabled = _this.setup.disabled;\n            var _a = _this.props, onPinchingStart = _a.onPinchingStart, onZoomStart = _a.onZoomStart;\n            if (disabled)\n                return;\n            var isAllowed = isPinchStartAllowed(_this, event);\n            if (!isAllowed)\n                return;\n            handlePinchStart(_this, event);\n            handleCancelAnimation(_this);\n            handleCallback(getContext(_this), event, onPinchingStart);\n            handleCallback(getContext(_this), event, onZoomStart);\n        };\n        this.onPinch = function (event) {\n            var disabled = _this.setup.disabled;\n            var _a = _this.props, onPinching = _a.onPinching, onZoom = _a.onZoom;\n            if (disabled)\n                return;\n            var isAllowed = isPinchAllowed(_this);\n            if (!isAllowed)\n                return;\n            event.preventDefault();\n            event.stopPropagation();\n            handlePinchZoom(_this, event);\n            handleCallback(getContext(_this), event, onPinching);\n            handleCallback(getContext(_this), event, onZoom);\n        };\n        this.onPinchStop = function (event) {\n            var _a = _this.props, onPinchingStop = _a.onPinchingStop, onZoomStop = _a.onZoomStop;\n            if (_this.pinchStartScale) {\n                handlePinchStop(_this);\n                handleCallback(getContext(_this), event, onPinchingStop);\n                handleCallback(getContext(_this), event, onZoomStop);\n            }\n        };\n        /// ///////\n        // Touch\n        /// ///////\n        this.onTouchPanningStart = function (event) {\n            var disabled = _this.setup.disabled;\n            var onPanningStart = _this.props.onPanningStart;\n            if (disabled)\n                return;\n            var isAllowed = isPanningStartAllowed(_this, event);\n            if (!isAllowed)\n                return;\n            var isDoubleTap = _this.lastTouch &&\n                +new Date() - _this.lastTouch < 200 &&\n                event.touches.length === 1;\n            if (!isDoubleTap) {\n                _this.lastTouch = +new Date();\n                handleCancelAnimation(_this);\n                var touches = event.touches;\n                var isPanningAction = touches.length === 1;\n                var isPinchAction = touches.length === 2;\n                if (isPanningAction) {\n                    handleCancelAnimation(_this);\n                    handlePanningStart(_this, event);\n                    handleCallback(getContext(_this), event, onPanningStart);\n                }\n                if (isPinchAction) {\n                    _this.onPinchStart(event);\n                }\n            }\n        };\n        this.onTouchPanning = function (event) {\n            var disabled = _this.setup.disabled;\n            var onPanning = _this.props.onPanning;\n            if (_this.isPanning && event.touches.length === 1) {\n                if (disabled)\n                    return;\n                var isAllowed = isPanningAllowed(_this);\n                if (!isAllowed)\n                    return;\n                event.preventDefault();\n                event.stopPropagation();\n                var touch = event.touches[0];\n                handlePanning(_this, touch.clientX, touch.clientY);\n                handleCallback(getContext(_this), event, onPanning);\n            }\n            else if (event.touches.length > 1) {\n                _this.onPinch(event);\n            }\n        };\n        this.onTouchPanningStop = function (event) {\n            _this.onPanningStop(event);\n            _this.onPinchStop(event);\n        };\n        /// ///////\n        // Double Click\n        /// ///////\n        this.onDoubleClick = function (event) {\n            var disabled = _this.setup.disabled;\n            if (disabled)\n                return;\n            var isAllowed = isDoubleClickAllowed(_this, event);\n            if (!isAllowed)\n                return;\n            handleDoubleClick(_this, event);\n        };\n        /// ///////\n        // Helpers\n        /// ///////\n        this.clearPanning = function (event) {\n            if (_this.isPanning) {\n                _this.onPanningStop(event);\n            }\n        };\n        this.setKeyPressed = function (e) {\n            _this.pressedKeys[e.key] = true;\n        };\n        this.setKeyUnPressed = function (e) {\n            _this.pressedKeys[e.key] = false;\n        };\n        this.isPressingKeys = function (keys) {\n            if (!keys.length) {\n                return true;\n            }\n            return Boolean(keys.find(function (key) { return _this.pressedKeys[key]; }));\n        };\n        this.setTransformState = function (scale, positionX, positionY) {\n            var onTransformed = _this.props.onTransformed;\n            if (!Number.isNaN(scale) &&\n                !Number.isNaN(positionX) &&\n                !Number.isNaN(positionY)) {\n                if (scale !== _this.transformState.scale) {\n                    _this.transformState.previousScale = _this.transformState.scale;\n                    _this.transformState.scale = scale;\n                }\n                _this.transformState.positionX = positionX;\n                _this.transformState.positionY = positionY;\n                _this.applyTransformation();\n                var ctx_1 = getContext(_this);\n                _this.onChangeCallbacks.forEach(function (callback) { return callback(ctx_1); });\n                handleCallback(ctx_1, { scale: scale, positionX: positionX, positionY: positionY }, onTransformed);\n            }\n            else {\n                console.error(\"Detected NaN set state values\");\n            }\n        };\n        this.setCenter = function () {\n            if (_this.wrapperComponent && _this.contentComponent) {\n                var targetState = getCenterPosition(_this.transformState.scale, _this.wrapperComponent, _this.contentComponent);\n                _this.setTransformState(targetState.scale, targetState.positionX, targetState.positionY);\n            }\n        };\n        this.handleTransformStyles = function (x, y, scale) {\n            if (_this.props.customTransform) {\n                return _this.props.customTransform(x, y, scale);\n            }\n            return getTransformStyles(x, y, scale);\n        };\n        this.applyTransformation = function () {\n            if (!_this.mounted || !_this.contentComponent)\n                return;\n            var _a = _this.transformState, scale = _a.scale, positionX = _a.positionX, positionY = _a.positionY;\n            var transform = _this.handleTransformStyles(positionX, positionY, scale);\n            _this.contentComponent.style.transform = transform;\n        };\n        this.getContext = function () {\n            return getContext(_this);\n        };\n        /**\n         * Hooks\n         */\n        this.onChange = function (callback) {\n            if (!_this.onChangeCallbacks.has(callback)) {\n                _this.onChangeCallbacks.add(callback);\n            }\n            return function () {\n                _this.onChangeCallbacks.delete(callback);\n            };\n        };\n        this.onInit = function (callback) {\n            if (!_this.onInitCallbacks.has(callback)) {\n                _this.onInitCallbacks.add(callback);\n            }\n            return function () {\n                _this.onInitCallbacks.delete(callback);\n            };\n        };\n        /**\n         * Initialization\n         */\n        this.init = function (wrapperComponent, contentComponent) {\n            _this.cleanupWindowEvents();\n            _this.wrapperComponent = wrapperComponent;\n            _this.contentComponent = contentComponent;\n            handleCalculateBounds(_this, _this.transformState.scale);\n            _this.handleInitializeWrapperEvents(wrapperComponent);\n            _this.handleInitialize(wrapperComponent, contentComponent);\n            _this.initializeWindowEvents();\n            _this.isInitialized = true;\n            var ctx = getContext(_this);\n            handleCallback(ctx, undefined, _this.props.onInit);\n        };\n        this.props = props;\n        this.setup = createSetup(this.props);\n        this.transformState = createState(this.props);\n    }\n    return ZoomPanPinch;\n}());\n\nvar Context = react__WEBPACK_IMPORTED_MODULE_0___default().createContext(null);\nvar getContent = function (children, ctx) {\n    if (typeof children === \"function\") {\n        return children(ctx);\n    }\n    return children;\n};\nvar TransformWrapper = react__WEBPACK_IMPORTED_MODULE_0___default().forwardRef(function (props, ref) {\n    var instance = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(new ZoomPanPinch(props)).current;\n    var content = getContent(props.children, getControls(instance));\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, function () { return getControls(instance); }, [instance]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n        instance.update(props);\n    }, [instance, props]);\n    return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Context.Provider, { value: instance }, content);\n});\n\nvar KeepScale = react__WEBPACK_IMPORTED_MODULE_0___default().forwardRef(function (props, ref) {\n    var localRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var instance = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(Context);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n        return instance.onChange(function (ctx) {\n            if (localRef.current) {\n                var positionX = 0;\n                var positionY = 0;\n                localRef.current.style.transform = instance.handleTransformStyles(positionX, positionY, 1 / ctx.instance.transformState.scale);\n            }\n        });\n    }, [instance]);\n    return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", __assign({}, props, { ref: mergeRefs([localRef, ref]) }));\n});\n\nvar initialElementRect = {\n    width: 0,\n    height: 0,\n    y: 0,\n    x: 0,\n    top: 0,\n    bottom: 0,\n    left: 0,\n    right: 0,\n};\nvar useResize = function (ref, onResize, dependencies) {\n    var resizeObserverRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    var rectRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(initialElementRect);\n    var didUnmount = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(function () {\n        var _a;\n        didUnmount.current = false;\n        if (!(\"ResizeObserver\" in window)) {\n            return;\n        }\n        if (ref) {\n            resizeObserverRef.current = new ResizeObserver(function (entries) {\n                var newSize = ref.getBoundingClientRect();\n                if (!Array.isArray(entries) ||\n                    !entries.length ||\n                    didUnmount.current ||\n                    (newSize.width === rectRef.current.width &&\n                        newSize.height === rectRef.current.height))\n                    return;\n                onResize(newSize, ref);\n                rectRef.current = newSize;\n            });\n            (_a = resizeObserverRef.current) === null || _a === void 0 ? void 0 : _a.observe(ref);\n        }\n        return function () {\n            var _a;\n            didUnmount.current = true;\n            if (ref) {\n                (_a = resizeObserverRef.current) === null || _a === void 0 ? void 0 : _a.unobserve(ref);\n            }\n        };\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, __spreadArray([onResize, ref], dependencies, true));\n};\n\nvar previewStyles = {\n    position: \"absolute\",\n    zIndex: 2,\n    top: \"0px\",\n    left: \"0px\",\n    boxSizing: \"border-box\",\n    border: \"3px solid red\",\n    transformOrigin: \"0% 0%\",\n    boxShadow: \"rgba(0,0,0,0.2) 0 0 0 10000000px\",\n};\nvar MiniMap = function (_a) {\n    var _b = _a.width, width = _b === void 0 ? 200 : _b, _c = _a.height, height = _c === void 0 ? 200 : _c, _d = _a.borderColor, borderColor = _d === void 0 ? \"red\" : _d, children = _a.children, rest = __rest(_a, [\"width\", \"height\", \"borderColor\", \"children\"]);\n    var _e = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false), initialized = _e[0], setInitialized = _e[1];\n    var instance = useTransformContext();\n    var miniMapInstance = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var mainRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var wrapperRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var previewRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var getViewportSize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {\n        if (instance.wrapperComponent) {\n            var rect = instance.wrapperComponent.getBoundingClientRect();\n            return {\n                width: rect.width,\n                height: rect.height,\n            };\n        }\n        return {\n            width: 0,\n            height: 0,\n        };\n    }, [instance.wrapperComponent]);\n    var getContentSize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {\n        if (instance.contentComponent) {\n            var rect = instance.contentComponent.getBoundingClientRect();\n            return {\n                width: rect.width / instance.transformState.scale,\n                height: rect.height / instance.transformState.scale,\n            };\n        }\n        return {\n            width: 0,\n            height: 0,\n        };\n    }, [instance.contentComponent, instance.transformState.scale]);\n    var computeMiniMapScale = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {\n        var contentSize = getContentSize();\n        var scaleX = width / contentSize.width;\n        var scaleY = height / contentSize.height;\n        var scale = scaleY > scaleX ? scaleX : scaleY;\n        return scale;\n    }, [getContentSize, height, width]);\n    var computeMiniMapSize = function () {\n        var contentSize = getContentSize();\n        var scaleX = width / contentSize.width;\n        var scaleY = height / contentSize.height;\n        if (scaleY > scaleX) {\n            return { width: width, height: contentSize.height * scaleX };\n        }\n        return { width: contentSize.width * scaleY, height: height };\n    };\n    var computeMiniMapStyle = function () {\n        var scale = computeMiniMapScale();\n        var style = {\n            transform: \"scale(\".concat(scale || 1, \")\"),\n            transformOrigin: \"0% 0%\",\n            position: \"absolute\",\n            boxSizing: \"border-box\",\n            zIndex: 1,\n            overflow: \"hidden\",\n        };\n        Object.keys(style).forEach(function (key) {\n            if (wrapperRef.current) {\n                wrapperRef.current.style[key] = style[key];\n            }\n        });\n    };\n    var transformMiniMap = function () {\n        computeMiniMapStyle();\n        var miniSize = computeMiniMapSize();\n        var wrapSize = getContentSize();\n        if (wrapperRef.current) {\n            wrapperRef.current.style.width = \"\".concat(wrapSize.width, \"px\");\n            wrapperRef.current.style.height = \"\".concat(wrapSize.height, \"px\");\n        }\n        if (mainRef.current) {\n            mainRef.current.style.width = \"\".concat(miniSize.width, \"px\");\n            mainRef.current.style.height = \"\".concat(miniSize.height, \"px\");\n        }\n        if (previewRef.current) {\n            var size = getViewportSize();\n            var scale = computeMiniMapScale();\n            var previewScale = scale * (1 / instance.transformState.scale);\n            var transform = instance.handleTransformStyles(-instance.transformState.positionX * previewScale, -instance.transformState.positionY * previewScale, 1);\n            previewRef.current.style.transform = transform;\n            previewRef.current.style.width = \"\".concat(size.width * previewScale, \"px\");\n            previewRef.current.style.height = \"\".concat(size.height * previewScale, \"px\");\n        }\n    };\n    var initialize = function () {\n        transformMiniMap();\n    };\n    useTransformEffect(function () {\n        transformMiniMap();\n    });\n    useTransformInit(function () {\n        initialize();\n        setInitialized(true);\n    });\n    useResize(instance.contentComponent, initialize, [initialized]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n        return instance.onChange(function (zpp) {\n            var scale = computeMiniMapScale();\n            if (miniMapInstance.current) {\n                miniMapInstance.current.instance.transformState.scale =\n                    zpp.instance.transformState.scale;\n                miniMapInstance.current.instance.transformState.positionX =\n                    zpp.instance.transformState.positionX * scale;\n                miniMapInstance.current.instance.transformState.positionY =\n                    zpp.instance.transformState.positionY * scale;\n            }\n        });\n    }, [computeMiniMapScale, instance, miniMapInstance]);\n    var wrapperStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {\n        return {\n            position: \"relative\",\n            zIndex: 2,\n            overflow: \"hidden\",\n        };\n    }, []);\n    return (react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", __assign({}, rest, { ref: mainRef, style: wrapperStyle, className: \"rzpp-mini-map \".concat(rest.className || \"\") }),\n        react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", __assign({}, rest, { ref: wrapperRef, className: \"rzpp-wrapper\" }), children),\n        react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", { className: \"rzpp-preview\", ref: previewRef, style: __assign(__assign({}, previewStyles), { borderColor: borderColor }) })));\n};\n\nfunction styleInject(css, ref) {\n  if ( ref === void 0 ) ref = {};\n  var insertAt = ref.insertAt;\n\n  if (!css || typeof document === 'undefined') { return; }\n\n  var head = document.head || document.getElementsByTagName('head')[0];\n  var style = document.createElement('style');\n  style.type = 'text/css';\n\n  if (insertAt === 'top') {\n    if (head.firstChild) {\n      head.insertBefore(style, head.firstChild);\n    } else {\n      head.appendChild(style);\n    }\n  } else {\n    head.appendChild(style);\n  }\n\n  if (style.styleSheet) {\n    style.styleSheet.cssText = css;\n  } else {\n    style.appendChild(document.createTextNode(css));\n  }\n}\n\nvar css_248z = \".transform-component-module_wrapper__SPB86 {\\n  position: relative;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  height: -moz-fit-content;\\n  height: fit-content;\\n  overflow: hidden;\\n  -webkit-touch-callout: none; /* iOS Safari */\\n  -webkit-user-select: none; /* Safari */\\n  -khtml-user-select: none; /* Konqueror HTML */\\n  -moz-user-select: none; /* Firefox */\\n  -ms-user-select: none; /* Internet Explorer/Edge */\\n  user-select: none;\\n  margin: 0;\\n  padding: 0;\\n  transform: translate3d(0, 0, 0);\\n}\\n.transform-component-module_content__FBWxo {\\n  display: flex;\\n  flex-wrap: wrap;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  height: -moz-fit-content;\\n  height: fit-content;\\n  margin: 0;\\n  padding: 0;\\n  transform-origin: 0% 0%;\\n}\\n.transform-component-module_content__FBWxo img {\\n  pointer-events: none;\\n}\\n\";\nvar styles = {\"wrapper\":\"transform-component-module_wrapper__SPB86\",\"content\":\"transform-component-module_content__FBWxo\"};\nstyleInject(css_248z);\n\nvar TransformComponent = function (_a) {\n    var children = _a.children, _b = _a.wrapperClass, wrapperClass = _b === void 0 ? \"\" : _b, _c = _a.contentClass, contentClass = _c === void 0 ? \"\" : _c, wrapperStyle = _a.wrapperStyle, contentStyle = _a.contentStyle, _d = _a.wrapperProps, wrapperProps = _d === void 0 ? {} : _d, _e = _a.contentProps, contentProps = _e === void 0 ? {} : _e;\n    var _f = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(Context), init = _f.init, cleanupWindowEvents = _f.cleanupWindowEvents;\n    var wrapperRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var contentRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n        var wrapper = wrapperRef.current;\n        var content = contentRef.current;\n        if (wrapper !== null && content !== null && init) {\n            init === null || init === void 0 ? void 0 : init(wrapper, content);\n        }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        return function () {\n            cleanupWindowEvents === null || cleanupWindowEvents === void 0 ? void 0 : cleanupWindowEvents();\n        };\n    }, []);\n    return (react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", __assign({}, wrapperProps, { ref: wrapperRef, className: \"\".concat(baseClasses.wrapperClass, \" \").concat(styles.wrapper, \" \").concat(wrapperClass), style: wrapperStyle }),\n        react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", __assign({}, contentProps, { ref: contentRef, className: \"\".concat(baseClasses.contentClass, \" \").concat(styles.content, \" \").concat(contentClass), style: contentStyle }), children)));\n};\n\nvar useTransformContext = function () {\n    var libraryContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(Context);\n    if (!libraryContext) {\n        throw new Error(\"Transform context must be placed inside TransformWrapper\");\n    }\n    return libraryContext;\n};\n\nvar useControls = function () {\n    var libraryContext = useTransformContext();\n    return getControls(libraryContext);\n};\n\nvar useTransformInit = function (callback) {\n    var libraryContext = useTransformContext();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n        var unmountCallback;\n        var unmount;\n        if (libraryContext.contentComponent && libraryContext.wrapperComponent) {\n            unmountCallback = callback(getState(libraryContext));\n        }\n        else {\n            unmount = libraryContext.onInit(function (ref) {\n                unmountCallback = callback(getState(ref.instance));\n            });\n        }\n        return function () {\n            unmount === null || unmount === void 0 ? void 0 : unmount();\n            unmountCallback === null || unmountCallback === void 0 ? void 0 : unmountCallback();\n        };\n    }, []);\n};\n\nvar useTransformEffect = function (callback) {\n    var libraryContext = useTransformContext();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n        var unmountCallback;\n        var unmount = libraryContext.onChange(function (ref) {\n            unmountCallback = callback(getState(ref.instance));\n        });\n        return function () {\n            unmount();\n            unmountCallback === null || unmountCallback === void 0 ? void 0 : unmountCallback();\n        };\n    }, [callback, libraryContext]);\n};\n\nfunction useTransformComponent(callback) {\n    var libraryContext = useTransformContext();\n    var _a = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(callback(getState(libraryContext))), transformRender = _a[0], setTransformRender = _a[1];\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n        var mounted = true;\n        var unmount = libraryContext.onChange(function (ref) {\n            if (mounted) {\n                setTransformRender(callback(getState(ref.instance)));\n            }\n        });\n        return function () {\n            unmount();\n            mounted = false;\n        };\n    }, [callback, libraryContext]);\n    return transformRender;\n}\n\n\n//# sourceMappingURL=index.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-zoom-pan-pinch@3.7.0__ddc0427b0adf89251254484d2dac3217/node_modules/react-zoom-pan-pinch/dist/index.esm.js\n");

/***/ })

};
;