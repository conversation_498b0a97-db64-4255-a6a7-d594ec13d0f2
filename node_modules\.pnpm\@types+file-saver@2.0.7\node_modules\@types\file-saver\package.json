{"name": "@types/file-saver", "version": "2.0.7", "description": "TypeScript definitions for file-saver", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/file-saver", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/cyrilschumacher"}, {"name": "<PERSON>", "githubUsername": "DaIgeb", "url": "https://github.com/DaIgeb"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/HitkoDev"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/bendingbender"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/file-saver"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "fff70ecd1cd1d1cd4b827d5e090ff94e2591f9044912e4e01ebd176d4eca9e14", "typeScriptVersion": "4.5"}