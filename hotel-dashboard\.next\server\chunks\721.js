exports.id=721,exports.ids=[721],exports.modules={2641:(e,t,l)=>{Promise.resolve().then(l.t.bind(l,85770,23)),Promise.resolve().then(l.t.bind(l,88204,23)),Promise.resolve().then(l.t.bind(l,82576,23)),Promise.resolve().then(l.t.bind(l,59507,23)),Promise.resolve().then(l.t.bind(l,61283,23)),Promise.resolve().then(l.t.bind(l,75147,23)),Promise.resolve().then(l.t.bind(l,83163,23)),Promise.resolve().then(l.t.bind(l,99773,23))},8629:(e,t,l)=>{"use strict";l.d(t,{dj:()=>h,oR:()=>u});var o=l(13072);let s=0,i=new Map,r=e=>{if(i.has(e))return;let t=setTimeout(()=>{i.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);i.set(e,t)},a=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:l}=t;return l?r(l):e.toasts.forEach(e=>{r(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===l||void 0===l?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},n=[],c={toasts:[]};function d(e){c=a(c,e),n.forEach(e=>{e(c)})}function u({...e}){let t=(s=(s+1)%Number.MAX_SAFE_INTEGER).toString(),l=()=>d({type:"DISMISS_TOAST",toastId:t});return d({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||l()}}}),{id:t,dismiss:l,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function h(){let[e,t]=o.useState(c);return o.useEffect(()=>(n.push(t),()=>{let e=n.indexOf(t);e>-1&&n.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},14784:(e,t,l)=>{"use strict";l.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>n,Zp:()=>r,aR:()=>a,wL:()=>u});var o=l(45781),s=l(13072),i=l(77401);let r=s.forwardRef(({className:e,...t},l)=>(0,o.jsx)("div",{ref:l,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));r.displayName="Card";let a=s.forwardRef(({className:e,...t},l)=>(0,o.jsx)("div",{ref:l,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));a.displayName="CardHeader";let n=s.forwardRef(({className:e,...t},l)=>(0,o.jsx)("div",{ref:l,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));n.displayName="CardTitle";let c=s.forwardRef(({className:e,...t},l)=>(0,o.jsx)("div",{ref:l,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let d=s.forwardRef(({className:e,...t},l)=>(0,o.jsx)("div",{ref:l,className:(0,i.cn)("p-6 pt-0",e),...t}));d.displayName="CardContent";let u=s.forwardRef(({className:e,...t},l)=>(0,o.jsx)("div",{ref:l,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},25290:()=>{},25794:()=>{},43030:(e,t,l)=>{"use strict";l.d(t,{J:()=>c});var o=l(45781),s=l(13072),i=l(11687),r=l(87990),a=l(77401);let n=(0,r.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=s.forwardRef(({className:e,...t},l)=>(0,o.jsx)(i.b,{ref:l,className:(0,a.cn)(n(),e),...t}));c.displayName=i.b.displayName},44238:(e,t,l)=>{"use strict";l.d(t,{$:()=>c});var o=l(45781),s=l(13072),i=l(83759),r=l(87990),a=l(77401);let n=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef(({className:e,variant:t,size:l,asChild:s=!1,...r},c)=>{let d=s?i.DX:"button";return(0,o.jsx)(d,{className:(0,a.cn)(n({variant:t,size:l,className:e})),ref:c,...r})});c.displayName="Button"},59650:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>i,metadata:()=>s});var o=l(95479);l(25290);let s={title:"v0 App",description:"Created with v0",generator:"v0.dev"};function i({children:e}){return(0,o.jsx)("html",{lang:"en",children:(0,o.jsx)("body",{children:e})})}},63911:(e,t,l)=>{"use strict";l.d(t,{k:()=>a});var o=l(45781),s=l(13072),i=l(10467),r=l(77401);let a=s.forwardRef(({className:e,value:t,...l},s)=>(0,o.jsx)(i.bL,{ref:s,className:(0,r.cn)("relative h-2 w-full overflow-hidden rounded-full bg-gray-200",e),...l,children:(0,o.jsx)(i.C1,{className:"h-full w-full flex-1 bg-blue-500 transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));a.displayName=i.bL.displayName},77401:(e,t,l)=>{"use strict";l.d(t,{cn:()=>i});var o=l(42366),s=l(73927);function i(...e){return(0,s.QP)((0,o.$)(e))}},88116:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>I});var o=l(45781),s=l(13072),i=l.n(s),r=l(14784),a=l(43030),n=l(44238),c=l(758),d=l(2334),u=l.n(d),h=l(8629),f=l(88231),m=l(74299),g=l.n(m);async function p(e,t,l=3e3){let o=Date.now();return new Promise((s,i)=>{let r=()=>{let i=e.innerText.includes("图表加载中..."),a=e.querySelector(".recharts-surface");a&&!i?(console.log(`Chart for block ${t} is ready.`),s()):Date.now()-o>l?(console.warn(`Timeout waiting for chart ${t} to render. isLoadingTextPresent: ${i}, hasRechartsSurface: ${!!a}`),s()):setTimeout(r,200)};r()})}async function x(e,t,l,o){try{console.log("[exportUtils] Starting export for ZIP. Selected block IDs:",l);let s=new(g()),i=l.length,r=0,a=0;for(let t of l){console.log(`[exportUtils] Processing block ${t}`);let l=e.querySelector(`[data-block-id="${t}"]`);if(!l){console.error(`[exportUtils] Block element not found for ID: ${t}. Skipping.`),s.file(`error_block-${t}_not_found.txt`,`DOM element for block ID ${t} was not found.`),r++,a++,o&&o(r/i*100);continue}try{console.log(`[exportUtils] Found block element for ${t}. Waiting for chart to be ready...`),await p(l,t),console.log(`[exportUtils] Chart for ${t} is considered ready. Generating image...`);let e=await u().toPng(l,{width:l.scrollWidth,height:l.scrollHeight,bgcolor:"#ffffff",style:{border:"none !important",outline:"none !important","box-shadow":"none !important","background-color":"#ffffff !important"},filter:e=>(e instanceof HTMLElement&&(e.style.border="none",e.style.outline="none",e.style.boxShadow="none"),!0),cacheBust:!0});console.log(`[exportUtils] Image generated for block ${t}.`);let o=e.split(",")[1];if(!o)throw Error(`Failed to extract base64 data for block ${t}`);let i=atob(o),r=new Uint8Array(i.length);for(let e=0;e<i.length;e++)r[e]=i.charCodeAt(e);s.file(`chart_block-${t}.png`,r),console.log(`[exportUtils] Added image for block ${t} to zip.`)}catch(e){console.error(`[exportUtils] Failed to generate or add image for block ${t}:`,e),s.file(`error_block-${t}_img_generation.txt`,`Failed to generate image for block ${t}: ${e instanceof Error?e.message:String(e)}`),a++}r++,o&&o(r/i*100),console.log(`[exportUtils] Finished processing block ${t}. Progress: ${r/i*100}%`)}if(0===r&&i>0)throw Error("No blocks were processed for export.");if(0===i){(0,h.oR)({title:"没有内容可导出",description:"没有选择任何数据块进行导出。",variant:"default"});return}console.log("[exportUtils] Generating zip file...");let n=await s.generateAsync({type:"blob"});if(console.log("[exportUtils] Zip file generated, size:",n.size),0===n.size)throw Error("Generated zip file is empty. This might happen if all image generations failed.");(0,f.saveAs)(n,`${t}.zip`),console.log("[exportUtils] Zip file saved."),a>0?(0,h.oR)({title:"导出部分完成",description:`${i-a} 个图表已导出，但有 ${a} 个图表导出失败。详情请查看ZIP包内的错误文件。`,variant:"default"}):(0,h.oR)({title:"导出成功",description:`已将 ${i} 个图表导出为压缩包。`})}catch(e){throw console.error("[exportUtils] Error exporting elements as images:",e),(0,h.oR)({title:"导出失败",description:e instanceof Error?e.message:"导出过程中发生错误",variant:"destructive"}),e}}var b=l(63911),k=l(5516),y=l(23102),v=l(77401);let N=s.forwardRef(({className:e,...t},l)=>(0,o.jsx)(k.bL,{className:(0,v.cn)("grid gap-2",e),...t,ref:l}));N.displayName=k.bL.displayName;let w=s.forwardRef(({className:e,...t},l)=>(0,o.jsx)(k.q7,{ref:l,className:(0,v.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,o.jsx)(k.C1,{className:"flex items-center justify-center",children:(0,o.jsx)(y.A,{className:"h-2.5 w-2.5 fill-current text-current"})})}));w.displayName=k.q7.displayName;let j=({dataChunks:e,onSelectionChange:t,onStartExport:l})=>{console.log("[LogDisplayArea] Rendering. ProcessedDataChunks count:",e.length);let[d,u]=(0,s.useState)(""),[f,m]=(0,s.useState)([]),g=(0,s.useRef)(null),[p,k]=(0,s.useState)(!1),[y,v]=(0,s.useState)(0),{toast:j}=(0,h.dj)(),A=i().useRef(t);(0,s.useEffect)(()=>{A.current=t},[t]),(0,s.useEffect)(()=>{if(console.log("[LogDisplayArea] useEffect for auto-selection triggered. processedDataChunks count:",e.length,"current selectedBlockId:",d),0===e.length)""!==d&&(console.log("[LogDisplayArea] No data chunks, clearing selectedBlockId."),u(""));else if(d&&""!==d.trim()&&e.some(e=>e.block_id===d))console.log("[LogDisplayArea] Current selection is still valid:",d);else{console.log("[LogDisplayArea] Current selection is invalid or not set. Attempting to find first valid block.");let t=e.find(e=>e.block_id&&"string"==typeof e.block_id&&""!==e.block_id.trim());t?(console.log("[LogDisplayArea] Found first valid block. Setting selectedBlockId to:",t.block_id),u(t.block_id)):""!==d&&(console.warn("[LogDisplayArea] No valid block_id found in any processed chunks. Clearing selectedBlockId."),u(""))}},[e,d]),(0,s.useEffect)(()=>{if(d&&e.length>0){let t=e.filter(e=>e.block_id===d);A.current(t)}else A.current([])},[d,e]);let D=(0,s.useCallback)(e=>{console.log("[LogDisplayArea] handleBlockSelectionChange - START. blockId:",e),u(e),console.log("[LogDisplayArea] handleBlockSelectionChange - END.")},[]),T=(0,s.useCallback)(e=>{console.log("[LogDisplayArea] handleExportSelectionChange - START. blockId:",e),m(t=>{let l=t.includes(e)?t.filter(t=>t!==e):[...t,e];return console.log("[LogDisplayArea] handleExportSelectionChange - New selection:",l),l})},[]),S=(0,s.useCallback)(()=>{console.log("[LogDisplayArea] selectAllForExport - START");let t=e.map(e=>e.block_id);m(t),console.log("[LogDisplayArea] selectAllForExport - END. Selected all IDs:",t)},[e]),_=(0,s.useCallback)(()=>{console.log("[LogDisplayArea] deselectAllForExport - START"),m([]),console.log("[LogDisplayArea] deselectAllForExport - END")},[]),C=async()=>{if(console.log("handleExportAllImages called"),console.log("displayAreaRef:",g.current),console.log("processedDataChunks:",e),console.log("exportBlockIds:",f),!g.current){j({variant:"destructive",title:"错误",description:"显示区域容器未找到。"});return}if(!e||0===e.length){j({variant:"destructive",title:"错误",description:"没有可导出的内容。"});return}if(0===f.length){j({variant:"destructive",title:"错误",description:"请至少选择一个数据块进行导出。"});return}k(!0),v(0);try{let e=document.querySelector(".log-chart-container");if(!e)throw Error("找不到图表容器");let t=e.querySelectorAll("[data-block-id]");t.forEach(e=>{let t=e.getAttribute("data-block-id");t&&f.includes(t)?e.style.display="block":e.style.display="none"}),await new Promise(e=>setTimeout(e,1e3)),await x(e,`log-analysis-summary-${Date.now()}`,f,e=>{v(e)}),t.forEach(e=>{e.getAttribute("data-block-id")===d?e.style.display="block":e.style.display="none"})}catch(e){console.error("DisplayArea export failed in component:",e),j({variant:"destructive",title:"导出失败",description:e instanceof Error?e.message:"导出过程中发生错误。"})}finally{k(!1),v(0)}},E=e&&e.length>0;return(0,o.jsxs)(r.Zp,{className:"h-[450px] flex flex-col",children:[(0,o.jsxs)(r.aR,{className:"flex flex-row items-center justify-between",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)(r.ZB,{children:"选择数据块进行分析"}),(0,o.jsx)(r.BT,{children:"从解析的日志文件中选择一个数据块以在图表中显示。"})]}),(0,o.jsx)(n.$,{onClick:C,disabled:p||!E||0===f.length,size:"sm",children:p?"导出中...":(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"导出选中图片"]})})]}),(0,o.jsx)(r.Wu,{className:"flex-1 overflow-hidden p-4",children:(0,o.jsxs)("div",{ref:g,className:"h-full flex flex-col",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,o.jsx)(n.$,{onClick:S,size:"sm",variant:"outline",children:"全选"}),(0,o.jsx)(n.$,{onClick:_,size:"sm",variant:"outline",children:"取消全选"}),(0,o.jsxs)("span",{className:"text-sm text-muted-foreground ml-2",children:["已选择导出: ",f.length," 项"]}),p&&(0,o.jsx)("div",{className:"flex-1 ml-4",children:(0,o.jsx)(b.k,{value:y,className:"h-1"})})]}),(0,o.jsx)("div",{className:"flex-1 overflow-y-auto min-h-0",children:(0,o.jsx)("div",{className:"space-y-2 p-2 border rounded-md",children:e.map(e=>(0,o.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,o.jsx)(N,{value:d,onValueChange:D,className:"flex items-center",children:(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)(w,{value:e.block_id,id:`display-${e.block_id}`}),(0,o.jsx)(a.J,{htmlFor:`display-${e.block_id}`,className:"cursor-pointer",children:"显示"})]})}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)("input",{type:"checkbox",id:`export-${e.block_id}`,checked:f.includes(e.block_id),onChange:()=>T(e.block_id),className:"h-4 w-4 rounded border-gray-300"}),(0,o.jsx)(a.J,{htmlFor:`export-${e.block_id}`,className:"cursor-pointer",children:`数据块 ${e.block_id} (胶厚: ${e.glue_thickness_values.length}, 准直: ${e.collimation_diff_values.length})`})]})]},e.block_id))})}),(!e||0===e.length)&&(0,o.jsx)("p",{className:"text-muted-foreground p-2",children:"暂无数据块可供分析或导出。"})]})})]})};var A=l(73486),D=l(71945),T=l(30212),S=l(29695),_=l(61676),C=l(51603),E=l(70608),R=l(38754),$=l(36377);let F=e=>{if(!e)return NaN;try{let t=e.replace(",",".");if(!t.includes("T")&&t.includes(" ")){let e=t.split(" ");e.length>1&&e[0].includes("-")&&e[1].includes(":")&&(t=e[0]+"T"+e.slice(1).join(" "))}let l=new Date(t),o=l.getTime();if(isNaN(o)){let t=e.replace(",",".");o=(l=new Date(t)).getTime()}if(isNaN(o)&&(o=(l=new Date(e)).getTime()),isNaN(o))return console.warn(`[LogChartViewHelper] Failed to parse timestamp: "${e}"`),NaN;return o}catch(t){return console.error(`[LogChartViewHelper] Error parsing timestamp: "${e}"`,t),NaN}},L=e=>{let t=[];(e.glue_thickness_values||[]).forEach(e=>{let l=F(e.timestamp);isNaN(l)||t.push({time:l,glueThickness:e.value,collimationDiff:null})}),(e.collimation_diff_values||[]).forEach(e=>{let l=F(e.timestamp);isNaN(l)||t.push({time:l,glueThickness:null,collimationDiff:e.value})}),t.sort((e,t)=>e.time-t.time);let l=[];return t.forEach(e=>{let t=l.find(t=>t.time===e.time);t?(null!==e.glueThickness&&(t.glueThickness=e.glueThickness),null!==e.collimationDiff&&(t.collimationDiff=e.collimationDiff)):l.push({...e})}),l},P=({chunk:e,isChartReady:t})=>{let{chartDataForThisBlock:l,eventPointsForThisBlock:i,timeDomainForThisBlock:r,glueDomainForThisBlock:a,collimationDomainForThisBlock:n,hasDataForThisBlock:c}=(0,s.useMemo)(()=>{if(!e)return{chartDataForThisBlock:[],eventPointsForThisBlock:[],timeDomainForThisBlock:[Date.now()-36e5,Date.now()],glueDomainForThisBlock:[0,1e3],collimationDomainForThisBlock:[0,.1],hasDataForThisBlock:!1};let t=L(e),l=(e.valve_open_events||[]).map(e=>({time:F(e.timestamp),value:0,label:`打开放气阀 (${e.timestamp.split(" ")[1]||e.timestamp})`})).filter(e=>!isNaN(e.time));l.sort((e,t)=>e.time-t.time);let o=[Date.now()-36e5,Date.now()];if(t.length>0){let e=t.map(e=>e.time).filter(e=>!isNaN(e));e.length>0&&(o=[Math.min(...e),Math.max(...e)])}o[0]===o[1]&&(o[1]=o[0]+36e5);let s=t.map(e=>e.glueThickness).filter(e=>null!==e&&!isNaN(e)),i=[0,1e3];s.length>0&&(i=[Math.min(...s),Math.max(...s)]),i[0]===i[1]&&(i[1]=i[0]+10);let r=t.map(e=>e.collimationDiff).filter(e=>null!==e&&!isNaN(e)),a=[0,.1];return r.length>0&&(a=[Math.min(...r),Math.max(...r)]),a[0]===a[1]&&(a[1]=a[0]+.01),{chartDataForThisBlock:t,eventPointsForThisBlock:l,timeDomainForThisBlock:o,glueDomainForThisBlock:i,collimationDomainForThisBlock:a,hasDataForThisBlock:t.length>0}},[e]);return c&&t?(0,o.jsx)(A.u,{width:"100%",height:"100%",children:(0,o.jsxs)(D.X,{data:l,margin:{top:20,right:40,left:30,bottom:20},children:[(0,o.jsx)(T.d,{strokeDasharray:"3 3"}),(0,o.jsx)(S.W,{dataKey:"time",domain:r,type:"number",tickFormatter:e=>new Date(e).toLocaleTimeString(),allowDuplicatedCategory:!1}),(0,o.jsx)(_.h,{yAxisId:"glue",orientation:"left",domain:a,type:"number",stroke:"#8884d8",label:{value:"胶厚 (μm)",angle:-90,position:"insideLeft",offset:-5,style:{fill:"#8884d8",textAnchor:"middle"}},tickFormatter:e=>e.toFixed(2),width:70}),(0,o.jsx)(_.h,{yAxisId:"collimation",orientation:"right",domain:n,type:"number",stroke:"#82ca9d",label:{value:"准直差",angle:90,position:"insideRight",offset:-15,style:{fill:"#82ca9d",textAnchor:"middle"}},tickFormatter:e=>e.toFixed(3),width:80}),(0,o.jsx)(C.m,{labelFormatter:e=>new Date(e).toLocaleString(),formatter:(e,t)=>"number"!=typeof e?[e,t]:"glueThickness"===t?[e.toFixed(2)+" μm","胶厚"]:"collimationDiff"===t?[e.toFixed(3),"准直差"]:[e,t],contentStyle:{backgroundColor:"rgba(255, 255, 255, 0.9)",border:"1px solid #ccc",borderRadius:"4px",padding:"8px"}}),(0,o.jsx)(E.s,{verticalAlign:"top",height:36,wrapperStyle:{paddingBottom:"10px"}}),(0,o.jsx)(R.N,{yAxisId:"glue",type:"monotone",dataKey:"glueThickness",name:"胶厚",stroke:"#8884d8",strokeWidth:2,dot:{r:2},activeDot:{r:5},isAnimationActive:!1,connectNulls:!0}),(0,o.jsx)(R.N,{yAxisId:"collimation",type:"monotone",dataKey:"collimationDiff",name:"准直差",stroke:"#82ca9d",strokeWidth:2,dot:{r:2},activeDot:{r:5},isAnimationActive:!1,connectNulls:!0}),i.map((t,l)=>(0,o.jsx)($.e,{x:t.time,stroke:"rgba(255,0,0,0.7)",yAxisId:"glue",strokeDasharray:"4 4",label:{value:t.label,position:"insideTopRight",fill:"rgba(255,0,0,0.7)",fontSize:10}},`event-${e.block_id}-${l}`))]})}):(0,o.jsx)("div",{className:"flex items-center justify-center h-full text-muted-foreground",children:t?"此数据块无有效图表数据。":"图表加载中..."})},B=function({dataChunks:e,selectedBlockIds:t,onBlockSelect:l}){let i=(0,s.useRef)(null),[a,n]=(0,s.useState)(!1),[c,d]=(0,s.useState)(!1),{toast:u}=(0,h.dj)();return(0,s.useMemo)(()=>(console.log("LogChartView - hasChartData check:",{dataChunks:e}),e&&Array.isArray(e))?e.length>0:(console.log("LogChartView - dataChunks is invalid:",e),!1),[e]),(0,s.useMemo)(()=>(console.log("LogChartView - chartData calculation:",{dataChunks:e,selectedBlockIds:t}),e&&Array.isArray(e))?e.filter(e=>(console.log("Filtering chunk:",{chunk:e,selectedBlockIds:t}),t.includes(e.block_id))).flatMap(e=>(console.log("Processing chunk for chart data:",e),e.data&&Array.isArray(e.data))?e.data.map(t=>({name:t.name,value:t.value,type:t.type,block_id:e.block_id})):(console.log("Invalid chunk data:",e.data),[])):(console.log("LogChartView - dataChunks is invalid in chartData:",e),[]),[e,t]),(0,o.jsx)("div",{ref:i,className:"space-y-6 log-chart-container",children:e.map(e=>(0,o.jsxs)(r.Zp,{"data-block-id":e.block_id,className:t.includes(e.block_id)?"block":"hidden",children:[(0,o.jsx)(r.aR,{className:"flex flex-row justify-between items-center",children:(0,o.jsxs)(r.ZB,{children:["数据块 ",e.block_id]})}),(0,o.jsx)(r.Wu,{className:"p-0",children:(0,o.jsx)("div",{className:"w-full h-[400px] min-h-[400px]",style:{width:"100%",height:"400px"},children:(0,o.jsx)(P,{chunk:e,isChartReady:c})})})]},e.block_id))})};function I(){let[e,t]=(0,s.useState)([]),[l,i]=(0,s.useState)([]),[a,n]=(0,s.useState)(!0),[c,d]=(0,s.useState)(null),[u,f]=(0,s.useState)(!1),[m,g]=(0,s.useState)([]),p=(0,s.useRef)(null),{toast:x}=(0,h.dj)(),b=t=>{console.log("[LogAnalysisPage] handleBlockSelect - START. Received blockId:",t);let l=e.find(e=>e.block_id===t);l&&i([l]),console.log("[LogAnalysisPage] handleBlockSelect - END")},k=(0,s.useMemo)(()=>(console.log("[LogAnalysisPage] Recalculating chartDataForView. Selected blocks count:",l.length),l),[l]);return(console.log("[LogAnalysisPage] Rendering. isLoading:",a,"error:",c,"dataChunks count:",e.length,"selectedBlocksForChart count:",l.length,"chartDataForView count:",k.length),a)?(0,o.jsx)("div",{children:"加载中..."}):c?(0,o.jsxs)("div",{children:["错误: ",c]}):(0,o.jsxs)("div",{className:"container mx-auto p-4",children:[(0,o.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"日志分析"}),(0,o.jsx)(j,{dataChunks:e,onSelectionChange:e=>{console.log("[LogAnalysisPage] handleBlockSelectionChanged - START. Received selectedBlocks count:",e.length),i(e),console.log("[LogAnalysisPage] handleBlockSelectionChanged - END")},onStartExport:t=>{console.log("[LogAnalysisPage] initiateExportProcess - START. exportIds:",t);let l=e.filter(e=>t.includes(e.block_id));if(0===l.length){x({title:"导出错误",description:"没有找到要导出的数据块。",variant:"destructive"});return}g(l),f(!0)}}),!a&&!c&&e.length>0&&(0,o.jsx)("div",{ref:p,className:"h-full",children:u?(0,o.jsx)(B,{dataChunks:m,selectedBlockIds:m.map(e=>e.block_id),onBlockSelect:b}):k.length>0?(0,o.jsx)(B,{dataChunks:k,selectedBlockIds:k.map(e=>e.block_id),onBlockSelect:b}):(0,o.jsx)(r.Zp,{className:"h-full flex items-center justify-center",children:(0,o.jsx)(r.Wu,{className:"text-center",children:(0,o.jsx)("p",{className:"text-muted-foreground",children:"请从上方选择数据块以在图表中显示。"})})})})]})}},91514:()=>{},92913:(e,t,l)=>{Promise.resolve().then(l.t.bind(l,60140,23)),Promise.resolve().then(l.t.bind(l,18946,23)),Promise.resolve().then(l.t.bind(l,74178,23)),Promise.resolve().then(l.t.bind(l,6229,23)),Promise.resolve().then(l.t.bind(l,31281,23)),Promise.resolve().then(l.t.bind(l,93833,23)),Promise.resolve().then(l.t.bind(l,97857,23)),Promise.resolve().then(l.t.bind(l,4947,23))}};