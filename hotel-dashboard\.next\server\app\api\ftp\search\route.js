(()=>{var e={};e.id=663,e.ids=[663],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4761:()=>{},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},61295:(e,r,t)=>{"use strict";t.d(r,{WI:()=>f,_K:()=>d});var s=t(16023),o=t(27910);let a=process.env.FTP_HOST||"",n=process.env.FTP_USER||"",i=process.env.FTP_PASSWORD||"",c=process.env.FTP_PORT?parseInt(process.env.FTP_PORT,10):21,l=process.env.FTP_PATH_PREFIX||"/";async function p(){let e=new s.Client;try{return await e.access({host:a,port:c,user:n,password:i,secure:!1}),console.log("FTP connected successfully for operation."),e}catch(r){throw console.error("FTP connection error for operation:",r),e.close(),Error("Failed to connect to FTP server for operation.")}}function u(e){let r=e.replace(/\\/g,"/");return l&&"/"!==l&&(r=`${l.replace(/\/$/,"")}/${r.replace(/^\//,"")}`),r.replace(/\/\//g,"/")}async function d(e=".",r,t=20){let s=await p(),o=u(e),a=[],n=RegExp(r,"i");async function i(e){let r;if(!(a.length>=t)){try{r=await s.list(e)}catch(r){console.error(`Error listing directory ${e} during search:`,r);return}for(let s of r){if(a.length>=t)break;let r=`${e}/${s.name}`.replace(/\/\//g,"/");s.isFile&&n.test(s.name)?a.push({type:"f",name:s.name,size:s.size,date:s.rawModifiedAt,path:r}):s.isDirectory&&"."!==s.name&&".."!==s.name&&await i(r)}}}try{await i(o)}catch(e){throw console.error("Error during recursive search:",e),Error("File search operation failed.")}finally{s.closed||s.close()}return a.slice(0,t)}async function f(e){let r=await p(),t=u(e),s=new o.PassThrough,a=[];try{let e=r.downloadTo(s,t),o=new Promise((e,r)=>{s.on("data",e=>{a.push(Buffer.isBuffer(e)?e:Buffer.from(e))}),s.on("end",()=>{e(Buffer.concat(a))}),s.on("error",e=>{console.error(`Error reading from PassThrough stream for file ${t}:`,e),r(Error(`Failed to read stream for file ${t}.`))})});return await e,await o}catch(e){throw console.error(`Error initiating or during download for file ${t}:`,e),s.destroy(e),Error(`Failed to download file ${t}. Details: ${e.message}`)}finally{r.closed||(r.close(),console.log(`FTP connection closed for ${t}`))}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68313:()=>{},88904:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{GET:()=>l});var o=t(44713),a=t(18198),n=t(84557),i=t(61768),c=t(61295);async function l(e){let{searchParams:r}=new URL(e.url),t=process.env.FTP_BASE_PATH_PRISM||".",s=r.get("basePath")||t,o=r.get("regex");if(!o)return i.NextResponse.json({error:"Missing regex query parameter."},{status:400});try{let e=(await (0,c._K)(s,o,50)).map(e=>{let r=s.endsWith("/")?s:`${s}/`,t=e.path;return e.path.startsWith(r)?t=e.path.substring(r.length):"."!==s||e.path.startsWith("/")||(t=e.path),{name:e.name,path:t,fullPath:e.path,size:e.size,type:e.type}}),r={files:e,total:e.length,limit:50,offset:0};return i.NextResponse.json(r)}catch(e){return console.error("FTP search API error:",e),i.NextResponse.json({error:"Failed to search files on FTP server.",details:e.message},{status:500})}finally{}}let p=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/ftp/search/route",pathname:"/api/ftp/search",filename:"route",bundlePath:"app/api/ftp/search/route"},resolvedPagePath:"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\api\\ftp\\search\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:u,workUnitAsyncStorage:d,serverHooks:f}=p;function h(){return(0,n.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:d})}},91645:e=>{"use strict";e.exports=require("net")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[557,716],()=>t(88904));module.exports=s})();