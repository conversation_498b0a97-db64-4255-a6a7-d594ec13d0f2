# 面形数据查询开发者文档

## 1. 概述

本文档为面形数据查询功能的开发者提供技术细节、API说明、架构设计和扩展指南。该功能允许用户通过FTP服务器搜索和下载大/小棱镜的面形数据。

## 2. 技术架构

### 2.1 前端

*   **框架**: Next.js (集成于 `hotel-dashboard` 项目)
*   **UI库**: shadcn/ui, Tailwind CSS
*   **核心组件**:
    *   [`hotel-dashboard/app/(dashboard)/surface-data-query/page.tsx`](hotel-dashboard/app/(dashboard)/surface-data-query/page.tsx:1): 功能主页面，管理状态和协调子组件。
    *   [`hotel-dashboard/components/surface-data-query/SearchBar.tsx`](hotel-dashboard/components/surface-data-query/SearchBar.tsx:1): 负责接收用户输入的一个或多个SN（每行一个，通过多行文本区域输入）。**默认对每一行输入启用正则表达式匹配**。触发搜索API调用。
    *   [`hotel-dashboard/components/surface-data-query/ResultsDisplay.tsx`](hotel-dashboard/components/surface-data-query/ResultsDisplay.tsx:1): 展示从API获取的搜索结果（结果上限已提升至50条），处理文件选择、下载请求（单文件或批量ZIP）以及触发文件预览。
    *   [`hotel-dashboard/components/surface-data-query/PointCloudPreviewModal.tsx`](hotel-dashboard/components/surface-data-query/PointCloudPreviewModal.tsx:1): 以模态框形式展示点云数据文件的二维渲染预览图，支持缩放和平移。
*   **状态管理**: React Hooks (`useState`, `useEffect`)。
*   **数据类型**: 定义在 [`hotel-dashboard/types/surface-data.ts`](hotel-dashboard/types/surface-data.ts:1) (例如 `SurfaceFile`, `SearchResult`)。
*   **核心工具**:
    *   [`hotel-dashboard/utils/pointCloudUtils.ts`](hotel-dashboard/utils/pointCloudUtils.ts:1): 提供点云数据解析、边界计算、颜色梯度生成和数据采样等功能。

### 2.2 后端 (API)

*   **框架**: Next.js API Routes
*   **核心API端点**:
    *   `POST /api/ftp/search`: 处理文件搜索请求。
    *   `GET /api/ftp/download`: 处理单文件下载和批量文件打包下载（ZIP）请求。
    *   `POST /api/surface-data/preview`: 处理单个点云文件的预览图生成请求。
*   **FTP交互**: 通过 [`hotel-dashboard/lib/ftpClient.ts`](hotel-dashboard/lib/ftpClient.ts:1) 模块与FTP服务器通信。该模块封装了FTP连接、列表、下载等操作。
*   **安全**: FTP凭证通过环境变量进行配置和管理，不在客户端暴露。

### 2.3 数据流

1.  **搜索**:
    *   用户在 [`SearchBar.tsx`](hotel-dashboard/components/surface-data-query/SearchBar.tsx:1) 的多行文本框中输入一个或多个SN（每行一个），并点击搜索。
    *   [`page.tsx`](hotel-dashboard/app/(dashboard)/surface-data-query/page.tsx:1) 将多行输入处理成SN数组，调用 `/api/ftp/search` API，传递该SN数组。
    *   [`/api/ftp/search/route.ts`](hotel-dashboard/app/api/ftp/search/route.ts:1) 接收SN数组，对数组中的每个SN（作为独立的正则表达式）执行文件列表和筛选。
    *   API返回所有匹配的文件列表 (结果上限已提升至 **50条**) 给前端。
    *   [`ResultsDisplay.tsx`](hotel-dashboard/components/surface-data-query/ResultsDisplay.tsx:1) 渲染结果。
2.  **单文件下载**:
    *   用户在 [`ResultsDisplay.tsx`](hotel-dashboard/components/surface-data-query/ResultsDisplay.tsx:1) 点击单个文件的下载按钮。
    *   前端直接请求 `/api/ftp/download?file=[filepath]`。
    *   [`/api/ftp/download/route.ts`](hotel-dashboard/app/api/ftp/download/route.ts:1) 从FTP服务器下载指定文件，并将其流式传输给客户端。
3.  **批量下载 (ZIP)**:
    *   用户在 [`ResultsDisplay.tsx`](hotel-dashboard/components/surface-data-query/ResultsDisplay.tsx:1) 选择多个文件并点击批量下载按钮。
    *   前端请求 `/api/ftp/download?files=[filepath1],[filepath2],...`。
    *   [`/api/ftp/download/route.ts`](hotel-dashboard/app/api/ftp/download/route.ts:1) 逐个从FTP下载选中的文件，使用 `archiver` 等库将它们实时打包成ZIP流，并传输给客户端。
4.  **文件预览**:
    *   用户在 [`ResultsDisplay.tsx`](hotel-dashboard/components/surface-data-query/ResultsDisplay.tsx:1) 点击单个文件的预览按钮。
    *   [`PointCloudPreviewModal.tsx`](hotel-dashboard/components/surface-data-query/PointCloudPreviewModal.tsx:1) 组件被触发并显示。
    *   模态框调用 `/api/surface-data/preview` API，传递文件的完整路径。
    *   [`/api/surface-data/preview/route.ts`](hotel-dashboard/app/api/surface-data/preview/route.ts:1) 接收请求，执行以下操作：
        1.  从FTP下载文件到内存缓冲区。
        2.  根据文件扩展名（`.zip`, `.gz`, `.xyz`）进行解压。
        3.  解析`.xyz`数据，并对大数据集进行**采样**（上限 `150,000` 点）。
        4.  使用 `node-canvas` 将点云数据渲染成二维俯视图（Z轴用颜色表示）。
        5.  返回PNG图像的Base64编码字符串。
    *   前端接收图像数据并在模态框中显示，用户可以通过 `react-zoom-pan-pinch` 库进行缩放和平移。

## 3. API接口文档

### 3.1 搜索接口

*   **Endpoint**: `POST /api/ftp/search`
*   **Method**: `POST`
*   **Request Body**:
    ```json
    {
      "sns": ["YOUR_SERIAL_NUMBER_OR_REGEX_1", "YOUR_SERIAL_NUMBER_OR_REGEX_2"]
    }
    ```
*   **`sns` (string[], required)**: 包含一个或多个产品序列号/正则表达式的数组。每个字符串将作为独立的搜索条件。
*   **Success Response (200 OK)**:
    ```json
    {
      "files": [
        {
          "name": "SN123_data_file1.dat",
          "size": 1024, // bytes
          "date": "2025-06-12T10:00:00.000Z", // ISO 8601 date string
          "type": "f" // 'f' for file, 'd' for directory (though typically only files are returned)
        },
        // ... more files (max 50)
      ]
    }
    ```
*   **Error Responses**:
    *   `400 Bad Request`: 请求体无效或缺少 `sns` 数组，或 `sns` 数组为空。
    *   `500 Internal Server Error`: FTP连接错误、搜索执行错误或其他服务器端问题。

### 3.2 下载接口

*   **Endpoint**: `GET /api/ftp/download`
*   **Method**: `GET`
*   **Query Parameters**:
    *   `file` (string, optional): 单个文件的完整路径 (相对于FTP根目录)。
    *   `files` (string, optional): 多个文件路径的逗号分隔列表，用于批量ZIP下载。
    *   **Note**: 必须提供 `file` 或 `files` 中的一个。
*   **Success Response (200 OK)**:
    *   **单文件**: 文件内容流。`Content-Type` 根据文件类型设置 (e.g., `application/octet-stream`)。`Content-Disposition` 设置为 `attachment; filename="[original_filename]"`.
    *   **批量下载**: ZIP文件流。`Content-Type` 设置为 `application/zip`。`Content-Disposition` 设置为 `attachment; filename="surface_data_export_YYYYMMDDHHMMSS.zip"`.
*   **Error Responses**:
    *   `400 Bad Request`: 未提供 `file` 或 `files` 参数，或参数格式错误。
    *   `404 Not Found`: 请求的文件在FTP服务器上未找到。
    *   `500 Internal Server Error`: FTP连接错误、文件读取错误、ZIP打包错误或其他服务器端问题。

### 3.3 文件预览接口

*   **Endpoint**: `POST /api/surface-data/preview`
*   **Method**: `POST`
*   **Request Body**:
    ```json
    {
      "filePath": "/path/to/your/file.xyz.zip"
    }
    ```
*   **`filePath` (string, required)**: 要预览的文件的完整路径 (相对于FTP根目录)。支持 `.xyz`, `.gz`, `.zip` 格式。
*   **Success Response (200 OK)**:
    ```json
    {
      "success": true,
      "imageData": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUg..." // Base64 encoded PNG image
    }
    ```
*   **Error Responses**:
    *   `400 Bad Request`: 请求体无效、缺少 `filePath`、文件类型不支持或文件内容无法解析。
    *   `413 Payload Too Large`: 文件大小超过服务器限制（当前为50MB）。
    *   `500 Internal Server Error`: FTP下载错误、图像渲染错误等服务器端问题。
    *   `504 Gateway Timeout`: 图像渲染操作超时（当前为30秒）。

## 4. 环境配置指南

### 4.1 FTP服务器凭证

FTP服务器的连接信息和凭证通过环境变量进行配置，以确保安全。这些变量在服务器端使用，不会暴露给客户端。

在 `hotel-dashboard` 项目的根目录下创建或修改 `.env.local` 文件 (如果不存在，可以从 [`hotel-dashboard/.env.local.example`](hotel-dashboard/.env.local.example:1) 复制并重命名)：

```env
FTP_HOST='************'
FTP_USER='BiUser'
FTP_PASSWORD='o41Gkwvo4WRVIKKP'
FTP_PATH='/home' # 搜索的起始路径
```

*   `FTP_HOST`: FTP服务器的主机名或IP地址。
*   `FTP_USER`: FTP登录用户名。
*   `FTP_PASSWORD`: FTP登录密码。
*   `FTP_PATH`: FTP服务器上开始搜索文件的基础路径。

### 4.2 开发环境

1.  确保已安装 Node.js 和 pnpm。
2.  克隆 `hotel-dashboard` 项目仓库。
3.  在项目根目录运行 `pnpm install` 安装依赖。
4.  配置好上述 `.env.local` 文件。
5.  运行 `pnpm dev` 启动开发服务器。
6.  在浏览器中访问面形数据查询页面 (通常在 `http://localhost:3000/surface-data-query`)。

## 5. 代码结构说明

*   **`hotel-dashboard/app/(dashboard)/surface-data-query/page.tsx`**:
    *   主页面组件。
    *   管理搜索状态（加载中、错误、结果）。
    *   处理 `SearchBar` 的搜索请求，调用API。
    *   将搜索结果和下载处理函数传递给 `ResultsDisplay`。
*   **`hotel-dashboard/components/surface-data-query/`**:
    *   **`SearchBar.tsx`**:
        *   包含一个多行文本输入框（Textarea）用于输入一个或多个SN，以及一个搜索按钮。
        *   **正则表达式开关默认开启**，并应用于每一行输入。
        *   通过props回调将解析后的SN数组（每个元素代表一行输入）传递给父组件 (`page.tsx`)。
    *   **`ResultsDisplay.tsx`**:
        *   接收文件列表并渲染。
        *   处理文件选择（复选框）。
        *   处理单文件下载和批量ZIP下载请求，通常通过调用父组件传递的函数或直接构造下载URL。
*   **`hotel-dashboard/app/api/ftp/`**:
    *   **`search/route.ts`**:
        *   处理搜索请求。
        *   接收包含一个或多个SN（正则表达式）的数组。
        *   对数组中的每个SN，使用 `ftpClient` 连接FTP，执行列表和正则匹配。
        *   合并所有查询的结果，并限制总返回数量（例如，50条）。
        *   返回文件列表。
    *   **`download/route.ts`**:
        *   处理下载请求。
        *   根据查询参数（`file` 或 `files`）执行单文件下载或批量ZIP打包。
        *   使用 `ftpClient` 下载文件。
        *   使用 `archiver` (或类似库) 创建ZIP流。
*   **`hotel-dashboard/lib/ftpClient.ts`**:
    *   封装了与FTP服务器交互的逻辑 (使用 `basic-ftp` 或类似库)。
    *   提供连接、列出文件、下载文件等方法。
    *   从环境变量读取FTP配置。
*   **`hotel-dashboard/types/surface-data.ts`**:
    *   定义了与面形数据查询功能相关的TypeScript类型，如 `SurfaceFile`, `ApiSearchResponse` 等。

## 6. 扩展开发指南

### 6.1 添加新的搜索条件

如果需要基于文件名以外的条件（例如修改日期范围）进行搜索：

1.  **前端**: 在 [`SearchBar.tsx`](hotel-dashboard/components/surface-data-query/SearchBar.tsx:1) 中添加新的输入字段。
2.  **API (`search/route.ts`)**:
    *   修改请求体以接受新的搜索参数。
    *   更新FTP文件列表后的筛选逻辑，以包含新的条件。
    *   注意：FTP协议本身对元数据搜索的支持有限，复杂的筛选可能需要在获取完整列表后在服务器端进行。

### 6.2 修改结果显示

如果需要显示更多文件信息或更改显示格式：

1.  **API (`search/route.ts`)**: 确保 `ftpClient` 返回的列表信息包含所需数据。
2.  **前端 (`ResultsDisplay.tsx`)**: 修改渲染逻辑以展示新信息。
3.  **类型 (`surface-data.ts`)**: 更新相关类型定义。

### 6.3 优化性能

*   **FTP列表**: 对于非常大的目录，FTP列表操作可能较慢。考虑是否可以优化 `FTP_PATH` 或引入更深层级的路径参数。
*   **ZIP打包**: 对于大量或非常大的文件，服务器端ZIP打包可能消耗较多CPU和内存。监控服务器性能，必要时考虑流式处理的优化或限制批量下载的大小/数量。
*   **文件预览**:
    *   **数据采样**: 后端API已实现点云数据采样，将用于渲染的点数限制在 `150,000` 以内，防止因数据量过大导致服务器超时或浏览器卡顿。
    *   **内存解压**: 对 `.zip` 文件的处理已优化为在内存中进行，避免了临时文件的磁盘I/O，提升了处理速度。
*   **前端渲染**: 对于大量搜索结果（即使限制为50条），确保高效渲染，可以使用虚拟列表等技术（如果未来需要显示更多条目）。

### 6.4 安全增强

*   **输入验证**: 对所有用户输入（特别是正则表达式）进行严格的服务器端验证，防止潜在的注入或滥用。
*   **错误处理**: 确保所有API端点都有健壮的错误处理和日志记录。
*   **依赖更新**: 定期更新FTP客户端库、ZIP库和其他依赖项，以修补已知的安全漏洞。

## 7. 故障排除和常见问题

**P1: FTP连接失败。**
S1:
    *   检查 `.env.local` 中的FTP凭证 (`FTP_HOST`, `FTP_USER`, `FTP_PASSWORD`, `FTP_PATH`) 是否正确。
    *   确认FTP服务器 ([`************`](:0)) 是否可访问，网络防火墙是否允许从应用服务器到FTP服务器的连接。
    *   查看API服务器日志以获取详细错误信息。

**P2: 搜索返回空结果，但文件确实存在。**
S2:
    *   检查传递给搜索API的正则表达式是否正确。
    *   确认 `FTP_PATH` 配置是否指向包含目标文件的正确父目录。
    *   在 [`search/route.ts`](hotel-dashboard/app/api/ftp/search/route.ts:1) 中添加日志，打印FTP客户端列出的原始文件列表和正则表达式匹配过程，以进行调试。

**P3: 文件下载中断或下载的文件损坏。**
S3:
    *   **单文件**: 检查 [`download/route.ts`](hotel-dashboard/app/api/ftp/download/route.ts:1) 中文件流处理是否正确。网络问题也可能导致中断。
    *   **批量ZIP**: 检查ZIP打包库 (`archiver`) 的使用是否正确，是否有足够的磁盘空间（如果需要临时存储）和内存。流式打包错误可能导致ZIP损坏。查看API服务器日志。

**P4: 正则表达式在本地测试有效，但在应用中无效。**
S4:
    *   确保前端传递给后端的正则表达式字符串没有被意外转义或修改。
    *   确认后端使用的FTP客户端库或自定义匹配逻辑所支持的正则表达式方言与测试环境一致。

---
*本文档最后更新于：2025-06-12 12:13 PM*