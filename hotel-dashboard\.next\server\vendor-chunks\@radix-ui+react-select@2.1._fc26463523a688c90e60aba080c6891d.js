"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-select@2.1._fc26463523a688c90e60aba080c6891d";
exports.ids = ["vendor-chunks/@radix-ui+react-select@2.1._fc26463523a688c90e60aba080c6891d"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-select@2.1._fc26463523a688c90e60aba080c6891d/node_modules/@radix-ui/react-select/dist/index.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-select@2.1._fc26463523a688c90e60aba080c6891d/node_modules/@radix-ui/react-select/dist/index.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Group: () => (/* binding */ Group),\n/* harmony export */   Icon: () => (/* binding */ Icon),\n/* harmony export */   Item: () => (/* binding */ Item),\n/* harmony export */   ItemIndicator: () => (/* binding */ ItemIndicator),\n/* harmony export */   ItemText: () => (/* binding */ ItemText),\n/* harmony export */   Label: () => (/* binding */ Label),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   ScrollDownButton: () => (/* binding */ ScrollDownButton),\n/* harmony export */   ScrollUpButton: () => (/* binding */ ScrollUpButton),\n/* harmony export */   Select: () => (/* binding */ Select),\n/* harmony export */   SelectArrow: () => (/* binding */ SelectArrow),\n/* harmony export */   SelectContent: () => (/* binding */ SelectContent),\n/* harmony export */   SelectGroup: () => (/* binding */ SelectGroup),\n/* harmony export */   SelectIcon: () => (/* binding */ SelectIcon),\n/* harmony export */   SelectItem: () => (/* binding */ SelectItem),\n/* harmony export */   SelectItemIndicator: () => (/* binding */ SelectItemIndicator),\n/* harmony export */   SelectItemText: () => (/* binding */ SelectItemText),\n/* harmony export */   SelectLabel: () => (/* binding */ SelectLabel),\n/* harmony export */   SelectPortal: () => (/* binding */ SelectPortal),\n/* harmony export */   SelectScrollDownButton: () => (/* binding */ SelectScrollDownButton),\n/* harmony export */   SelectScrollUpButton: () => (/* binding */ SelectScrollUpButton),\n/* harmony export */   SelectSeparator: () => (/* binding */ SelectSeparator),\n/* harmony export */   SelectTrigger: () => (/* binding */ SelectTrigger),\n/* harmony export */   SelectValue: () => (/* binding */ SelectValue),\n/* harmony export */   SelectViewport: () => (/* binding */ SelectViewport),\n/* harmony export */   Separator: () => (/* binding */ Separator),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   Value: () => (/* binding */ Value),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createSelectScope: () => (/* binding */ createSelectScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_number__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @radix-ui/number */ \"(ssr)/./node_modules/.pnpm/@radix-ui+number@1.1.0/node_modules/@radix-ui/number/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-collection@_5a9538d81cc9a82cfec21341eafe5f60/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_2a0e526a8f7e7aada080206d385bb572/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_4fe40d510edca7ae4ca9c92afeb1ae6d/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1_033f2d9864c311ebc46e3d65a72ec4b7/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable_e0654b1a402476d2bc9d17a4916b81c5/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-guard_269ed620171cebd49025512d22fad1ff/node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-scope_f4aa807b3e1605c991a664dd895fa0ef/node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.0_@types+react@19.1.8_react@19.1.0/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2._735ba650859fcd3c1e79e21390d513c8/node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1._eb5128f7adaf3128c1076c6b6e93c13d/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_997b35f2e2aa9d3174fc03a0f79e437b/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.1.1_@types+react@19.1.8_react@19.1.0/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callbac_2dc63ba6354ec7ef7d955ba47145829a/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_84b05e8d8acde331bf79519153011e46/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-_7d9c308966eafc0f645092b629b133a3/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-previou_f62eb26ebf07111fa37167228db23a06/node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-visually-hi_da21897b5bc909f873add44d7cbc4eba/node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! aria-hidden */ \"(ssr)/./node_modules/.pnpm/aria-hidden@1.2.6/node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/./node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@19.1.8_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Arrow,Content,Group,Icon,Item,ItemIndicator,ItemText,Label,Portal,Root,ScrollDownButton,ScrollUpButton,Select,SelectArrow,SelectContent,SelectGroup,SelectIcon,SelectItem,SelectItemIndicator,SelectItemText,SelectLabel,SelectPortal,SelectScrollDownButton,SelectScrollUpButton,SelectSeparator,SelectTrigger,SelectValue,SelectViewport,Separator,Trigger,Value,Viewport,createSelectScope auto */ // packages/react/select/src/Select.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar OPEN_KEYS = [\n    \" \",\n    \"Enter\",\n    \"ArrowUp\",\n    \"ArrowDown\"\n];\nvar SELECTION_KEYS = [\n    \" \",\n    \"Enter\"\n];\nvar SELECT_NAME = \"Select\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(SELECT_NAME);\nvar [createSelectContext, createSelectScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__.createContextScope)(SELECT_NAME, [\n    createCollectionScope,\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.createPopperScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.createPopperScope)();\nvar [SelectProvider, useSelectContext] = createSelectContext(SELECT_NAME);\nvar [SelectNativeOptionsProvider, useSelectNativeOptionsContext] = createSelectContext(SELECT_NAME);\nvar Select = (props)=>{\n    const { __scopeSelect, children, open: openProp, defaultOpen, onOpenChange, value: valueProp, defaultValue, onValueChange, dir, name, autoComplete, disabled, required, form } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const [trigger, setTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [valueNode, setValueNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [valueNodeHasChildren, setValueNodeHasChildren] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__.useDirection)(dir);\n    const [open = false, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen,\n        onChange: onOpenChange\n    });\n    const [value, setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n        prop: valueProp,\n        defaultProp: defaultValue,\n        onChange: onValueChange\n    });\n    const triggerPointerDownPosRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFormControl = trigger ? form || !!trigger.closest(\"form\") : true;\n    const [nativeOptionsSet, setNativeOptionsSet] = react__WEBPACK_IMPORTED_MODULE_0__.useState(/* @__PURE__ */ new Set());\n    const nativeSelectKey = Array.from(nativeOptionsSet).map((option)=>option.props.value).join(\";\");\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(SelectProvider, {\n            required,\n            scope: __scopeSelect,\n            trigger,\n            onTriggerChange: setTrigger,\n            valueNode,\n            onValueNodeChange: setValueNode,\n            valueNodeHasChildren,\n            onValueNodeHasChildrenChange: setValueNodeHasChildren,\n            contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)(),\n            value,\n            onValueChange: setValue,\n            open,\n            onOpenChange: setOpen,\n            dir: direction,\n            triggerPointerDownPosRef,\n            disabled,\n            children: [\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Provider, {\n                    scope: __scopeSelect,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectNativeOptionsProvider, {\n                        scope: props.__scopeSelect,\n                        onNativeOptionAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n                            \"Select.useCallback\": (option)=>{\n                                setNativeOptionsSet({\n                                    \"Select.useCallback\": (prev)=>new Set(prev).add(option)\n                                }[\"Select.useCallback\"]);\n                            }\n                        }[\"Select.useCallback\"], []),\n                        onNativeOptionRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n                            \"Select.useCallback\": (option)=>{\n                                setNativeOptionsSet({\n                                    \"Select.useCallback\": (prev)=>{\n                                        const optionsSet = new Set(prev);\n                                        optionsSet.delete(option);\n                                        return optionsSet;\n                                    }\n                                }[\"Select.useCallback\"]);\n                            }\n                        }[\"Select.useCallback\"], []),\n                        children\n                    })\n                }),\n                isFormControl ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(BubbleSelect, {\n                    \"aria-hidden\": true,\n                    required,\n                    tabIndex: -1,\n                    name,\n                    autoComplete,\n                    value,\n                    onChange: (event)=>setValue(event.target.value),\n                    disabled,\n                    form,\n                    children: [\n                        value === void 0 ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"option\", {\n                            value: \"\"\n                        }) : null,\n                        Array.from(nativeOptionsSet)\n                    ]\n                }, nativeSelectKey) : null\n            ]\n        })\n    });\n};\nSelect.displayName = SELECT_NAME;\nvar TRIGGER_NAME = \"SelectTrigger\";\nvar SelectTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, disabled = false, ...triggerProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(TRIGGER_NAME, __scopeSelect);\n    const isDisabled = context.disabled || disabled;\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, context.onTriggerChange);\n    const getItems = useCollection(__scopeSelect);\n    const pointerTypeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"touch\");\n    const [searchRef, handleTypeaheadSearch, resetTypeahead] = useTypeaheadSearch({\n        \"SelectTrigger.useTypeaheadSearch\": (search)=>{\n            const enabledItems = getItems().filter({\n                \"SelectTrigger.useTypeaheadSearch.enabledItems\": (item)=>!item.disabled\n            }[\"SelectTrigger.useTypeaheadSearch.enabledItems\"]);\n            const currentItem = enabledItems.find({\n                \"SelectTrigger.useTypeaheadSearch.currentItem\": (item)=>item.value === context.value\n            }[\"SelectTrigger.useTypeaheadSearch.currentItem\"]);\n            const nextItem = findNextItem(enabledItems, search, currentItem);\n            if (nextItem !== void 0) {\n                context.onValueChange(nextItem.value);\n            }\n        }\n    }[\"SelectTrigger.useTypeaheadSearch\"]);\n    const handleOpen = (pointerEvent)=>{\n        if (!isDisabled) {\n            context.onOpenChange(true);\n            resetTypeahead();\n        }\n        if (pointerEvent) {\n            context.triggerPointerDownPosRef.current = {\n                x: Math.round(pointerEvent.pageX),\n                y: Math.round(pointerEvent.pageY)\n            };\n        }\n    };\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Anchor, {\n        asChild: true,\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.button, {\n            type: \"button\",\n            role: \"combobox\",\n            \"aria-controls\": context.contentId,\n            \"aria-expanded\": context.open,\n            \"aria-required\": context.required,\n            \"aria-autocomplete\": \"none\",\n            dir: context.dir,\n            \"data-state\": context.open ? \"open\" : \"closed\",\n            disabled: isDisabled,\n            \"data-disabled\": isDisabled ? \"\" : void 0,\n            \"data-placeholder\": shouldShowPlaceholder(context.value) ? \"\" : void 0,\n            ...triggerProps,\n            ref: composedRefs,\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onClick, (event)=>{\n                event.currentTarget.focus();\n                if (pointerTypeRef.current !== \"mouse\") {\n                    handleOpen(event);\n                }\n            }),\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onPointerDown, (event)=>{\n                pointerTypeRef.current = event.pointerType;\n                const target = event.target;\n                if (target.hasPointerCapture(event.pointerId)) {\n                    target.releasePointerCapture(event.pointerId);\n                }\n                if (event.button === 0 && event.ctrlKey === false && event.pointerType === \"mouse\") {\n                    handleOpen(event);\n                    event.preventDefault();\n                }\n            }),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onKeyDown, (event)=>{\n                const isTypingAhead = searchRef.current !== \"\";\n                const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n                if (isTypingAhead && event.key === \" \") return;\n                if (OPEN_KEYS.includes(event.key)) {\n                    handleOpen();\n                    event.preventDefault();\n                }\n            })\n        })\n    });\n});\nSelectTrigger.displayName = TRIGGER_NAME;\nvar VALUE_NAME = \"SelectValue\";\nvar SelectValue = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, className, style, children, placeholder = \"\", ...valueProps } = props;\n    const context = useSelectContext(VALUE_NAME, __scopeSelect);\n    const { onValueNodeHasChildrenChange } = context;\n    const hasChildren = children !== void 0;\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, context.onValueNodeChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectValue.useLayoutEffect\": ()=>{\n            onValueNodeHasChildrenChange(hasChildren);\n        }\n    }[\"SelectValue.useLayoutEffect\"], [\n        onValueNodeHasChildrenChange,\n        hasChildren\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        ...valueProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: \"none\"\n        },\n        children: shouldShowPlaceholder(context.value) ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n            children: placeholder\n        }) : children\n    });\n});\nSelectValue.displayName = VALUE_NAME;\nvar ICON_NAME = \"SelectIcon\";\nvar SelectIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, children, ...iconProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        \"aria-hidden\": true,\n        ...iconProps,\n        ref: forwardedRef,\n        children: children || \"\\u25BC\"\n    });\n});\nSelectIcon.displayName = ICON_NAME;\nvar PORTAL_NAME = \"SelectPortal\";\nvar SelectPortal = (props)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__.Portal, {\n        asChild: true,\n        ...props\n    });\n};\nSelectPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"SelectContent\";\nvar SelectContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useSelectContext(CONTENT_NAME, props.__scopeSelect);\n    const [fragment, setFragment] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectContent.useLayoutEffect\": ()=>{\n            setFragment(new DocumentFragment());\n        }\n    }[\"SelectContent.useLayoutEffect\"], []);\n    if (!context.open) {\n        const frag = fragment;\n        return frag ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentProvider, {\n            scope: props.__scopeSelect,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: props.__scopeSelect,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n                    children: props.children\n                })\n            })\n        }), frag) : null;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentImpl, {\n        ...props,\n        ref: forwardedRef\n    });\n});\nSelectContent.displayName = CONTENT_NAME;\nvar CONTENT_MARGIN = 10;\nvar [SelectContentProvider, useSelectContentContext] = createSelectContext(CONTENT_NAME);\nvar CONTENT_IMPL_NAME = \"SelectContentImpl\";\nvar SelectContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, position = \"item-aligned\", onCloseAutoFocus, onEscapeKeyDown, onPointerDownOutside, //\n    // PopperContent props\n    side, sideOffset, align, alignOffset, arrowPadding, collisionBoundary, collisionPadding, sticky, hideWhenDetached, avoidCollisions, //\n    ...contentProps } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, {\n        \"SelectContentImpl.useComposedRefs[composedRefs]\": (node)=>setContent(node)\n    }[\"SelectContentImpl.useComposedRefs[composedRefs]\"]);\n    const [selectedItem, setSelectedItem] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [selectedItemText, setSelectedItemText] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const getItems = useCollection(__scopeSelect);\n    const [isPositioned, setIsPositioned] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const firstValidItemFoundRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"SelectContentImpl.useEffect\": ()=>{\n            if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_14__.hideOthers)(content);\n        }\n    }[\"SelectContentImpl.useEffect\"], [\n        content\n    ]);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_15__.useFocusGuards)();\n    const focusFirst = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectContentImpl.useCallback[focusFirst]\": (candidates)=>{\n            const [firstItem, ...restItems] = getItems().map({\n                \"SelectContentImpl.useCallback[focusFirst]\": (item)=>item.ref.current\n            }[\"SelectContentImpl.useCallback[focusFirst]\"]);\n            const [lastItem] = restItems.slice(-1);\n            const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n            for (const candidate of candidates){\n                if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n                candidate?.scrollIntoView({\n                    block: \"nearest\"\n                });\n                if (candidate === firstItem && viewport) viewport.scrollTop = 0;\n                if (candidate === lastItem && viewport) viewport.scrollTop = viewport.scrollHeight;\n                candidate?.focus();\n                if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n            }\n        }\n    }[\"SelectContentImpl.useCallback[focusFirst]\"], [\n        getItems,\n        viewport\n    ]);\n    const focusSelectedItem = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectContentImpl.useCallback[focusSelectedItem]\": ()=>focusFirst([\n                selectedItem,\n                content\n            ])\n    }[\"SelectContentImpl.useCallback[focusSelectedItem]\"], [\n        focusFirst,\n        selectedItem,\n        content\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"SelectContentImpl.useEffect\": ()=>{\n            if (isPositioned) {\n                focusSelectedItem();\n            }\n        }\n    }[\"SelectContentImpl.useEffect\"], [\n        isPositioned,\n        focusSelectedItem\n    ]);\n    const { onOpenChange, triggerPointerDownPosRef } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"SelectContentImpl.useEffect\": ()=>{\n            if (content) {\n                let pointerMoveDelta = {\n                    x: 0,\n                    y: 0\n                };\n                const handlePointerMove = {\n                    \"SelectContentImpl.useEffect.handlePointerMove\": (event)=>{\n                        pointerMoveDelta = {\n                            x: Math.abs(Math.round(event.pageX) - (triggerPointerDownPosRef.current?.x ?? 0)),\n                            y: Math.abs(Math.round(event.pageY) - (triggerPointerDownPosRef.current?.y ?? 0))\n                        };\n                    }\n                }[\"SelectContentImpl.useEffect.handlePointerMove\"];\n                const handlePointerUp = {\n                    \"SelectContentImpl.useEffect.handlePointerUp\": (event)=>{\n                        if (pointerMoveDelta.x <= 10 && pointerMoveDelta.y <= 10) {\n                            event.preventDefault();\n                        } else {\n                            if (!content.contains(event.target)) {\n                                onOpenChange(false);\n                            }\n                        }\n                        document.removeEventListener(\"pointermove\", handlePointerMove);\n                        triggerPointerDownPosRef.current = null;\n                    }\n                }[\"SelectContentImpl.useEffect.handlePointerUp\"];\n                if (triggerPointerDownPosRef.current !== null) {\n                    document.addEventListener(\"pointermove\", handlePointerMove);\n                    document.addEventListener(\"pointerup\", handlePointerUp, {\n                        capture: true,\n                        once: true\n                    });\n                }\n                return ({\n                    \"SelectContentImpl.useEffect\": ()=>{\n                        document.removeEventListener(\"pointermove\", handlePointerMove);\n                        document.removeEventListener(\"pointerup\", handlePointerUp, {\n                            capture: true\n                        });\n                    }\n                })[\"SelectContentImpl.useEffect\"];\n            }\n        }\n    }[\"SelectContentImpl.useEffect\"], [\n        content,\n        onOpenChange,\n        triggerPointerDownPosRef\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"SelectContentImpl.useEffect\": ()=>{\n            const close = {\n                \"SelectContentImpl.useEffect.close\": ()=>onOpenChange(false)\n            }[\"SelectContentImpl.useEffect.close\"];\n            window.addEventListener(\"blur\", close);\n            window.addEventListener(\"resize\", close);\n            return ({\n                \"SelectContentImpl.useEffect\": ()=>{\n                    window.removeEventListener(\"blur\", close);\n                    window.removeEventListener(\"resize\", close);\n                }\n            })[\"SelectContentImpl.useEffect\"];\n        }\n    }[\"SelectContentImpl.useEffect\"], [\n        onOpenChange\n    ]);\n    const [searchRef, handleTypeaheadSearch] = useTypeaheadSearch({\n        \"SelectContentImpl.useTypeaheadSearch\": (search)=>{\n            const enabledItems = getItems().filter({\n                \"SelectContentImpl.useTypeaheadSearch.enabledItems\": (item)=>!item.disabled\n            }[\"SelectContentImpl.useTypeaheadSearch.enabledItems\"]);\n            const currentItem = enabledItems.find({\n                \"SelectContentImpl.useTypeaheadSearch.currentItem\": (item)=>item.ref.current === document.activeElement\n            }[\"SelectContentImpl.useTypeaheadSearch.currentItem\"]);\n            const nextItem = findNextItem(enabledItems, search, currentItem);\n            if (nextItem) {\n                setTimeout({\n                    \"SelectContentImpl.useTypeaheadSearch\": ()=>nextItem.ref.current.focus()\n                }[\"SelectContentImpl.useTypeaheadSearch\"]);\n            }\n        }\n    }[\"SelectContentImpl.useTypeaheadSearch\"]);\n    const itemRefCallback = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectContentImpl.useCallback[itemRefCallback]\": (node, value, disabled)=>{\n            const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n            const isSelectedItem = context.value !== void 0 && context.value === value;\n            if (isSelectedItem || isFirstValidItem) {\n                setSelectedItem(node);\n                if (isFirstValidItem) firstValidItemFoundRef.current = true;\n            }\n        }\n    }[\"SelectContentImpl.useCallback[itemRefCallback]\"], [\n        context.value\n    ]);\n    const handleItemLeave = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectContentImpl.useCallback[handleItemLeave]\": ()=>content?.focus()\n    }[\"SelectContentImpl.useCallback[handleItemLeave]\"], [\n        content\n    ]);\n    const itemTextRefCallback = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectContentImpl.useCallback[itemTextRefCallback]\": (node, value, disabled)=>{\n            const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n            const isSelectedItem = context.value !== void 0 && context.value === value;\n            if (isSelectedItem || isFirstValidItem) {\n                setSelectedItemText(node);\n            }\n        }\n    }[\"SelectContentImpl.useCallback[itemTextRefCallback]\"], [\n        context.value\n    ]);\n    const SelectPosition = position === \"popper\" ? SelectPopperPosition : SelectItemAlignedPosition;\n    const popperContentProps = SelectPosition === SelectPopperPosition ? {\n        side,\n        sideOffset,\n        align,\n        alignOffset,\n        arrowPadding,\n        collisionBoundary,\n        collisionPadding,\n        sticky,\n        hideWhenDetached,\n        avoidCollisions\n    } : {};\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentProvider, {\n        scope: __scopeSelect,\n        content,\n        viewport,\n        onViewportChange: setViewport,\n        itemRefCallback,\n        selectedItem,\n        onItemLeave: handleItemLeave,\n        itemTextRefCallback,\n        focusSelectedItem,\n        selectedItemText,\n        position,\n        isPositioned,\n        searchRef,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react_remove_scroll__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n            as: _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_17__.Slot,\n            allowPinchZoom: true,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_18__.FocusScope, {\n                asChild: true,\n                trapped: context.open,\n                onMountAutoFocus: (event)=>{\n                    event.preventDefault();\n                },\n                onUnmountAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(onCloseAutoFocus, (event)=>{\n                    context.trigger?.focus({\n                        preventScroll: true\n                    });\n                    event.preventDefault();\n                }),\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_19__.DismissableLayer, {\n                    asChild: true,\n                    disableOutsidePointerEvents: true,\n                    onEscapeKeyDown,\n                    onPointerDownOutside,\n                    onFocusOutside: (event)=>event.preventDefault(),\n                    onDismiss: ()=>context.onOpenChange(false),\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectPosition, {\n                        role: \"listbox\",\n                        id: context.contentId,\n                        \"data-state\": context.open ? \"open\" : \"closed\",\n                        dir: context.dir,\n                        onContextMenu: (event)=>event.preventDefault(),\n                        ...contentProps,\n                        ...popperContentProps,\n                        onPlaced: ()=>setIsPositioned(true),\n                        ref: composedRefs,\n                        style: {\n                            // flex layout so we can place the scroll buttons properly\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            // reset the outline by default as the content MAY get focused\n                            outline: \"none\",\n                            ...contentProps.style\n                        },\n                        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(contentProps.onKeyDown, (event)=>{\n                            const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                            if (event.key === \"Tab\") event.preventDefault();\n                            if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n                            if ([\n                                \"ArrowUp\",\n                                \"ArrowDown\",\n                                \"Home\",\n                                \"End\"\n                            ].includes(event.key)) {\n                                const items = getItems().filter((item)=>!item.disabled);\n                                let candidateNodes = items.map((item)=>item.ref.current);\n                                if ([\n                                    \"ArrowUp\",\n                                    \"End\"\n                                ].includes(event.key)) {\n                                    candidateNodes = candidateNodes.slice().reverse();\n                                }\n                                if ([\n                                    \"ArrowUp\",\n                                    \"ArrowDown\"\n                                ].includes(event.key)) {\n                                    const currentElement = event.target;\n                                    const currentIndex = candidateNodes.indexOf(currentElement);\n                                    candidateNodes = candidateNodes.slice(currentIndex + 1);\n                                }\n                                setTimeout(()=>focusFirst(candidateNodes));\n                                event.preventDefault();\n                            }\n                        })\n                    })\n                })\n            })\n        })\n    });\n});\nSelectContentImpl.displayName = CONTENT_IMPL_NAME;\nvar ITEM_ALIGNED_POSITION_NAME = \"SelectItemAlignedPosition\";\nvar SelectItemAlignedPosition = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, onPlaced, ...popperProps } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(CONTENT_NAME, __scopeSelect);\n    const [contentWrapper, setContentWrapper] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, {\n        \"SelectItemAlignedPosition.useComposedRefs[composedRefs]\": (node)=>setContent(node)\n    }[\"SelectItemAlignedPosition.useComposedRefs[composedRefs]\"]);\n    const getItems = useCollection(__scopeSelect);\n    const shouldExpandOnScrollRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const shouldRepositionRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);\n    const { viewport, selectedItem, selectedItemText, focusSelectedItem } = contentContext;\n    const position = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectItemAlignedPosition.useCallback[position]\": ()=>{\n            if (context.trigger && context.valueNode && contentWrapper && content && viewport && selectedItem && selectedItemText) {\n                const triggerRect = context.trigger.getBoundingClientRect();\n                const contentRect = content.getBoundingClientRect();\n                const valueNodeRect = context.valueNode.getBoundingClientRect();\n                const itemTextRect = selectedItemText.getBoundingClientRect();\n                if (context.dir !== \"rtl\") {\n                    const itemTextOffset = itemTextRect.left - contentRect.left;\n                    const left = valueNodeRect.left - itemTextOffset;\n                    const leftDelta = triggerRect.left - left;\n                    const minContentWidth = triggerRect.width + leftDelta;\n                    const contentWidth = Math.max(minContentWidth, contentRect.width);\n                    const rightEdge = window.innerWidth - CONTENT_MARGIN;\n                    const clampedLeft = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_20__.clamp)(left, [\n                        CONTENT_MARGIN,\n                        // Prevents the content from going off the starting edge of the\n                        // viewport. It may still go off the ending edge, but this can be\n                        // controlled by the user since they may want to manage overflow in a\n                        // specific way.\n                        // https://github.com/radix-ui/primitives/issues/2049\n                        Math.max(CONTENT_MARGIN, rightEdge - contentWidth)\n                    ]);\n                    contentWrapper.style.minWidth = minContentWidth + \"px\";\n                    contentWrapper.style.left = clampedLeft + \"px\";\n                } else {\n                    const itemTextOffset = contentRect.right - itemTextRect.right;\n                    const right = window.innerWidth - valueNodeRect.right - itemTextOffset;\n                    const rightDelta = window.innerWidth - triggerRect.right - right;\n                    const minContentWidth = triggerRect.width + rightDelta;\n                    const contentWidth = Math.max(minContentWidth, contentRect.width);\n                    const leftEdge = window.innerWidth - CONTENT_MARGIN;\n                    const clampedRight = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_20__.clamp)(right, [\n                        CONTENT_MARGIN,\n                        Math.max(CONTENT_MARGIN, leftEdge - contentWidth)\n                    ]);\n                    contentWrapper.style.minWidth = minContentWidth + \"px\";\n                    contentWrapper.style.right = clampedRight + \"px\";\n                }\n                const items = getItems();\n                const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                const itemsHeight = viewport.scrollHeight;\n                const contentStyles = window.getComputedStyle(content);\n                const contentBorderTopWidth = parseInt(contentStyles.borderTopWidth, 10);\n                const contentPaddingTop = parseInt(contentStyles.paddingTop, 10);\n                const contentBorderBottomWidth = parseInt(contentStyles.borderBottomWidth, 10);\n                const contentPaddingBottom = parseInt(contentStyles.paddingBottom, 10);\n                const fullContentHeight = contentBorderTopWidth + contentPaddingTop + itemsHeight + contentPaddingBottom + contentBorderBottomWidth;\n                const minContentHeight = Math.min(selectedItem.offsetHeight * 5, fullContentHeight);\n                const viewportStyles = window.getComputedStyle(viewport);\n                const viewportPaddingTop = parseInt(viewportStyles.paddingTop, 10);\n                const viewportPaddingBottom = parseInt(viewportStyles.paddingBottom, 10);\n                const topEdgeToTriggerMiddle = triggerRect.top + triggerRect.height / 2 - CONTENT_MARGIN;\n                const triggerMiddleToBottomEdge = availableHeight - topEdgeToTriggerMiddle;\n                const selectedItemHalfHeight = selectedItem.offsetHeight / 2;\n                const itemOffsetMiddle = selectedItem.offsetTop + selectedItemHalfHeight;\n                const contentTopToItemMiddle = contentBorderTopWidth + contentPaddingTop + itemOffsetMiddle;\n                const itemMiddleToContentBottom = fullContentHeight - contentTopToItemMiddle;\n                const willAlignWithoutTopOverflow = contentTopToItemMiddle <= topEdgeToTriggerMiddle;\n                if (willAlignWithoutTopOverflow) {\n                    const isLastItem = items.length > 0 && selectedItem === items[items.length - 1].ref.current;\n                    contentWrapper.style.bottom = \"0px\";\n                    const viewportOffsetBottom = content.clientHeight - viewport.offsetTop - viewport.offsetHeight;\n                    const clampedTriggerMiddleToBottomEdge = Math.max(triggerMiddleToBottomEdge, selectedItemHalfHeight + // viewport might have padding bottom, include it to avoid a scrollable viewport\n                    (isLastItem ? viewportPaddingBottom : 0) + viewportOffsetBottom + contentBorderBottomWidth);\n                    const height = contentTopToItemMiddle + clampedTriggerMiddleToBottomEdge;\n                    contentWrapper.style.height = height + \"px\";\n                } else {\n                    const isFirstItem = items.length > 0 && selectedItem === items[0].ref.current;\n                    contentWrapper.style.top = \"0px\";\n                    const clampedTopEdgeToTriggerMiddle = Math.max(topEdgeToTriggerMiddle, contentBorderTopWidth + viewport.offsetTop + // viewport might have padding top, include it to avoid a scrollable viewport\n                    (isFirstItem ? viewportPaddingTop : 0) + selectedItemHalfHeight);\n                    const height = clampedTopEdgeToTriggerMiddle + itemMiddleToContentBottom;\n                    contentWrapper.style.height = height + \"px\";\n                    viewport.scrollTop = contentTopToItemMiddle - topEdgeToTriggerMiddle + viewport.offsetTop;\n                }\n                contentWrapper.style.margin = `${CONTENT_MARGIN}px 0`;\n                contentWrapper.style.minHeight = minContentHeight + \"px\";\n                contentWrapper.style.maxHeight = availableHeight + \"px\";\n                onPlaced?.();\n                requestAnimationFrame({\n                    \"SelectItemAlignedPosition.useCallback[position]\": ()=>shouldExpandOnScrollRef.current = true\n                }[\"SelectItemAlignedPosition.useCallback[position]\"]);\n            }\n        }\n    }[\"SelectItemAlignedPosition.useCallback[position]\"], [\n        getItems,\n        context.trigger,\n        context.valueNode,\n        contentWrapper,\n        content,\n        viewport,\n        selectedItem,\n        selectedItemText,\n        context.dir,\n        onPlaced\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectItemAlignedPosition.useLayoutEffect\": ()=>position()\n    }[\"SelectItemAlignedPosition.useLayoutEffect\"], [\n        position\n    ]);\n    const [contentZIndex, setContentZIndex] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectItemAlignedPosition.useLayoutEffect\": ()=>{\n            if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n        }\n    }[\"SelectItemAlignedPosition.useLayoutEffect\"], [\n        content\n    ]);\n    const handleScrollButtonChange = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectItemAlignedPosition.useCallback[handleScrollButtonChange]\": (node)=>{\n            if (node && shouldRepositionRef.current === true) {\n                position();\n                focusSelectedItem?.();\n                shouldRepositionRef.current = false;\n            }\n        }\n    }[\"SelectItemAlignedPosition.useCallback[handleScrollButtonChange]\"], [\n        position,\n        focusSelectedItem\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectViewportProvider, {\n        scope: __scopeSelect,\n        contentWrapper,\n        shouldExpandOnScrollRef,\n        onScrollButtonChange: handleScrollButtonChange,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n            ref: setContentWrapper,\n            style: {\n                display: \"flex\",\n                flexDirection: \"column\",\n                position: \"fixed\",\n                zIndex: contentZIndex\n            },\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                ...popperProps,\n                ref: composedRefs,\n                style: {\n                    // When we get the height of the content, it includes borders. If we were to set\n                    // the height without having `boxSizing: 'border-box'` it would be too big.\n                    boxSizing: \"border-box\",\n                    // We need to ensure the content doesn't get taller than the wrapper\n                    maxHeight: \"100%\",\n                    ...popperProps.style\n                }\n            })\n        })\n    });\n});\nSelectItemAlignedPosition.displayName = ITEM_ALIGNED_POSITION_NAME;\nvar POPPER_POSITION_NAME = \"SelectPopperPosition\";\nvar SelectPopperPosition = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, align = \"start\", collisionPadding = CONTENT_MARGIN, ...popperProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Content, {\n        ...popperScope,\n        ...popperProps,\n        ref: forwardedRef,\n        align,\n        collisionPadding,\n        style: {\n            // Ensure border-box for floating-ui calculations\n            boxSizing: \"border-box\",\n            ...popperProps.style,\n            // re-namespace exposed content custom properties\n            ...{\n                \"--radix-select-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                \"--radix-select-content-available-width\": \"var(--radix-popper-available-width)\",\n                \"--radix-select-content-available-height\": \"var(--radix-popper-available-height)\",\n                \"--radix-select-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                \"--radix-select-trigger-height\": \"var(--radix-popper-anchor-height)\"\n            }\n        }\n    });\n});\nSelectPopperPosition.displayName = POPPER_POSITION_NAME;\nvar [SelectViewportProvider, useSelectViewportContext] = createSelectContext(CONTENT_NAME, {});\nvar VIEWPORT_NAME = \"SelectViewport\";\nvar SelectViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, nonce, ...viewportProps } = props;\n    const contentContext = useSelectContentContext(VIEWPORT_NAME, __scopeSelect);\n    const viewportContext = useSelectViewportContext(VIEWPORT_NAME, __scopeSelect);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, contentContext.onViewportChange);\n    const prevScrollTopRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"style\", {\n                dangerouslySetInnerHTML: {\n                    __html: `[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}`\n                },\n                nonce\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: __scopeSelect,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                    \"data-radix-select-viewport\": \"\",\n                    role: \"presentation\",\n                    ...viewportProps,\n                    ref: composedRefs,\n                    style: {\n                        // we use position: 'relative' here on the `viewport` so that when we call\n                        // `selectedItem.offsetTop` in calculations, the offset is relative to the viewport\n                        // (independent of the scrollUpButton).\n                        position: \"relative\",\n                        flex: 1,\n                        // Viewport should only be scrollable in the vertical direction.\n                        // This won't work in vertical writing modes, so we'll need to\n                        // revisit this if/when that is supported\n                        // https://developer.chrome.com/blog/vertical-form-controls\n                        overflow: \"hidden auto\",\n                        ...viewportProps.style\n                    },\n                    onScroll: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(viewportProps.onScroll, (event)=>{\n                        const viewport = event.currentTarget;\n                        const { contentWrapper, shouldExpandOnScrollRef } = viewportContext;\n                        if (shouldExpandOnScrollRef?.current && contentWrapper) {\n                            const scrolledBy = Math.abs(prevScrollTopRef.current - viewport.scrollTop);\n                            if (scrolledBy > 0) {\n                                const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                                const cssMinHeight = parseFloat(contentWrapper.style.minHeight);\n                                const cssHeight = parseFloat(contentWrapper.style.height);\n                                const prevHeight = Math.max(cssMinHeight, cssHeight);\n                                if (prevHeight < availableHeight) {\n                                    const nextHeight = prevHeight + scrolledBy;\n                                    const clampedNextHeight = Math.min(availableHeight, nextHeight);\n                                    const heightDiff = nextHeight - clampedNextHeight;\n                                    contentWrapper.style.height = clampedNextHeight + \"px\";\n                                    if (contentWrapper.style.bottom === \"0px\") {\n                                        viewport.scrollTop = heightDiff > 0 ? heightDiff : 0;\n                                        contentWrapper.style.justifyContent = \"flex-end\";\n                                    }\n                                }\n                            }\n                        }\n                        prevScrollTopRef.current = viewport.scrollTop;\n                    })\n                })\n            })\n        ]\n    });\n});\nSelectViewport.displayName = VIEWPORT_NAME;\nvar GROUP_NAME = \"SelectGroup\";\nvar [SelectGroupContextProvider, useSelectGroupContext] = createSelectContext(GROUP_NAME);\nvar SelectGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...groupProps } = props;\n    const groupId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)();\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectGroupContextProvider, {\n        scope: __scopeSelect,\n        id: groupId,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n            role: \"group\",\n            \"aria-labelledby\": groupId,\n            ...groupProps,\n            ref: forwardedRef\n        })\n    });\n});\nSelectGroup.displayName = GROUP_NAME;\nvar LABEL_NAME = \"SelectLabel\";\nvar SelectLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...labelProps } = props;\n    const groupContext = useSelectGroupContext(LABEL_NAME, __scopeSelect);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        id: groupContext.id,\n        ...labelProps,\n        ref: forwardedRef\n    });\n});\nSelectLabel.displayName = LABEL_NAME;\nvar ITEM_NAME = \"SelectItem\";\nvar [SelectItemContextProvider, useSelectItemContext] = createSelectContext(ITEM_NAME);\nvar SelectItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, value, disabled = false, textValue: textValueProp, ...itemProps } = props;\n    const context = useSelectContext(ITEM_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_NAME, __scopeSelect);\n    const isSelected = context.value === value;\n    const [textValue, setTextValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(textValueProp ?? \"\");\n    const [isFocused, setIsFocused] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, {\n        \"SelectItem.useComposedRefs[composedRefs]\": (node)=>contentContext.itemRefCallback?.(node, value, disabled)\n    }[\"SelectItem.useComposedRefs[composedRefs]\"]);\n    const textId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)();\n    const pointerTypeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"touch\");\n    const handleSelect = ()=>{\n        if (!disabled) {\n            context.onValueChange(value);\n            context.onOpenChange(false);\n        }\n    };\n    if (value === \"\") {\n        throw new Error(\"A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.\");\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectItemContextProvider, {\n        scope: __scopeSelect,\n        value,\n        disabled,\n        textId,\n        isSelected,\n        onItemTextChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"SelectItem.useCallback\": (node)=>{\n                setTextValue({\n                    \"SelectItem.useCallback\": (prevTextValue)=>prevTextValue || (node?.textContent ?? \"\").trim()\n                }[\"SelectItem.useCallback\"]);\n            }\n        }[\"SelectItem.useCallback\"], []),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.ItemSlot, {\n            scope: __scopeSelect,\n            value,\n            disabled,\n            textValue,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                role: \"option\",\n                \"aria-labelledby\": textId,\n                \"data-highlighted\": isFocused ? \"\" : void 0,\n                \"aria-selected\": isSelected && isFocused,\n                \"data-state\": isSelected ? \"checked\" : \"unchecked\",\n                \"aria-disabled\": disabled || void 0,\n                \"data-disabled\": disabled ? \"\" : void 0,\n                tabIndex: disabled ? void 0 : -1,\n                ...itemProps,\n                ref: composedRefs,\n                onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onFocus, ()=>setIsFocused(true)),\n                onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onBlur, ()=>setIsFocused(false)),\n                onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onClick, ()=>{\n                    if (pointerTypeRef.current !== \"mouse\") handleSelect();\n                }),\n                onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerUp, ()=>{\n                    if (pointerTypeRef.current === \"mouse\") handleSelect();\n                }),\n                onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerDown, (event)=>{\n                    pointerTypeRef.current = event.pointerType;\n                }),\n                onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerMove, (event)=>{\n                    pointerTypeRef.current = event.pointerType;\n                    if (disabled) {\n                        contentContext.onItemLeave?.();\n                    } else if (pointerTypeRef.current === \"mouse\") {\n                        event.currentTarget.focus({\n                            preventScroll: true\n                        });\n                    }\n                }),\n                onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerLeave, (event)=>{\n                    if (event.currentTarget === document.activeElement) {\n                        contentContext.onItemLeave?.();\n                    }\n                }),\n                onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onKeyDown, (event)=>{\n                    const isTypingAhead = contentContext.searchRef?.current !== \"\";\n                    if (isTypingAhead && event.key === \" \") return;\n                    if (SELECTION_KEYS.includes(event.key)) handleSelect();\n                    if (event.key === \" \") event.preventDefault();\n                })\n            })\n        })\n    });\n});\nSelectItem.displayName = ITEM_NAME;\nvar ITEM_TEXT_NAME = \"SelectItemText\";\nvar SelectItemText = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, className, style, ...itemTextProps } = props;\n    const context = useSelectContext(ITEM_TEXT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_TEXT_NAME, __scopeSelect);\n    const itemContext = useSelectItemContext(ITEM_TEXT_NAME, __scopeSelect);\n    const nativeOptionsContext = useSelectNativeOptionsContext(ITEM_TEXT_NAME, __scopeSelect);\n    const [itemTextNode, setItemTextNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, {\n        \"SelectItemText.useComposedRefs[composedRefs]\": (node)=>setItemTextNode(node)\n    }[\"SelectItemText.useComposedRefs[composedRefs]\"], itemContext.onItemTextChange, {\n        \"SelectItemText.useComposedRefs[composedRefs]\": (node)=>contentContext.itemTextRefCallback?.(node, itemContext.value, itemContext.disabled)\n    }[\"SelectItemText.useComposedRefs[composedRefs]\"]);\n    const textContent = itemTextNode?.textContent;\n    const nativeOption = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"SelectItemText.useMemo[nativeOption]\": ()=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"option\", {\n                value: itemContext.value,\n                disabled: itemContext.disabled,\n                children: textContent\n            }, itemContext.value)\n    }[\"SelectItemText.useMemo[nativeOption]\"], [\n        itemContext.disabled,\n        itemContext.value,\n        textContent\n    ]);\n    const { onNativeOptionAdd, onNativeOptionRemove } = nativeOptionsContext;\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectItemText.useLayoutEffect\": ()=>{\n            onNativeOptionAdd(nativeOption);\n            return ({\n                \"SelectItemText.useLayoutEffect\": ()=>onNativeOptionRemove(nativeOption)\n            })[\"SelectItemText.useLayoutEffect\"];\n        }\n    }[\"SelectItemText.useLayoutEffect\"], [\n        onNativeOptionAdd,\n        onNativeOptionRemove,\n        nativeOption\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n                id: itemContext.textId,\n                ...itemTextProps,\n                ref: composedRefs\n            }),\n            itemContext.isSelected && context.valueNode && !context.valueNodeHasChildren ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(itemTextProps.children, context.valueNode) : null\n        ]\n    });\n});\nSelectItemText.displayName = ITEM_TEXT_NAME;\nvar ITEM_INDICATOR_NAME = \"SelectItemIndicator\";\nvar SelectItemIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...itemIndicatorProps } = props;\n    const itemContext = useSelectItemContext(ITEM_INDICATOR_NAME, __scopeSelect);\n    return itemContext.isSelected ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        \"aria-hidden\": true,\n        ...itemIndicatorProps,\n        ref: forwardedRef\n    }) : null;\n});\nSelectItemIndicator.displayName = ITEM_INDICATOR_NAME;\nvar SCROLL_UP_BUTTON_NAME = \"SelectScrollUpButton\";\nvar SelectScrollUpButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const contentContext = useSelectContentContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n    const viewportContext = useSelectViewportContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n    const [canScrollUp, setCanScrollUp] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, viewportContext.onScrollButtonChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectScrollUpButton.useLayoutEffect\": ()=>{\n            if (contentContext.viewport && contentContext.isPositioned) {\n                let handleScroll2 = {\n                    \"SelectScrollUpButton.useLayoutEffect.handleScroll2\": function() {\n                        const canScrollUp2 = viewport.scrollTop > 0;\n                        setCanScrollUp(canScrollUp2);\n                    }\n                }[\"SelectScrollUpButton.useLayoutEffect.handleScroll2\"];\n                var handleScroll = handleScroll2;\n                const viewport = contentContext.viewport;\n                handleScroll2();\n                viewport.addEventListener(\"scroll\", handleScroll2);\n                return ({\n                    \"SelectScrollUpButton.useLayoutEffect\": ()=>viewport.removeEventListener(\"scroll\", handleScroll2)\n                })[\"SelectScrollUpButton.useLayoutEffect\"];\n            }\n        }\n    }[\"SelectScrollUpButton.useLayoutEffect\"], [\n        contentContext.viewport,\n        contentContext.isPositioned\n    ]);\n    return canScrollUp ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectScrollButtonImpl, {\n        ...props,\n        ref: composedRefs,\n        onAutoScroll: ()=>{\n            const { viewport, selectedItem } = contentContext;\n            if (viewport && selectedItem) {\n                viewport.scrollTop = viewport.scrollTop - selectedItem.offsetHeight;\n            }\n        }\n    }) : null;\n});\nSelectScrollUpButton.displayName = SCROLL_UP_BUTTON_NAME;\nvar SCROLL_DOWN_BUTTON_NAME = \"SelectScrollDownButton\";\nvar SelectScrollDownButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const contentContext = useSelectContentContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n    const viewportContext = useSelectViewportContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n    const [canScrollDown, setCanScrollDown] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, viewportContext.onScrollButtonChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectScrollDownButton.useLayoutEffect\": ()=>{\n            if (contentContext.viewport && contentContext.isPositioned) {\n                let handleScroll2 = {\n                    \"SelectScrollDownButton.useLayoutEffect.handleScroll2\": function() {\n                        const maxScroll = viewport.scrollHeight - viewport.clientHeight;\n                        const canScrollDown2 = Math.ceil(viewport.scrollTop) < maxScroll;\n                        setCanScrollDown(canScrollDown2);\n                    }\n                }[\"SelectScrollDownButton.useLayoutEffect.handleScroll2\"];\n                var handleScroll = handleScroll2;\n                const viewport = contentContext.viewport;\n                handleScroll2();\n                viewport.addEventListener(\"scroll\", handleScroll2);\n                return ({\n                    \"SelectScrollDownButton.useLayoutEffect\": ()=>viewport.removeEventListener(\"scroll\", handleScroll2)\n                })[\"SelectScrollDownButton.useLayoutEffect\"];\n            }\n        }\n    }[\"SelectScrollDownButton.useLayoutEffect\"], [\n        contentContext.viewport,\n        contentContext.isPositioned\n    ]);\n    return canScrollDown ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectScrollButtonImpl, {\n        ...props,\n        ref: composedRefs,\n        onAutoScroll: ()=>{\n            const { viewport, selectedItem } = contentContext;\n            if (viewport && selectedItem) {\n                viewport.scrollTop = viewport.scrollTop + selectedItem.offsetHeight;\n            }\n        }\n    }) : null;\n});\nSelectScrollDownButton.displayName = SCROLL_DOWN_BUTTON_NAME;\nvar SelectScrollButtonImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, onAutoScroll, ...scrollIndicatorProps } = props;\n    const contentContext = useSelectContentContext(\"SelectScrollButton\", __scopeSelect);\n    const autoScrollTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const getItems = useCollection(__scopeSelect);\n    const clearAutoScrollTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectScrollButtonImpl.useCallback[clearAutoScrollTimer]\": ()=>{\n            if (autoScrollTimerRef.current !== null) {\n                window.clearInterval(autoScrollTimerRef.current);\n                autoScrollTimerRef.current = null;\n            }\n        }\n    }[\"SelectScrollButtonImpl.useCallback[clearAutoScrollTimer]\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"SelectScrollButtonImpl.useEffect\": ()=>{\n            return ({\n                \"SelectScrollButtonImpl.useEffect\": ()=>clearAutoScrollTimer()\n            })[\"SelectScrollButtonImpl.useEffect\"];\n        }\n    }[\"SelectScrollButtonImpl.useEffect\"], [\n        clearAutoScrollTimer\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectScrollButtonImpl.useLayoutEffect\": ()=>{\n            const activeItem = getItems().find({\n                \"SelectScrollButtonImpl.useLayoutEffect.activeItem\": (item)=>item.ref.current === document.activeElement\n            }[\"SelectScrollButtonImpl.useLayoutEffect.activeItem\"]);\n            activeItem?.ref.current?.scrollIntoView({\n                block: \"nearest\"\n            });\n        }\n    }[\"SelectScrollButtonImpl.useLayoutEffect\"], [\n        getItems\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        \"aria-hidden\": true,\n        ...scrollIndicatorProps,\n        ref: forwardedRef,\n        style: {\n            flexShrink: 0,\n            ...scrollIndicatorProps.style\n        },\n        onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerDown, ()=>{\n            if (autoScrollTimerRef.current === null) {\n                autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n            }\n        }),\n        onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerMove, ()=>{\n            contentContext.onItemLeave?.();\n            if (autoScrollTimerRef.current === null) {\n                autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n            }\n        }),\n        onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerLeave, ()=>{\n            clearAutoScrollTimer();\n        })\n    });\n});\nvar SEPARATOR_NAME = \"SelectSeparator\";\nvar SelectSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...separatorProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        \"aria-hidden\": true,\n        ...separatorProps,\n        ref: forwardedRef\n    });\n});\nSelectSeparator.displayName = SEPARATOR_NAME;\nvar ARROW_NAME = \"SelectArrow\";\nvar SelectArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(ARROW_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ARROW_NAME, __scopeSelect);\n    return context.open && contentContext.position === \"popper\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    }) : null;\n});\nSelectArrow.displayName = ARROW_NAME;\nfunction shouldShowPlaceholder(value) {\n    return value === \"\" || value === void 0;\n}\nvar BubbleSelect = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { value, ...selectProps } = props;\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, ref);\n    const prevValue = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_21__.usePrevious)(value);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"BubbleSelect.useEffect\": ()=>{\n            const select = ref.current;\n            const selectProto = window.HTMLSelectElement.prototype;\n            const descriptor = Object.getOwnPropertyDescriptor(selectProto, \"value\");\n            const setValue = descriptor.set;\n            if (prevValue !== value && setValue) {\n                const event = new Event(\"change\", {\n                    bubbles: true\n                });\n                setValue.call(select, value);\n                select.dispatchEvent(event);\n            }\n        }\n    }[\"BubbleSelect.useEffect\"], [\n        prevValue,\n        value\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_22__.VisuallyHidden, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"select\", {\n            ...selectProps,\n            ref: composedRefs,\n            defaultValue: value\n        })\n    });\n});\nBubbleSelect.displayName = \"BubbleSelect\";\nfunction useTypeaheadSearch(onSearchChange) {\n    const handleSearchChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_23__.useCallbackRef)(onSearchChange);\n    const searchRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"\");\n    const timerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const handleTypeaheadSearch = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"useTypeaheadSearch.useCallback[handleTypeaheadSearch]\": (key)=>{\n            const search = searchRef.current + key;\n            handleSearchChange(search);\n            (function updateSearch(value) {\n                searchRef.current = value;\n                window.clearTimeout(timerRef.current);\n                if (value !== \"\") timerRef.current = window.setTimeout({\n                    \"useTypeaheadSearch.useCallback[handleTypeaheadSearch].updateSearch\": ()=>updateSearch(\"\")\n                }[\"useTypeaheadSearch.useCallback[handleTypeaheadSearch].updateSearch\"], 1e3);\n            })(search);\n        }\n    }[\"useTypeaheadSearch.useCallback[handleTypeaheadSearch]\"], [\n        handleSearchChange\n    ]);\n    const resetTypeahead = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"useTypeaheadSearch.useCallback[resetTypeahead]\": ()=>{\n            searchRef.current = \"\";\n            window.clearTimeout(timerRef.current);\n        }\n    }[\"useTypeaheadSearch.useCallback[resetTypeahead]\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useTypeaheadSearch.useEffect\": ()=>{\n            return ({\n                \"useTypeaheadSearch.useEffect\": ()=>window.clearTimeout(timerRef.current)\n            })[\"useTypeaheadSearch.useEffect\"];\n        }\n    }[\"useTypeaheadSearch.useEffect\"], []);\n    return [\n        searchRef,\n        handleTypeaheadSearch,\n        resetTypeahead\n    ];\n}\nfunction findNextItem(items, search, currentItem) {\n    const isRepeated = search.length > 1 && Array.from(search).every((char)=>char === search[0]);\n    const normalizedSearch = isRepeated ? search[0] : search;\n    const currentItemIndex = currentItem ? items.indexOf(currentItem) : -1;\n    let wrappedItems = wrapArray(items, Math.max(currentItemIndex, 0));\n    const excludeCurrentItem = normalizedSearch.length === 1;\n    if (excludeCurrentItem) wrappedItems = wrappedItems.filter((v)=>v !== currentItem);\n    const nextItem = wrappedItems.find((item)=>item.textValue.toLowerCase().startsWith(normalizedSearch.toLowerCase()));\n    return nextItem !== currentItem ? nextItem : void 0;\n}\nfunction wrapArray(array, startIndex) {\n    return array.map((_, index)=>array[(startIndex + index) % array.length]);\n}\nvar Root2 = Select;\nvar Trigger = SelectTrigger;\nvar Value = SelectValue;\nvar Icon = SelectIcon;\nvar Portal = SelectPortal;\nvar Content2 = SelectContent;\nvar Viewport = SelectViewport;\nvar Group = SelectGroup;\nvar Label = SelectLabel;\nvar Item = SelectItem;\nvar ItemText = SelectItemText;\nvar ItemIndicator = SelectItemIndicator;\nvar ScrollUpButton = SelectScrollUpButton;\nvar ScrollDownButton = SelectScrollDownButton;\nvar Separator = SelectSeparator;\nvar Arrow2 = SelectArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-select@2.1._fc26463523a688c90e60aba080c6891d/node_modules/@radix-ui/react-select/dist/index.mjs\n");

/***/ })

};
;