(()=>{var e={};e.id=964,e.ids=[964],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10199:(e,r,t)=>{Promise.resolve().then(t.bind(t,88116))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41491:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>c,tree:()=>p});var o=t(7025),s=t(18198),n=t(82576),a=t.n(n),i=t(45239),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let p={children:["",{children:["(dashboard)",{children:["log-analysis",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,62555)),"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\(dashboard)\\log-analysis\\page.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,4540,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,53117,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,6874,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,59650)),"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,4540,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,53117,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,6874,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["D:\\pycode\\support_chart2\\hotel-dashboard\\app\\(dashboard)\\log-analysis\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},c=new o.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(dashboard)/log-analysis/page",pathname:"/log-analysis",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},62555:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});let o=(0,t(51129).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\(dashboard)\\log-analysis\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73247:(e,r,t)=>{Promise.resolve().then(t.bind(t,62555))},79428:e=>{"use strict";e.exports=require("buffer")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[557,273,403,787,721],()=>t(41491));module.exports=o})();