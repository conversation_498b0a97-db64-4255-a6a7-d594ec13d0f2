# 面形数据查询用户使用指南

## 1. 功能介绍

面形数据查询功能允许用户通过FTP服务器检索和下载大棱镜和小棱镜的面形检测数据文件。用户可以通过指定产品序列号（SN）进行精确搜索或使用正则表达式进行模糊搜索。搜索结果会以列表形式展示，并支持单文件下载或批量打包下载为ZIP文件。

## 2. 使用步骤

### 2.1 进入查询页面

1.  在 `hotel-dashboard` 的侧边栏导航中，找到并点击“面形数据查询”菜单项。
2.  系统将导航至面形数据查询页面。

### 2.2 搜索面形数据

1.  在页面顶部的搜索区域，您会看到一个 **多行文本输入框**。
2.  在输入框中输入您想要查询的产品序列号（SN）。您可以输入 **一个或多个SN**，每个SN占一行。
    *   **多行搜索**：每一行都被视为一个独立的搜索条件。
    *   **正则表达式默认启用**：对于每一行输入的SN，系统 **默认启用正则表达式匹配**。您可以直接在每一行输入正则表达式。例如：
        *   第一行输入 `SN123.*` (搜索所有以 `SN123` 开头的SN)
        *   第二行输入 `.*LSM.*` (搜索所有包含 `LSM` 的SN)
        *   第三行输入 `SN(XYZ|ABC)[0-9]{3}` (搜索 `SNXYZ` 或 `SNABC` 后跟3位数字的SN)
    *   **精确搜索**：如果您想对某一行进行精确（非正则）匹配，请确保您的输入不包含正则表达式的特殊字符，或者对特殊字符进行转义（例如 `\.` 代表实际的点字符）。但通常直接输入SN即可。
3.  输入一个或多个SN（每行一个）后，点击“搜索”按钮。
4.  系统将为每一行输入分别向FTP服务器发送查询请求，并汇总结果。

### 2.3 查看搜索结果

1.  搜索完成后，结果将以列表形式显示在搜索区域下方。
2.  每条结果通常包含文件名、文件大小、修改日期等信息。
3.  对于支持预览的文件类型（如 `.xyz`, `.zip`, `.gz`），每行会有一个“预览”按钮。
4.  如果搜索结果过多，列表最多显示 **50条** 记录。请优化您的搜索条件以获得更精确的结果。
5.  如果未找到匹配文件，将显示提示信息。

### 2.4 文件预览（新功能）

对于包含点云数据的文件，您可以直接在浏览器中查看其二维渲染预览图。

1.  在搜索结果列表中，找到您希望预览的文件。
2.  点击该文件条目右侧的“预览”按钮。
3.  一个预览窗口将会弹出，显示点云数据的俯视图。图中点的颜色代表其Z轴高度。
4.  **交互操作**：
    *   **缩放**：使用鼠标滚轮进行放大和缩小。
    *   **平移**：按住鼠标左键并拖动图像进行平移。
    *   **控制按钮**：窗口右上角提供“放大”、“缩小”和“重置视图”的快捷按钮。
    *   **双击**：双击预览图可以快速重置视图。
5.  预览加载过程中会显示加载动画。如果预览失败，会显示错误信息和“重试”按钮。
6.  点击窗口右上角的 "×" 按钮或在窗口外单击可关闭预览。

### 2.5 下载文件

#### 2.5.1 单文件下载

1.  在搜索结果列表中，找到您需要下载的文件。
2.  点击对应文件条目右侧的“下载”按钮。
3.  浏览器将开始下载该文件。

#### 2.5.2 批量下载 (ZIP)

1.  在搜索结果列表中，勾选您希望批量下载的多个文件左侧的复选框。
2.  勾选完毕后，点击结果列表上方的“下载选中项 (ZIP)”按钮。
3.  系统会将选中的文件打包成一个ZIP压缩文件，并开始下载。

## 3. 界面说明

*   **导航栏**：位于页面左侧，包含“面形数据查询”等导航链接。
*   **搜索栏 ([`hotel-dashboard/components/surface-data-query/SearchBar.tsx`](hotel-dashboard/components/surface-data-query/SearchBar.tsx:1))**：
    *   **SN多行输入框**：用于输入一个或多个产品序列号（SN），每行一个。**默认对每行输入启用正则表达式匹配**。
    *   **搜索按钮**：触发搜索操作。
*   **结果展示区 ([`hotel-dashboard/components/surface-data-query/ResultsDisplay.tsx`](hotel-dashboard/components/surface-data-query/ResultsDisplay.tsx:1))**：
    *   **结果列表**：显示匹配的搜索结果，包含文件名、大小、日期等。
    *   **复选框**：用于选择文件进行批量下载。
    *   **预览按钮（新功能）**：每条结果旁边的预览按钮，用于打开文件预览窗口。
    *   **下载按钮（单文件）**：每条结果旁边的下载按钮。
    *   **下载选中项 (ZIP) 按钮**：用于批量下载选中的文件。
    *   **加载提示/无结果提示**：在搜索过程中或无结果时显示。

## 4. 搜索功能详细说明

### 4.1 多行搜索与正则表达式

*   **多行输入**: 您可以在搜索框中输入多行文本，每一行代表一个独立的搜索查询。
*   **独立正则匹配**: 系统会对每一行输入 **独立进行正则表达式匹配**。这意味着您可以在不同行使用不同的正则表达式模式。
*   **结果合并**: 所有行的搜索结果将会合并显示在结果列表中。
*   **便利性**: 多行搜索功能允许您一次性执行多个不同的查询，例如同时搜索不同系列或不同特征的产品SN，提高了查询效率。

### 4.2 正则表达式基础

正则表达式是一种强大的文本匹配工具。在SN搜索中，**默认对每一行输入启用** 正则表达式，您可以使用它来定义灵活的搜索模式。

**常用元字符：**

*   `.` (点)：匹配除换行符以外的任意单个字符。
*   `*` (星号)：匹配前面的子表达式零次或多次。例如，`SN12*` 匹配 `SN1`、`SN12`、`SN122` 等。
*   `+` (加号)：匹配前面的子表达式一次或多次。例如，`SN12+` 匹配 `SN12`、`SN122`，但不匹配 `SN1`。
*   `?` (问号)：匹配前面的子表达式零次或一次。例如，`SN123?` 匹配 `SN12` 和 `SN123`。
*   `^` (脱字符)：匹配输入的开始。例如，`^SN` 匹配以 `SN` 开头的SN。
*   `$` (美元符号)：匹配输入的结束。例如，`DATA$` 匹配以 `DATA` 结尾的SN。
*   `[]` (方括号)：定义字符集。例如，`[abc]` 匹配 `a`、`b` 或 `c`。`[0-9]` 匹配任意数字。
*   `()` (圆括号)：将子表达式分组。例如，`(LSM|SSM)` 匹配 `LSM` 或 `SSM`。
*   `|` (竖线)：或操作符。例如，`SN123|SN456` 匹配 `SN123` 或 `SN456`。

**示例：**

*   搜索所有以 "P" 开头，后跟3位数字的SN：`^P[0-9]{3}` (假设系统支持 `{n}` 数量限定符) 或 `^P[0-9][0-9][0-9]`
*   搜索包含 "BIGPRISM" 或 "SMALLPRISM" 的SN：`BIGPRISM|SMALLPRISM` 或 `(BIG|SMALL)PRISM`

**注意：** 具体的正则表达式语法和支持程度可能因后端实现而略有差异。请参考开发者文档或咨询技术支持以获取最准确的信息。

## 5. 文件下载操作指南

*   **单文件下载**：直接点击文件旁的“下载”按钮即可。文件将以其原始名称和格式保存。
*   **批量下载**：
    1.  选中需要下载的文件。
    2.  点击“下载选中项 (ZIP)”按钮。
    3.  下载的ZIP文件名通常会包含时间戳或批次信息，例如 `surface_data_export_YYYYMMDDHHMMSS.zip`。
    4.  下载完成后，您需要使用解压缩软件（如WinRAR, 7-Zip等）来解压ZIP文件，获取内部的原始数据文件。

## 6. 常见问题解答 (FAQ)

**Q1: 搜索没有结果怎么办？**
A1:
    *   请检查您输入的SN是否正确，包括大小写（如果敏感）。
    *   尝试使用更宽松的正则表达式，或减少搜索条件的限制。
    *   确认FTP服务器 ([`************`](:0)) 是否在线且数据是否已同步。
    *   联系技术支持确认数据是否存在或查询条件是否有误。

**Q2: 下载速度很慢怎么办？**
A2:
    *   下载速度受网络连接和FTP服务器负载影响。请检查您的网络连接。
    *   如果下载大文件或大量文件，请耐心等待。
    *   如果问题持续，请联系技术支持检查服务器状态。

**Q3: 批量下载的ZIP文件无法解压或损坏怎么办？**
A3:
    *   尝试使用不同的解压缩软件。
    *   重新尝试批量下载。
    *   如果问题依旧，可能是打包过程中出现错误，请联系技术支持。

**Q4: 我可以使用哪些正则表达式元字符？**
A4: 请参考本文档的“4.1 正则表达式基础”部分。具体的支持情况可能因后端实现而异，如有疑问请咨询技术支持。

**Q5: 搜索结果列表为什么最多只显示50条？**
A5: 这是为了优化性能和用户体验，搜索结果上限已从20条提升至 **50条**。如果您的搜索结果超过50条，建议您优化搜索条件以获得更精确的结果。

**Q6: FTP服务器的地址和登录凭证是什么？**
A6: FTP服务器地址为 [`************`](:0)。登录凭证（用户名 `BiUser`，密码 `o41Gkwvo4WRVIKKP`，路径 `/home`）由后端服务管理，用户无需直接操作。

---
*本文档最后更新于：2025-06-12 12:14 PM*