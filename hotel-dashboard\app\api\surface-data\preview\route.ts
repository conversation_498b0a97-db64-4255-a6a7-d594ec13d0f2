import { NextRequest, NextResponse } from 'next/server';
import { downloadFileToBuffer } from '@/lib/ftpClient';
import StreamZip from 'node-stream-zip';
import zlib from 'zlib';
import { PointXYZ, parsePointCloudData, calculateBounds, getColorFromGradient } from '@/utils/pointCloudUtils';
import path from 'path';
import fs from 'fs/promises';
import os from 'os';
import { v4 as uuidv4 } from 'uuid';

// --- Constants ---
const MAX_FILE_SIZE_BYTES = 50 * 1024 * 1024; // 50MB
const RENDER_TIMEOUT_MS = 30000; // 30 seconds
const MAX_POINTS_FOR_PREVIEW = 150000; // Limit points for performance

// --- Type Definitions ---
interface PreviewRequestBody {
  filePath: string;
}

interface PreviewResponse {
  success: boolean;
  imageData?: string;
  error?: string;
  message?: string;
}

class PreviewError extends Error {
  constructor(message: string, public status: number = 500) {
    super(message);
    this.name = 'PreviewError';
  }
}

// --- Core Logic ---

/**
 * Handles the decompression of a .zip file buffer in memory.
 * @param fileBuffer The buffer containing the .zip file data.
 * @returns A promise that resolves with the string content of the first .xyz file found.
 */
async function handleZipDecompression(fileBuffer: Buffer): Promise<string> {
  const tmpDir = os.tmpdir();
  const tmpZipPath = path.join(tmpDir, `preview-${uuidv4()}.zip`);
  let zip: any; // Declare zip here to access in finally block

  try {
    console.log(`[Preview API] Writing buffer to temporary zip file: ${tmpZipPath}`);
    await fs.writeFile(tmpZipPath, fileBuffer);

    console.log('[Preview API] Detected .zip file, decompressing from temporary file');
    zip = new StreamZip.async({ file: tmpZipPath });

    const entries = await zip.entries();
    const xyzEntry = Object.values(entries).find((entry: any) =>
      !entry.isDirectory && entry.name.toLowerCase().endsWith('.xyz')
    );

    if (!xyzEntry) {
      throw new PreviewError('No .xyz file found in the zip archive', 400);
    }

    console.log(`[Preview API] Found .xyz file in zip: ${xyzEntry.name}`);
    const decompressedBuffer = await zip.entryData(xyzEntry.name);
    return decompressedBuffer.toString('utf-8');
  } finally {
    if (zip) {
      await zip.close();
      console.log('[Preview API] Zip instance closed.');
    }
    try {
      await fs.unlink(tmpZipPath);
      console.log(`[Preview API] Deleted temporary zip file: ${tmpZipPath}`);
    } catch (cleanupError) {
      console.error(`[Preview API] Failed to delete temporary zip file ${tmpZipPath}:`, cleanupError);
    }
  }
}

/**
 * Downloads and decompresses the point cloud file.
 * @param filePath The path to the file on the FTP server.
 * @returns A promise that resolves to the string content of the .xyz data.
 */
async function getPointCloudData(filePath: string): Promise<string> {
  console.log(`[Preview API] Downloading: ${filePath}`);
  const fileBuffer = await downloadFileToBuffer(filePath).catch(err => {
    throw new PreviewError(`FTP Download Error: ${err.message}`, 500);
  });
  console.log(`[Preview API] File downloaded, size: ${fileBuffer.length} bytes`);

  if (fileBuffer.length > MAX_FILE_SIZE_BYTES) {
    throw new PreviewError(`File size exceeds limit of ${MAX_FILE_SIZE_BYTES / (1024 * 1024)}MB`, 413);
  }

  const fileExtension = path.extname(filePath).toLowerCase();

  switch (fileExtension) {
    case '.zip':
      return handleZipDecompression(fileBuffer);
    case '.gz':
      console.log('[Preview API] Detected .gz file, decompressing');
      return zlib.gunzipSync(fileBuffer).toString('utf-8');
    case '.xyz':
      console.log('[Preview API] Detected .xyz file');
      return fileBuffer.toString('utf-8');
    default:
      throw new PreviewError(`Unsupported file type: ${fileExtension}`, 400);
  }
}

/**
 * Generates a preview image from point cloud data.
 * @param points The array of PointXYZ objects.
 * @returns A promise that resolves to the base64 encoded PNG image data URL.
 */
async function generatePreviewImage(points: PointXYZ[]): Promise<string> {
  return new Promise(async (resolve, reject) => {
    try {
      let createCanvas;
      try {
        const canvasModule = await import('canvas');
        createCanvas = canvasModule.createCanvas;
      } catch (e) {
        console.error("Failed to load 'canvas' module. It may not be installed or the environment is missing dependencies.", e);
        throw new PreviewError("Preview generation is unavailable on the server due to missing dependencies.", 501);
      }
      const canvasWidth = 800;
      const canvasHeight = 600;
      const legendWidth = 80;
      const chartWidth = canvasWidth - legendWidth;

      const canvas = createCanvas(canvasWidth, canvasHeight);
      const ctx = canvas.getContext('2d');

      ctx.fillStyle = 'rgb(30,30,30)';
      ctx.fillRect(0, 0, canvasWidth, canvasHeight);

      const bounds = calculateBounds(points);
      if (!bounds) {
        return reject(new PreviewError('Could not calculate bounds for the point cloud.', 400));
      }

      const { minX, maxX, minY, maxY, minZ, maxZ } = bounds;
      const cloudWidth = maxX - minX;
      const cloudHeight = maxY - minY;

      if (cloudWidth <= 0 || cloudHeight <= 0) {
        ctx.font = '16px Arial';
        ctx.fillStyle = 'red';
        ctx.textAlign = 'center';
        ctx.fillText('Invalid point cloud dimensions.', chartWidth / 2, canvasHeight / 2);
        return resolve(canvas.toDataURL('image/png'));
      }

      const scale = Math.min(chartWidth / cloudWidth, canvasHeight / cloudHeight) * 0.9;
      const offsetX = (chartWidth - (cloudWidth * scale)) / 2;
      const offsetY = (canvasHeight - (cloudHeight * scale)) / 2;
      const zRange = maxZ - minZ;

      // Render points
      for (const point of points) {
        const x = offsetX + (point.x - minX) * scale;
        const y = offsetY + (point.y - minY) * scale;
        
        let normalizedZ = zRange > 1e-9 ? (point.z - minZ) / zRange : 0.5;
        normalizedZ = Math.max(0, Math.min(1, normalizedZ));

        const color = getColorFromGradient(normalizedZ);
        ctx.fillStyle = `rgb(${color.r},${color.g},${color.b})`;
        ctx.fillRect(x - 1, y - 1, 3, 3);
      }

      // Draw Color Legend
      drawLegend(ctx, { minZ, maxZ, chartWidth, canvasHeight });

      // Draw Info
      ctx.font = '10px Arial';
      ctx.fillStyle = 'grey';
      ctx.fillText(`Points: ${points.length.toLocaleString()}`, 10, canvasHeight - 10);

      console.log('[Preview API] Canvas rendering completed');
      resolve(canvas.toDataURL('image/png'));
    } catch (renderError) {
      console.error('[Preview API] Rendering error:', renderError);
      reject(new PreviewError('Failed to render preview image.', 500));
    }
  });
}

/**
 * Helper to draw the color legend on the canvas.
 */
function drawLegend(ctx: CanvasRenderingContext2D, { minZ, maxZ, chartWidth, canvasHeight }: { minZ: number, maxZ: number, chartWidth: number, canvasHeight: number }) {
    const legendRectX = chartWidth + 10;
    const legendRectY = 50;
    const legendRectWidth = 30;
    const legendRectHeight = canvasHeight - 100;
    
    const legendSteps = 20;
    const stepHeight = legendRectHeight / legendSteps;

    for (let i = 0; i < legendSteps; i++) {
        const normalizedValue = 1.0 - (i / (legendSteps - 1));
        const color = getColorFromGradient(normalizedValue);
        ctx.fillStyle = `rgb(${color.r},${color.g},${color.b})`;
        ctx.fillRect(legendRectX, legendRectY + i * stepHeight, legendRectWidth, stepHeight);
    }

    ctx.strokeStyle = 'white';
    ctx.lineWidth = 1;
    ctx.strokeRect(legendRectX, legendRectY, legendRectWidth, legendRectHeight);

    ctx.font = '12px Arial';
    ctx.fillStyle = 'white';
    ctx.textAlign = 'left';
    ctx.fillText(maxZ.toFixed(2), legendRectX + legendRectWidth + 5, legendRectY + 6);
    ctx.fillText(minZ.toFixed(2), legendRectX + legendRectWidth + 5, legendRectY + legendRectHeight);
    ctx.fillText("Z", legendRectX + legendRectWidth / 2 - 3, legendRectY - 10);
}


// --- API Handler ---

export async function POST(request: NextRequest): Promise<NextResponse<PreviewResponse>> {
  try {
    const body: PreviewRequestBody = await request.json();
    const { filePath } = body;

    if (!filePath) {
      throw new PreviewError('File path is required', 400);
    }

    console.log(`[Preview API] Request for filePath: ${filePath}`);

    // 1. Get Data (Download & Decompress)
    const xyzData = await getPointCloudData(filePath);

    // 2. Parse Data (with sampling)
    console.log('[Preview API] Parsing XYZ data');
    const points = parsePointCloudData(xyzData, { maxPoints: MAX_POINTS_FOR_PREVIEW });
    if (points.length === 0) {
      throw new PreviewError('No valid points found in the file.', 400);
    }
    console.log(`[Preview API] Parsed ${points.length} points (sampled)`);

    // 3. Generate Image (with timeout)
    console.log('[Preview API] Starting canvas rendering');
    const renderPromise = generatePreviewImage(points);
    
    const imageData = await Promise.race([
      renderPromise,
      new Promise<string>((_, reject) =>
        setTimeout(() => reject(new PreviewError('Rendering timed out', 504)), RENDER_TIMEOUT_MS)
      )
    ]);
    
    console.log('[Preview API] Image generated successfully');
    return NextResponse.json({ success: true, imageData });

  } catch (error: any) {
    console.error('[Preview API] Error:', error);
    const status = error instanceof PreviewError ? error.status : 500;
    const message = error.message || 'An unexpected error occurred.';
    return NextResponse.json({ success: false, error: message, message }, { status });
  }
}