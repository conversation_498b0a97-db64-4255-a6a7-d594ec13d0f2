{"version": 3, "sources": ["../src/FileSaver.js"], "names": [], "mappings": "uLAkBA,QAAS,CAAA,CAAT,CAAc,CAAd,CAAoB,CAApB,CAA0B,OACJ,WAAhB,QAAO,CAAA,CADa,CACS,CAAI,CAAG,CAAE,OAAO,GAAT,CADhB,CAEC,QAAhB,QAAO,CAAA,CAFQ,GAGtB,OAAO,CAAC,IAAR,CAAa,oDAAb,CAHsB,CAItB,CAAI,CAAG,CAAE,OAAO,CAAE,CAAC,CAAZ,CAJe,EASpB,CAAI,CAAC,OAAL,EAAgB,6EAA6E,IAA7E,CAAkF,CAAI,CAAC,IAAvF,CATI,CAUf,GAAI,CAAA,IAAJ,CAAS,UAA8B,CAA9B,CAAT,CAA8C,CAAE,IAAI,CAAE,CAAI,CAAC,IAAb,CAA9C,CAVe,CAYjB,CACR,CAED,QAAS,CAAA,CAAT,CAAmB,CAAnB,CAAwB,CAAxB,CAA8B,CAA9B,CAAoC,CAClC,GAAI,CAAA,CAAG,CAAG,GAAI,CAAA,cAAd,CACA,CAAG,CAAC,IAAJ,CAAS,KAAT,CAAgB,CAAhB,CAFkC,CAGlC,CAAG,CAAC,YAAJ,CAAmB,MAHe,CAIlC,CAAG,CAAC,MAAJ,CAAa,UAAY,CACvB,CAAM,CAAC,CAAG,CAAC,QAAL,CAAe,CAAf,CAAqB,CAArB,CACP,CANiC,CAOlC,CAAG,CAAC,OAAJ,CAAc,UAAY,CACxB,OAAO,CAAC,KAAR,CAAc,yBAAd,CACD,CATiC,CAUlC,CAAG,CAAC,IAAJ,EACD,CAED,QAAS,CAAA,CAAT,CAAsB,CAAtB,CAA2B,CACzB,GAAI,CAAA,CAAG,CAAG,GAAI,CAAA,cAAd,CAEA,CAAG,CAAC,IAAJ,CAAS,MAAT,CAAiB,CAAjB,IAHyB,CAIzB,GAAI,CACF,CAAG,CAAC,IAAJ,EACD,CAAC,MAAO,CAAP,CAAU,CAAE,CACd,MAAqB,IAAd,EAAA,CAAG,CAAC,MAAJ,EAAmC,GAAd,EAAA,CAAG,CAAC,MACjC,CAGD,QAAS,CAAA,CAAT,CAAgB,CAAhB,CAAsB,CACpB,GAAI,CACF,CAAI,CAAC,aAAL,CAAmB,GAAI,CAAA,UAAJ,CAAe,OAAf,CAAnB,CACD,CAAC,MAAO,CAAP,CAAU,CACV,GAAI,CAAA,CAAG,CAAG,QAAQ,CAAC,WAAT,CAAqB,aAArB,CAAV,CACA,CAAG,CAAC,cAAJ,CAAmB,OAAnB,OAAwC,MAAxC,CAAgD,CAAhD,CAAmD,CAAnD,CAAsD,CAAtD,CAAyD,EAAzD,CACsB,EADtB,aACsD,CADtD,CACyD,IADzD,CAFU,CAIV,CAAI,CAAC,aAAL,CAAmB,CAAnB,CACD,CACF,C,GAtDG,CAAA,CAAO,CAAqB,QAAlB,QAAO,CAAA,MAAP,EAA8B,MAAM,CAAC,MAAP,GAAkB,MAAhD,CACV,MADU,CACe,QAAhB,QAAO,CAAA,IAAP,EAA4B,IAAI,CAAC,IAAL,GAAc,IAA1C,CACT,IADS,CACgB,QAAlB,QAAO,CAAA,MAAP,EAA8B,MAAM,CAAC,MAAP,GAAkB,MAAhD,CACP,MADO,O,CAyDP,CAAc,CAAG,CAAO,CAAC,SAAR,EAAqB,YAAY,IAAZ,CAAiB,SAAS,CAAC,SAA3B,CAArB,EAA8D,cAAc,IAAd,CAAmB,SAAS,CAAC,SAA7B,CAA9D,EAAyG,CAAC,SAAS,IAAT,CAAc,SAAS,CAAC,SAAxB,C,CAE3H,CAAM,CAAG,CAAO,CAAC,MAAR,GAEQ,QAAlB,QAAO,CAAA,MAAP,EAA8B,MAAM,GAAK,CAA1C,CACI,UAAmB,CAAc,CADrC,CAIG,YAAc,CAAA,iBAAiB,CAAC,SAAhC,EAA6C,CAAC,CAA/C,CACA,SAAiB,CAAjB,CAAuB,CAAvB,CAA6B,CAA7B,CAAmC,IAC/B,CAAA,CAAG,CAAG,CAAO,CAAC,GAAR,EAAe,CAAO,CAAC,SADE,CAE/B,CAAC,CAAG,QAAQ,CAAC,aAAT,CAAuB,GAAvB,CAF2B,CAGnC,CAAI,CAAG,CAAI,EAAI,CAAI,CAAC,IAAb,EAAqB,UAHO,CAKnC,CAAC,CAAC,QAAF,CAAa,CALsB,CAMnC,CAAC,CAAC,GAAF,CAAQ,UAN2B,CAWf,QAAhB,QAAO,CAAA,CAXwB,EAajC,CAAC,CAAC,IAAF,CAAS,CAbwB,CAc7B,CAAC,CAAC,MAAF,GAAa,QAAQ,CAAC,MAdO,CAmB/B,CAAK,CAAC,CAAD,CAnB0B,CAe/B,CAAW,CAAC,CAAC,CAAC,IAAH,CAAX,CACI,CAAQ,CAAC,CAAD,CAAO,CAAP,CAAa,CAAb,CADZ,CAEI,CAAK,CAAC,CAAD,CAAI,CAAC,CAAC,MAAF,CAAW,QAAf,CAjBsB,GAuBjC,CAAC,CAAC,IAAF,CAAS,CAAG,CAAC,eAAJ,CAAoB,CAApB,CAvBwB,CAwBjC,UAAU,CAAC,UAAY,CAAE,CAAG,CAAC,eAAJ,CAAoB,CAAC,CAAC,IAAtB,CAA6B,CAA5C,CAA8C,GAA9C,CAxBuB,CAyBjC,UAAU,CAAC,UAAY,CAAE,CAAK,CAAC,CAAD,CAAK,CAAzB,CAA2B,CAA3B,CAzBuB,CA2BpC,CA5BC,CA+BA,oBAAsB,CAAA,SAAtB,CACA,SAAiB,CAAjB,CAAuB,CAAvB,CAA6B,CAA7B,CAAmC,CAGnC,GAFA,CAAI,CAAG,CAAI,EAAI,CAAI,CAAC,IAAb,EAAqB,UAE5B,CAAoB,QAAhB,QAAO,CAAA,CAAX,CAUE,SAAS,CAAC,gBAAV,CAA2B,CAAG,CAAC,CAAD,CAAO,CAAP,CAA9B,CAA4C,CAA5C,CAVF,KACE,IAAI,CAAW,CAAC,CAAD,CAAf,CACE,CAAQ,CAAC,CAAD,CAAO,CAAP,CAAa,CAAb,CADV,KAEO,CACL,GAAI,CAAA,CAAC,CAAG,QAAQ,CAAC,aAAT,CAAuB,GAAvB,CAAR,CACA,CAAC,CAAC,IAAF,CAAS,CAFJ,CAGL,CAAC,CAAC,MAAF,CAAW,QAHN,CAIL,UAAU,CAAC,UAAY,CAAE,CAAK,CAAC,CAAD,CAAK,CAAzB,CACX,CAIJ,CAhBC,CAmBA,SAAiB,CAAjB,CAAuB,CAAvB,CAA6B,CAA7B,CAAmC,CAAnC,CAA0C,CAS1C,GANA,CAAK,CAAG,CAAK,EAAI,IAAI,CAAC,EAAD,CAAK,QAAL,CAMrB,CALI,CAKJ,GAJE,CAAK,CAAC,QAAN,CAAe,KAAf,CACA,CAAK,CAAC,QAAN,CAAe,IAAf,CAAoB,SAApB,CAAgC,gBAGlC,EAAoB,QAAhB,QAAO,CAAA,CAAX,CAA8B,MAAO,CAAA,CAAQ,CAAC,CAAD,CAAO,CAAP,CAAa,CAAb,CAAf,CATY,GAWtC,CAAA,CAAK,CAAiB,0BAAd,GAAA,CAAI,CAAC,IAXyB,CAYtC,CAAQ,CAAG,eAAe,IAAf,CAAoB,CAAO,CAAC,WAA5B,GAA4C,CAAO,CAAC,MAZzB,CAatC,CAAW,CAAG,eAAe,IAAf,CAAoB,SAAS,CAAC,SAA9B,CAbwB,CAe1C,GAAI,CAAC,CAAW,EAAK,CAAK,EAAI,CAAzB,EAAsC,CAAvC,GAAgF,WAAtB,QAAO,CAAA,UAArE,CAAiG,CAE/F,GAAI,CAAA,CAAM,CAAG,GAAI,CAAA,UAAjB,CACA,CAAM,CAAC,SAAP,CAAmB,UAAY,CAC7B,GAAI,CAAA,CAAG,CAAG,CAAM,CAAC,MAAjB,CACA,CAAG,CAAG,CAAW,CAAG,CAAH,CAAS,CAAG,CAAC,OAAJ,CAAY,cAAZ,CAA4B,uBAA5B,CAFG,CAGzB,CAHyB,CAGlB,CAAK,CAAC,QAAN,CAAe,IAAf,CAAsB,CAHJ,CAIxB,QAAQ,CAAG,CAJa,CAK7B,CAAK,CAAG,IACT,CAT8F,CAU/F,CAAM,CAAC,aAAP,CAAqB,CAArB,CACD,CAXD,IAWO,IACD,CAAA,CAAG,CAAG,CAAO,CAAC,GAAR,EAAe,CAAO,CAAC,SAD5B,CAED,CAAG,CAAG,CAAG,CAAC,eAAJ,CAAoB,CAApB,CAFL,CAGD,CAHC,CAGM,CAAK,CAAC,QAAN,CAAiB,CAHvB,CAIA,QAAQ,CAAC,IAAT,CAAgB,CAJhB,CAKL,CAAK,CAAG,IALH,CAML,UAAU,CAAC,UAAY,CAAE,CAAG,CAAC,eAAJ,CAAoB,CAApB,CAA0B,CAAzC,CAA2C,GAA3C,CACX,CACF,CA1FU,C,CA6Fb,CAAO,CAAC,MAAR,CAAiB,CAAM,CAAC,MAAP,CAAgB,C,CAEX,WAAlB,QAAO,CAAA,M,GACT,MAAM,CAAC,OAAP,CAAiB,C", "file": "FileSaver.min.js", "sourcesContent": ["/*\n* FileSaver.js\n* A saveAs() FileSaver implementation.\n*\n* By <PERSON>, http://eligrey.com\n*\n* License : https://github.com/eligrey/FileSaver.js/blob/master/LICENSE.md (MIT)\n* source  : http://purl.eligrey.com/github/FileSaver.js\n*/\n\n// The one and only way of getting global scope in all environments\n// https://stackoverflow.com/q/3277182/1008999\nvar _global = typeof window === 'object' && window.window === window\n  ? window : typeof self === 'object' && self.self === self\n  ? self : typeof global === 'object' && global.global === global\n  ? global\n  : this\n\nfunction bom (blob, opts) {\n  if (typeof opts === 'undefined') opts = { autoBom: false }\n  else if (typeof opts !== 'object') {\n    console.warn('Deprecated: Expected third argument to be a object')\n    opts = { autoBom: !opts }\n  }\n\n  // prepend BOM for UTF-8 XML and text/* types (including HTML)\n  // note: your browser will automatically convert UTF-16 U+FEFF to EF BB BF\n  if (opts.autoBom && /^\\s*(?:text\\/\\S*|application\\/xml|\\S*\\/\\S*\\+xml)\\s*;.*charset\\s*=\\s*utf-8/i.test(blob.type)) {\n    return new Blob([String.fromCharCode(0xFEFF), blob], { type: blob.type })\n  }\n  return blob\n}\n\nfunction download (url, name, opts) {\n  var xhr = new XMLHttpRequest()\n  xhr.open('GET', url)\n  xhr.responseType = 'blob'\n  xhr.onload = function () {\n    saveAs(xhr.response, name, opts)\n  }\n  xhr.onerror = function () {\n    console.error('could not download file')\n  }\n  xhr.send()\n}\n\nfunction corsEnabled (url) {\n  var xhr = new XMLHttpRequest()\n  // use sync to avoid popup blocker\n  xhr.open('HEAD', url, false)\n  try {\n    xhr.send()\n  } catch (e) {}\n  return xhr.status >= 200 && xhr.status <= 299\n}\n\n// `a.click()` doesn't work for all browsers (#465)\nfunction click (node) {\n  try {\n    node.dispatchEvent(new MouseEvent('click'))\n  } catch (e) {\n    var evt = document.createEvent('MouseEvents')\n    evt.initMouseEvent('click', true, true, window, 0, 0, 0, 80,\n                          20, false, false, false, false, 0, null)\n    node.dispatchEvent(evt)\n  }\n}\n\n// Detect WebView inside a native macOS app by ruling out all browsers\n// We just need to check for 'Safari' because all other browsers (besides Firefox) include that too\n// https://www.whatismybrowser.com/guides/the-latest-user-agent/macos\nvar isMacOSWebView = _global.navigator && /Macintosh/.test(navigator.userAgent) && /AppleWebKit/.test(navigator.userAgent) && !/Safari/.test(navigator.userAgent)\n\nvar saveAs = _global.saveAs || (\n  // probably in some web worker\n  (typeof window !== 'object' || window !== _global)\n    ? function saveAs () { /* noop */ }\n\n  // Use download attribute first if possible (#193 Lumia mobile) unless this is a macOS WebView\n  : ('download' in HTMLAnchorElement.prototype && !isMacOSWebView)\n  ? function saveAs (blob, name, opts) {\n    var URL = _global.URL || _global.webkitURL\n    var a = document.createElement('a')\n    name = name || blob.name || 'download'\n\n    a.download = name\n    a.rel = 'noopener' // tabnabbing\n\n    // TODO: detect chrome extensions & packaged apps\n    // a.target = '_blank'\n\n    if (typeof blob === 'string') {\n      // Support regular links\n      a.href = blob\n      if (a.origin !== location.origin) {\n        corsEnabled(a.href)\n          ? download(blob, name, opts)\n          : click(a, a.target = '_blank')\n      } else {\n        click(a)\n      }\n    } else {\n      // Support blobs\n      a.href = URL.createObjectURL(blob)\n      setTimeout(function () { URL.revokeObjectURL(a.href) }, 4E4) // 40s\n      setTimeout(function () { click(a) }, 0)\n    }\n  }\n\n  // Use msSaveOrOpenBlob as a second approach\n  : 'msSaveOrOpenBlob' in navigator\n  ? function saveAs (blob, name, opts) {\n    name = name || blob.name || 'download'\n\n    if (typeof blob === 'string') {\n      if (corsEnabled(blob)) {\n        download(blob, name, opts)\n      } else {\n        var a = document.createElement('a')\n        a.href = blob\n        a.target = '_blank'\n        setTimeout(function () { click(a) })\n      }\n    } else {\n      navigator.msSaveOrOpenBlob(bom(blob, opts), name)\n    }\n  }\n\n  // Fallback to using FileReader and a popup\n  : function saveAs (blob, name, opts, popup) {\n    // Open a popup immediately do go around popup blocker\n    // Mostly only available on user interaction and the fileReader is async so...\n    popup = popup || open('', '_blank')\n    if (popup) {\n      popup.document.title =\n      popup.document.body.innerText = 'downloading...'\n    }\n\n    if (typeof blob === 'string') return download(blob, name, opts)\n\n    var force = blob.type === 'application/octet-stream'\n    var isSafari = /constructor/i.test(_global.HTMLElement) || _global.safari\n    var isChromeIOS = /CriOS\\/[\\d]+/.test(navigator.userAgent)\n\n    if ((isChromeIOS || (force && isSafari) || isMacOSWebView) && typeof FileReader !== 'undefined') {\n      // Safari doesn't allow downloading of blob URLs\n      var reader = new FileReader()\n      reader.onloadend = function () {\n        var url = reader.result\n        url = isChromeIOS ? url : url.replace(/^data:[^;]*;/, 'data:attachment/file;')\n        if (popup) popup.location.href = url\n        else location = url\n        popup = null // reverse-tabnabbing #460\n      }\n      reader.readAsDataURL(blob)\n    } else {\n      var URL = _global.URL || _global.webkitURL\n      var url = URL.createObjectURL(blob)\n      if (popup) popup.location = url\n      else location.href = url\n      popup = null // reverse-tabnabbing #460\n      setTimeout(function () { URL.revokeObjectURL(url) }, 4E4) // 40s\n    }\n  }\n)\n\n_global.saveAs = saveAs.saveAs = saveAs\n\nif (typeof module !== 'undefined') {\n  module.exports = saveAs;\n}\n"]}