"use client";

import React, { useState, useMemo, useRef, useEffect } from 'react';
import LogDisplayArea from '@/components/log-analysis/LogDisplayArea';
import LogChartView from '@/components/log-analysis/LogChartView';
import LogFileUpload from '@/components/log-analysis/LogFileUpload';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { exportElementAsImage } from '@/lib/exportUtils';
import { ProcessedBlock as WorkerProcessedBlock } from '@/workers/logParser.definitions';

interface ChartDataItem {
  name: string;
  value: number;
  type: string;
  block_id: string;
}

interface ProcessedBlock extends WorkerProcessedBlock {
  data: ChartDataItem[];
}

interface LogDisplayAreaProps {
  dataChunks: ProcessedBlock[];
  onSelectionChange: (selectedBlocks: ProcessedBlock[]) => void;
  onStartExport: (exportIds: string[]) => void;
}

export default function LogAnalysisPage() {
  const [dataChunks, setDataChunks] = useState<ProcessedBlock[]>([]);
  const [selectedBlocksForChart, setSelectedBlocksForChart] = useState<ProcessedBlock[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isExportingMode, setIsExportingMode] = useState(false);
  const [blocksToRenderForExport, setBlocksToRenderForExport] = useState<ProcessedBlock[]>([]);
  const exportTargetContainerRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        // 这里添加你的数据获取逻辑
        // 例如：从 API 获取数据或从本地存储读取
        const response = await fetch('/api/log-data');
        if (!response.ok) {
          throw new Error('Failed to fetch log data');
        }
        const data = await response.json();
        
        if (!data || !Array.isArray(data)) {
          throw new Error('Invalid data format');
        }
        
        // 转换数据格式
        const processedData = data.map((block: WorkerProcessedBlock) => ({
          ...block,
          data: [] // 初始化空的 data 数组
        }));
        
        setDataChunks(processedData);
      } catch (err) {
        console.error('Error fetching log data:', err);
        setError(err instanceof Error ? err.message : 'Failed to load log data');
        setDataChunks([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleProcessingStart = () => {
    console.log('[LogAnalysisPage] handleProcessingStart - START');
    setIsLoading(true);
    setError(null);
    setSelectedBlocksForChart([]);
    console.log('[LogAnalysisPage] handleProcessingStart - END');
  };

  const handleDataProcessed = (data: ProcessedBlock[]) => {
    console.log('[LogAnalysisPage] handleDataProcessed - START. Received data count:', data.length);
    setDataChunks(data);
    setIsLoading(false);
    toast({
      title: "数据已加载",
      description: `成功处理了 ${data.length} 个数据块。`,
    });
    console.log('[LogAnalysisPage] handleDataProcessed - END');
  };

  const handleError = (errorMessage: string) => {
    console.log('[LogAnalysisPage] handleError - START. Received errorMessage:', errorMessage);
    setError(errorMessage);
    setIsLoading(false);
    console.log('[LogAnalysisPage] handleError - END');
  };

  const handleBlockSelectionChanged = (selectedBlocks: ProcessedBlock[]) => {
    console.log('[LogAnalysisPage] handleBlockSelectionChanged - START. Received selectedBlocks count:', selectedBlocks.length);
    setSelectedBlocksForChart(selectedBlocks);
    console.log('[LogAnalysisPage] handleBlockSelectionChanged - END');
  };

  const handleBlockSelect = (blockId: string) => {
    console.log('[LogAnalysisPage] handleBlockSelect - START. Received blockId:', blockId);
    const block = dataChunks.find(b => b.block_id === blockId);
    if (block) {
      setSelectedBlocksForChart([block]);
    }
    console.log('[LogAnalysisPage] handleBlockSelect - END');
  };

  const chartDataForView = useMemo(() => {
    console.log('[LogAnalysisPage] Recalculating chartDataForView. Selected blocks count:', selectedBlocksForChart.length);
    return selectedBlocksForChart;
  }, [selectedBlocksForChart]);

  const initiateExportProcess = (exportIds: string[]) => {
    console.log('[LogAnalysisPage] initiateExportProcess - START. exportIds:', exportIds);
    const blocksToExport = dataChunks.filter(block => exportIds.includes(block.block_id));
    if (blocksToExport.length === 0) {
      toast({
        title: "导出错误",
        description: "没有找到要导出的数据块。",
        variant: "destructive",
      });
      return;
    }
    setBlocksToRenderForExport(blocksToExport);
    setIsExportingMode(true);
  };

  useEffect(() => {
    if (isExportingMode && blocksToRenderForExport.length > 0 && exportTargetContainerRef.current) {
      const allSelectedIds = blocksToRenderForExport.map(block => block.block_id);
      console.log('[LogAnalysisPage] Starting ZIP export for block IDs:', allSelectedIds);
      
      exportElementAsImage(
        exportTargetContainerRef.current,
        "exported_log_charts",
        allSelectedIds,
        (progress) => {
          console.log(`[LogAnalysisPage] Export progress: ${progress.toFixed(2)}%`);
        }
      ).then(() => {
        toast({
          title: "导出成功",
          description: "所有选中的图表已成功导出。",
        });
      }).catch((error) => {
        console.error('[LogAnalysisPage] Export failed:', error);
        toast({
          title: "导出失败",
          description: error instanceof Error ? error.message : "导出过程中发生错误。",
          variant: "destructive",
        });
      }).finally(() => {
        setIsExportingMode(false);
        setBlocksToRenderForExport([]);
      });
    }
  }, [isExportingMode, blocksToRenderForExport, dataChunks]);

  console.log(
    '[LogAnalysisPage] Rendering. isLoading:', isLoading,
    'error:', error,
    'dataChunks count:', dataChunks.length,
    'selectedBlocksForChart count:', selectedBlocksForChart.length,
    'chartDataForView count:', chartDataForView.length
  );

  if (isLoading) {
    return <div>加载中...</div>;
  }

  if (error) {
    return <div>错误: {error}</div>;
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">日志分析</h1>
      <LogDisplayArea 
        dataChunks={dataChunks}
        onSelectionChange={handleBlockSelectionChanged}
        onStartExport={initiateExportProcess}
      />
      {!isLoading && !error && dataChunks.length > 0 && (
        <div ref={exportTargetContainerRef} className="h-full">
          {isExportingMode ? (
            <LogChartView 
              dataChunks={blocksToRenderForExport} 
              selectedBlockIds={blocksToRenderForExport.map(b => b.block_id)}
              onBlockSelect={handleBlockSelect}
            />
          ) : chartDataForView.length > 0 ? (
            <LogChartView 
              dataChunks={chartDataForView} 
              selectedBlockIds={chartDataForView.map(b => b.block_id)}
              onBlockSelect={handleBlockSelect}
            />
          ) : (
            <Card className="h-full flex items-center justify-center">
              <CardContent className="text-center">
                <p className="text-muted-foreground">请从上方选择数据块以在图表中显示。</p>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );
}