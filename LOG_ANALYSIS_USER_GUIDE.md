# 日志分析模块用户指南

## 1. 功能概述

日志分析模块旨在帮助用户快速上传、解析和可视化设备生成的日志文件。通过该模块，您可以：

*   轻松上传包含设备运行数据的日志文件。
*   查看日志文件被分割成的不同数据块。
*   选择一个或多个数据块进行详细分析。
*   通过交互式图表直观地了解关键指标，例如胶厚、准直数据等。
*   识别并高亮显示日志中记录的特定事件，如放气阀打开事件。
*   将单个数据图表或整个分析区域导出为图片，方便分享和报告。

该功能旨在简化日志数据的处理流程，让用户能够更便捷地从原始数据中提取有价值的信息，用于故障排查、性能监控或工艺优化。

## 2. 如何使用

### 2.1 访问日志分析页面

您可以通过 `hotel-dashboard` 应用的侧边栏导航菜单访问日志分析功能。寻找名为“日志分析”或类似标识的链接。

### 2.2 上传日志文件

日志分析页面包含一个文件上传区域。

1.  文件上传区域采用两段式设计：
    *   **左侧** 是一个带有背景色的区域，显示文本“上传文件”。点击此区域会打开文件选择对话框。
    *   **右侧** 区域用于显示您选择的文件名。如果尚未选择文件，则显示“未选择文件”。
2.  点击左侧的“上传文件”区域，从您的计算机中选择一个日志文件。
3.  文件选择后，其名称将显示在右侧区域，系统会自动在后台进行解析。
*   **文件处理成功后，在文件选择区域下方，您将看到该日志文件的统计信息，包括总行数、日志记录的最早时间和最晚时间。** 这可以帮助您快速了解上传文件的基本情况。
    *   *(建议：此处更新一张包含新的文件输入框样式和文件统计信息显示的截图)*

### 2.3 选择已处理的数据块进行分析

日志文件成功解析后，会生成一系列数据块。这些数据块通常代表日志文件中的不同记录段或操作周期。

1.  在数据显示区域（由 [`hotel-dashboard/components/log-analysis/LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx:1) 管理），您会看到一个列表，展示了所有已处理的数据块。
2.  您可以勾选一个或多个数据块旁边的复选框，以选择它们进行图表分析。
3.  为了方便选择，该区域通常会提供“全选”按钮，允许您一次性选择所有列出的数据块。
4.  类似地，也会提供“全不选”按钮，用于快速取消所有当前的选择。

### 2.4 解读图表

当您选择数据块后，相关的图表会显示出来（由 [`hotel-dashboard/components/log-analysis/LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx:1) 渲染）。

*   **数据系列：** 图表会展示所选数据块中的关键数据，如“胶厚 (Glue Thickness)”和“准直数据 (Collimation Data)”。如果选择了多个数据块，不同数据块的同一类型数据会用不同颜色区分。
*   **时间轴 (X轴)：** X轴表示时间，通常来源于日志中的时间戳。
*   **数值轴 (Y轴)：** Y轴表示相应数据系列的值。可能会有多个Y轴，分别对应不同类型的数据（例如，一个Y轴用于胶厚，另一个用于准直）。
*   **事件标记：** 特定的日志事件，例如“放气阀打开 (Valve Open)”，可能会以垂直参考线或特殊标记的形式在图表上标出，帮助您关联数据变化与特定操作。
*   **图例：** 图表下方或侧边会提供图例，说明不同颜色或线条代表的数据系列。
*   **交互：** 您可能可以与图表进行交互，例如通过鼠标悬停查看具体数据点的值，或缩放图表以查看特定时间范围的细节。

### 2.5 导出图片

您可以将分析结果导出为图片，方便记录和分享。

*   **导出单个图表：**
    *   在“日志数据图表”区域的右上角，您会找到一个带有下载图标的“导出图片”按钮。
    *   点击此按钮，当前显示的图表将被导出为一个 PNG 图片文件。
    *   导出的文件名将包含时间戳以确保唯一性，例如 `log-chart-1678886400000.png`。
    *   如果图表当前没有数据，或者正在导出中，该按钮将被禁用。
*   **导出整个分析摘要图片：**
    *   在“选择数据块进行分析”区域的右上角，您会找到一个带有下载图标的“导出全部图片”按钮。
    *   点击此按钮，整个“选择数据块进行分析”区域（包含所有数据块的列表）将被导出为一个 PNG 图片文件。
    *   导出的文件名将包含时间戳，例如 `log-analysis-summary-1678886500000.png`。
    *   如果当前没有数据块可供显示，或者正在导出中，该按钮将被禁用。
*   **通知：**
    *   导出开始和成功时，您会收到屏幕右下角的 `toast` 通知。
    *   如果导出失败，`toast` 通知也会提示错误。

## 3. 预期输入

目前，系统主要设计用于处理特定格式的设备日志文件。通常，这些文件是文本文件，其中包含时间戳和各种传感器读数或事件记录。

如果您需要了解详细的日志文件格式要求，请咨询开发团队或查阅相关的技术规格文档。

## 4. 常见问题解答 (FAQ)

*(此部分可根据用户反馈和常见问题进行补充)*

*   **问：上传文件后没有看到数据块怎么办？**
    *   答：请检查您的日志文件格式是否符合系统要求。如果问题持续，请联系技术支持。
*   **问：图表加载很慢怎么办？**
    *   答：如果日志文件非常大或选择了非常多的数据块，图表加载可能需要一些时间。请耐心等待。如果加载时间过长，尝试减少选择的数据块数量。
*   **问：我可以导出图表或数据吗？**
    *   答：是的，您可以导出单个图表或整个数据块选择区域为 PNG 图片。请参考上方“2.5 导出图片”部分的说明。