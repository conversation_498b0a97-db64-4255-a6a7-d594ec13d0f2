[{"name": "generate-buildid", "duration": 404, "timestamp": 20196555348, "id": 4, "parentId": 1, "tags": {}, "startTime": 1750142109080, "traceId": "1eb9b100fb71111e"}, {"name": "load-custom-routes", "duration": 516, "timestamp": 20196555904, "id": 5, "parentId": 1, "tags": {}, "startTime": 1750142109081, "traceId": "1eb9b100fb71111e"}, {"name": "create-dist-dir", "duration": 766, "timestamp": 20196690518, "id": 6, "parentId": 1, "tags": {}, "startTime": 1750142109215, "traceId": "1eb9b100fb71111e"}, {"name": "create-pages-mapping", "duration": 431, "timestamp": 20196717751, "id": 7, "parentId": 1, "tags": {}, "startTime": 1750142109243, "traceId": "1eb9b100fb71111e"}, {"name": "collect-app-paths", "duration": 2929, "timestamp": 20196718280, "id": 8, "parentId": 1, "tags": {}, "startTime": 1750142109243, "traceId": "1eb9b100fb71111e"}, {"name": "create-app-mapping", "duration": 1455, "timestamp": 20196721254, "id": 9, "parentId": 1, "tags": {}, "startTime": 1750142109246, "traceId": "1eb9b100fb71111e"}, {"name": "public-dir-conflict-check", "duration": 1180, "timestamp": 20196723421, "id": 10, "parentId": 1, "tags": {}, "startTime": 1750142109248, "traceId": "1eb9b100fb71111e"}, {"name": "generate-routes-manifest", "duration": 4221, "timestamp": 20196724901, "id": 11, "parentId": 1, "tags": {}, "startTime": 1750142109250, "traceId": "1eb9b100fb71111e"}, {"name": "create-entrypoints", "duration": 39350, "timestamp": 20196752867, "id": 14, "parentId": 1, "tags": {}, "startTime": 1750142109278, "traceId": "1eb9b100fb71111e"}, {"name": "generate-webpack-config", "duration": 656578, "timestamp": 20196792348, "id": 15, "parentId": 13, "tags": {}, "startTime": 1750142109317, "traceId": "1eb9b100fb71111e"}, {"name": "next-trace-entrypoint-plugin", "duration": 2319, "timestamp": 20197631006, "id": 17, "parentId": 16, "tags": {}, "startTime": 1750142110156, "traceId": "1eb9b100fb71111e"}, {"name": "add-entry", "duration": 651673, "timestamp": 20197641304, "id": 24, "parentId": 18, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1750142110166, "traceId": "1eb9b100fb71111e"}, {"name": "add-entry", "duration": 834125, "timestamp": 20197641315, "id": 25, "parentId": 18, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1750142110166, "traceId": "1eb9b100fb71111e"}, {"name": "add-entry", "duration": 858499, "timestamp": 20197641618, "id": 31, "parentId": 18, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1750142110167, "traceId": "1eb9b100fb71111e"}, {"name": "add-entry", "duration": 907498, "timestamp": 20197641233, "id": 20, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fapi%2Ffeature%2Fupdate%2Froute&name=app%2Fapi%2Ffeature%2Fupdate%2Froute&pagePath=private-next-app-dir%2Fapi%2Ffeature%2Fupdate%2Froute.ts&appDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard%5Capp&appPaths=%2Fapi%2Ffeature%2Fupdate%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750142110166, "traceId": "1eb9b100fb71111e"}, {"name": "build-module-tsx", "duration": 25790, "timestamp": 20198617229, "id": 32, "parentId": 16, "tags": {"name": "D:\\pycode\\support_chart2\\hotel-dashboard\\components\\drawing-board\\DrawingBoard.tsx", "layer": "rsc"}, "startTime": 1750142111142, "traceId": "1eb9b100fb71111e"}, {"name": "add-entry", "duration": 1013334, "timestamp": 20197641278, "id": 22, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fapi%2Fftp%2Fsearch%2Froute&name=app%2Fapi%2Fftp%2Fsearch%2Froute&pagePath=private-next-app-dir%2Fapi%2Fftp%2Fsearch%2Froute.ts&appDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard%5Capp&appPaths=%2Fapi%2Fftp%2Fsearch%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750142110166, "traceId": "1eb9b100fb71111e"}, {"name": "add-entry", "duration": 1097487, "timestamp": 20197640641, "id": 19, "parentId": 18, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&appDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard%5Capp&appPaths=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750142110166, "traceId": "1eb9b100fb71111e"}, {"name": "add-entry", "duration": 1096818, "timestamp": 20197641328, "id": 26, "parentId": 18, "tags": {"request": "next-app-loader?page=%2F(dashboard)%2Fdrawing-board%2Fpage&name=app%2F(dashboard)%2Fdrawing-board%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fdrawing-board%2Fpage.tsx&appDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard%5Capp&appPaths=%2F(dashboard)%2Fdrawing-board%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750142110166, "traceId": "1eb9b100fb71111e"}, {"name": "add-entry", "duration": 1096807, "timestamp": 20197641343, "id": 27, "parentId": 18, "tags": {"request": "next-app-loader?page=%2F(dashboard)%2Ffeature-update%2Fpage&name=app%2F(dashboard)%2Ffeature-update%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Ffeature-update%2Fpage.tsx&appDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard%5Capp&appPaths=%2F(dashboard)%2Ffeature-update%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750142110166, "traceId": "1eb9b100fb71111e"}, {"name": "add-entry", "duration": 1096588, "timestamp": 20197641564, "id": 28, "parentId": 18, "tags": {"request": "next-app-loader?page=%2F(dashboard)%2Fsurface-data-query%2Fpage&name=app%2F(dashboard)%2Fsurface-data-query%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fsurface-data-query%2Fpage.tsx&appDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard%5Capp&appPaths=%2F(dashboard)%2Fsurface-data-query%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750142110167, "traceId": "1eb9b100fb71111e"}, {"name": "add-entry", "duration": 1096583, "timestamp": 20197641605, "id": 29, "parentId": 18, "tags": {"request": "next-app-loader?page=%2F(dashboard)%2Flog-analysis%2Fpage&name=app%2F(dashboard)%2Flog-analysis%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Flog-analysis%2Fpage.tsx&appDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard%5Capp&appPaths=%2F(dashboard)%2Flog-analysis%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750142110167, "traceId": "1eb9b100fb71111e"}, {"name": "add-entry", "duration": 1096583, "timestamp": 20197641615, "id": 30, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750142110167, "traceId": "1eb9b100fb71111e"}, {"name": "add-entry", "duration": 1246037, "timestamp": 20197641264, "id": 21, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fapi%2Fftp%2Fdownload%2Froute&name=app%2Fapi%2Fftp%2Fdownload%2Froute&pagePath=private-next-app-dir%2Fapi%2Fftp%2Fdownload%2Froute.ts&appDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard%5Capp&appPaths=%2Fapi%2Fftp%2Fdownload%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750142110166, "traceId": "1eb9b100fb71111e"}, {"name": "add-entry", "duration": 1272668, "timestamp": 20197641292, "id": 23, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fapi%2Fsurface-data%2Fpreview%2Froute&name=app%2Fapi%2Fsurface-data%2Fpreview%2Froute&pagePath=private-next-app-dir%2Fapi%2Fsurface-data%2Fpreview%2Froute.ts&appDir=D%3A%5Cpycode%5Csupport_chart2%5Chotel-dashboard%5Capp&appPaths=%2Fapi%2Fsurface-data%2Fpreview%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750142110166, "traceId": "1eb9b100fb71111e"}, {"name": "build-module-tsx", "duration": 41535, "timestamp": 20199023824, "id": 83, "parentId": 16, "tags": {"name": "D:\\pycode\\support_chart2\\hotel-dashboard\\components\\drawing-board\\DrawingBoard.tsx", "layer": "ssr"}, "startTime": 1750142111549, "traceId": "1eb9b100fb71111e"}, {"name": "make", "duration": 2885242, "timestamp": 20197640183, "id": 18, "parentId": 16, "tags": {}, "startTime": 1750142110165, "traceId": "1eb9b100fb71111e"}, {"name": "get-entries", "duration": 3666, "timestamp": 20200527074, "id": 85, "parentId": 84, "tags": {}, "startTime": 1750142113052, "traceId": "1eb9b100fb71111e"}, {"name": "node-file-trace-plugin", "duration": 135351, "timestamp": 20200536546, "id": 86, "parentId": 84, "tags": {"traceEntryCount": "22"}, "startTime": 1750142113062, "traceId": "1eb9b100fb71111e"}, {"name": "collect-traced-files", "duration": 918, "timestamp": 20200671912, "id": 87, "parentId": 84, "tags": {}, "startTime": 1750142113197, "traceId": "1eb9b100fb71111e"}, {"name": "finish-modules", "duration": 145992, "timestamp": 20200526842, "id": 84, "parentId": 17, "tags": {}, "startTime": 1750142113052, "traceId": "1eb9b100fb71111e"}, {"name": "chunk-graph", "duration": 32002, "timestamp": 20200752349, "id": 89, "parentId": 88, "tags": {}, "startTime": 1750142113277, "traceId": "1eb9b100fb71111e"}, {"name": "optimize-modules", "duration": 55, "timestamp": 20200784550, "id": 91, "parentId": 88, "tags": {}, "startTime": 1750142113310, "traceId": "1eb9b100fb71111e"}, {"name": "optimize-chunks", "duration": 29628, "timestamp": 20200785102, "id": 92, "parentId": 88, "tags": {}, "startTime": 1750142113310, "traceId": "1eb9b100fb71111e"}, {"name": "optimize-tree", "duration": 203, "timestamp": 20200814845, "id": 93, "parentId": 88, "tags": {}, "startTime": 1750142113340, "traceId": "1eb9b100fb71111e"}, {"name": "optimize-chunk-modules", "duration": 46549, "timestamp": 20200815151, "id": 94, "parentId": 88, "tags": {}, "startTime": 1750142113340, "traceId": "1eb9b100fb71111e"}, {"name": "optimize", "duration": 77369, "timestamp": 20200784464, "id": 90, "parentId": 88, "tags": {}, "startTime": 1750142113309, "traceId": "1eb9b100fb71111e"}, {"name": "module-hash", "duration": 38978, "timestamp": 20200901677, "id": 95, "parentId": 88, "tags": {}, "startTime": 1750142113427, "traceId": "1eb9b100fb71111e"}, {"name": "code-generation", "duration": 35057, "timestamp": 20200940743, "id": 96, "parentId": 88, "tags": {}, "startTime": 1750142113466, "traceId": "1eb9b100fb71111e"}, {"name": "hash", "duration": 11962, "timestamp": 20200983500, "id": 97, "parentId": 88, "tags": {}, "startTime": 1750142113508, "traceId": "1eb9b100fb71111e"}, {"name": "code-generation-jobs", "duration": 405, "timestamp": 20200995458, "id": 98, "parentId": 88, "tags": {}, "startTime": 1750142113520, "traceId": "1eb9b100fb71111e"}, {"name": "module-assets", "duration": 705, "timestamp": 20200995797, "id": 99, "parentId": 88, "tags": {}, "startTime": 1750142113521, "traceId": "1eb9b100fb71111e"}, {"name": "create-chunk-assets", "duration": 3579, "timestamp": 20200996518, "id": 100, "parentId": 88, "tags": {}, "startTime": 1750142113521, "traceId": "1eb9b100fb71111e"}, {"name": "minify-js", "duration": 5449, "timestamp": 20201015483, "id": 102, "parentId": 101, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1750142113540, "traceId": "1eb9b100fb71111e"}, {"name": "minify-js", "duration": 5276, "timestamp": 20201015673, "id": 103, "parentId": 101, "tags": {"name": "../app/api/feature/update/route.js", "cache": "HIT"}, "startTime": 1750142113541, "traceId": "1eb9b100fb71111e"}, {"name": "minify-js", "duration": 5264, "timestamp": 20201015687, "id": 104, "parentId": 101, "tags": {"name": "../app/api/ftp/download/route.js", "cache": "HIT"}, "startTime": 1750142113541, "traceId": "1eb9b100fb71111e"}, {"name": "minify-js", "duration": 5256, "timestamp": 20201015696, "id": 105, "parentId": 101, "tags": {"name": "../app/api/ftp/search/route.js", "cache": "HIT"}, "startTime": 1750142113541, "traceId": "1eb9b100fb71111e"}, {"name": "minify-js", "duration": 5247, "timestamp": 20201015707, "id": 106, "parentId": 101, "tags": {"name": "../app/api/surface-data/preview/route.js", "cache": "HIT"}, "startTime": 1750142113541, "traceId": "1eb9b100fb71111e"}, {"name": "minify-js", "duration": 5241, "timestamp": 20201015715, "id": 107, "parentId": 101, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1750142113541, "traceId": "1eb9b100fb71111e"}, {"name": "minify-js", "duration": 5234, "timestamp": 20201015723, "id": 108, "parentId": 101, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1750142113541, "traceId": "1eb9b100fb71111e"}, {"name": "minify-js", "duration": 5221, "timestamp": 20201015738, "id": 109, "parentId": 101, "tags": {"name": "../app/(dashboard)/drawing-board/page.js", "cache": "HIT"}, "startTime": 1750142113541, "traceId": "1eb9b100fb71111e"}, {"name": "minify-js", "duration": 5207, "timestamp": 20201015753, "id": 110, "parentId": 101, "tags": {"name": "../app/(dashboard)/feature-update/page.js", "cache": "HIT"}, "startTime": 1750142113541, "traceId": "1eb9b100fb71111e"}, {"name": "minify-js", "duration": 5114, "timestamp": 20201015847, "id": 111, "parentId": 101, "tags": {"name": "../app/(dashboard)/surface-data-query/page.js", "cache": "HIT"}, "startTime": 1750142113541, "traceId": "1eb9b100fb71111e"}, {"name": "minify-js", "duration": 5043, "timestamp": 20201015919, "id": 112, "parentId": 101, "tags": {"name": "../app/(dashboard)/log-analysis/page.js", "cache": "HIT"}, "startTime": 1750142113541, "traceId": "1eb9b100fb71111e"}, {"name": "minify-js", "duration": 5031, "timestamp": 20201015933, "id": 113, "parentId": 101, "tags": {"name": "../app/page.js", "cache": "HIT"}, "startTime": 1750142113541, "traceId": "1eb9b100fb71111e"}, {"name": "minify-js", "duration": 5027, "timestamp": 20201015938, "id": 114, "parentId": 101, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1750142113541, "traceId": "1eb9b100fb71111e"}, {"name": "minify-js", "duration": 5023, "timestamp": 20201015943, "id": 115, "parentId": 101, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1750142113541, "traceId": "1eb9b100fb71111e"}, {"name": "minify-js", "duration": 5015, "timestamp": 20201015952, "id": 116, "parentId": 101, "tags": {"name": "557.js", "cache": "HIT"}, "startTime": 1750142113541, "traceId": "1eb9b100fb71111e"}, {"name": "minify-js", "duration": 5012, "timestamp": 20201015957, "id": 117, "parentId": 101, "tags": {"name": "273.js", "cache": "HIT"}, "startTime": 1750142113541, "traceId": "1eb9b100fb71111e"}, {"name": "minify-js", "duration": 5009, "timestamp": 20201015961, "id": 118, "parentId": 101, "tags": {"name": "403.js", "cache": "HIT"}, "startTime": 1750142113541, "traceId": "1eb9b100fb71111e"}, {"name": "minify-js", "duration": 5005, "timestamp": 20201015966, "id": 119, "parentId": 101, "tags": {"name": "823.js", "cache": "HIT"}, "startTime": 1750142113541, "traceId": "1eb9b100fb71111e"}, {"name": "minify-js", "duration": 5002, "timestamp": 20201015970, "id": 120, "parentId": 101, "tags": {"name": "716.js", "cache": "HIT"}, "startTime": 1750142113541, "traceId": "1eb9b100fb71111e"}, {"name": "minify-js", "duration": 4999, "timestamp": 20201015975, "id": 121, "parentId": 101, "tags": {"name": "787.js", "cache": "HIT"}, "startTime": 1750142113541, "traceId": "1eb9b100fb71111e"}, {"name": "minify-js", "duration": 4996, "timestamp": 20201015979, "id": 122, "parentId": 101, "tags": {"name": "748.js", "cache": "HIT"}, "startTime": 1750142113541, "traceId": "1eb9b100fb71111e"}, {"name": "minify-js", "duration": 4993, "timestamp": 20201015984, "id": 123, "parentId": 101, "tags": {"name": "432.js", "cache": "HIT"}, "startTime": 1750142113541, "traceId": "1eb9b100fb71111e"}, {"name": "minify-js", "duration": 4990, "timestamp": 20201015988, "id": 124, "parentId": 101, "tags": {"name": "508.js", "cache": "HIT"}, "startTime": 1750142113541, "traceId": "1eb9b100fb71111e"}, {"name": "minify-js", "duration": 4985, "timestamp": 20201015994, "id": 125, "parentId": 101, "tags": {"name": "721.js", "cache": "HIT"}, "startTime": 1750142113541, "traceId": "1eb9b100fb71111e"}, {"name": "minify-js", "duration": 4966, "timestamp": 20201016014, "id": 126, "parentId": 101, "tags": {"name": "801.js", "cache": "HIT"}, "startTime": 1750142113541, "traceId": "1eb9b100fb71111e"}, {"name": "minify-js", "duration": 16078, "timestamp": 20201016019, "id": 127, "parentId": 101, "tags": {"name": "357.js", "cache": "MISS"}, "startTime": 1750142113541, "traceId": "1eb9b100fb71111e"}, {"name": "minify-webpack-plugin-optimize", "duration": 27241, "timestamp": 20201004872, "id": 101, "parentId": 16, "tags": {"compilationName": "server", "mangle": "true"}, "startTime": 1750142113530, "traceId": "1eb9b100fb71111e"}, {"name": "css-minimizer-plugin", "duration": 184, "timestamp": 20201032269, "id": 128, "parentId": 16, "tags": {}, "startTime": 1750142113557, "traceId": "1eb9b100fb71111e"}, {"name": "create-trace-assets", "duration": 2236, "timestamp": 20201032681, "id": 129, "parentId": 17, "tags": {}, "startTime": 1750142113558, "traceId": "1eb9b100fb71111e"}, {"name": "create-trace-assets", "duration": 1505, "timestamp": 20201035146, "id": 130, "parentId": 17, "tags": {}, "startTime": 1750142113560, "traceId": "1eb9b100fb71111e"}, {"name": "seal", "duration": 340302, "timestamp": 20200710352, "id": 88, "parentId": 16, "tags": {}, "startTime": 1750142113235, "traceId": "1eb9b100fb71111e"}, {"name": "webpack-compilation", "duration": 3442632, "timestamp": 20197628957, "id": 16, "parentId": 13, "tags": {"name": "server"}, "startTime": 1750142110154, "traceId": "1eb9b100fb71111e"}, {"name": "emit", "duration": 37936, "timestamp": 20201072165, "id": 131, "parentId": 13, "tags": {}, "startTime": 1750142113597, "traceId": "1eb9b100fb71111e"}, {"name": "webpack-close", "duration": 76789, "timestamp": 20201113903, "id": 132, "parentId": 13, "tags": {"name": "server"}, "startTime": 1750142113639, "traceId": "1eb9b100fb71111e"}, {"name": "webpack-generate-error-stats", "duration": 4717, "timestamp": 20201190809, "id": 133, "parentId": 132, "tags": {}, "startTime": 1750142113716, "traceId": "1eb9b100fb71111e"}, {"name": "make", "duration": 258, "timestamp": 20201207396, "id": 135, "parentId": 134, "tags": {}, "startTime": 1750142113732, "traceId": "1eb9b100fb71111e"}, {"name": "chunk-graph", "duration": 42, "timestamp": 20201208482, "id": 137, "parentId": 136, "tags": {}, "startTime": 1750142113733, "traceId": "1eb9b100fb71111e"}, {"name": "optimize-modules", "duration": 10, "timestamp": 20201208586, "id": 139, "parentId": 136, "tags": {}, "startTime": 1750142113734, "traceId": "1eb9b100fb71111e"}, {"name": "optimize-chunks", "duration": 98, "timestamp": 20201208666, "id": 140, "parentId": 136, "tags": {}, "startTime": 1750142113734, "traceId": "1eb9b100fb71111e"}, {"name": "optimize-tree", "duration": 11, "timestamp": 20201208824, "id": 141, "parentId": 136, "tags": {}, "startTime": 1750142113734, "traceId": "1eb9b100fb71111e"}, {"name": "optimize-chunk-modules", "duration": 61, "timestamp": 20201208912, "id": 142, "parentId": 136, "tags": {}, "startTime": 1750142113734, "traceId": "1eb9b100fb71111e"}, {"name": "optimize", "duration": 475, "timestamp": 20201208545, "id": 138, "parentId": 136, "tags": {}, "startTime": 1750142113734, "traceId": "1eb9b100fb71111e"}, {"name": "module-hash", "duration": 19, "timestamp": 20201209335, "id": 143, "parentId": 136, "tags": {}, "startTime": 1750142113734, "traceId": "1eb9b100fb71111e"}, {"name": "code-generation", "duration": 14, "timestamp": 20201209370, "id": 144, "parentId": 136, "tags": {}, "startTime": 1750142113734, "traceId": "1eb9b100fb71111e"}, {"name": "hash", "duration": 84, "timestamp": 20201209442, "id": 145, "parentId": 136, "tags": {}, "startTime": 1750142113734, "traceId": "1eb9b100fb71111e"}, {"name": "code-generation-jobs", "duration": 58, "timestamp": 20201209525, "id": 146, "parentId": 136, "tags": {}, "startTime": 1750142113734, "traceId": "1eb9b100fb71111e"}, {"name": "module-assets", "duration": 21, "timestamp": 20201209569, "id": 147, "parentId": 136, "tags": {}, "startTime": 1750142113735, "traceId": "1eb9b100fb71111e"}, {"name": "create-chunk-assets", "duration": 19, "timestamp": 20201209598, "id": 148, "parentId": 136, "tags": {}, "startTime": 1750142113735, "traceId": "1eb9b100fb71111e"}, {"name": "minify-js", "duration": 49, "timestamp": 20201215172, "id": 150, "parentId": 149, "tags": {"name": "interception-route-rewrite-manifest.js", "cache": "HIT"}, "startTime": 1750142113740, "traceId": "1eb9b100fb71111e"}, {"name": "minify-webpack-plugin-optimize", "duration": 1103, "timestamp": 20201214130, "id": 149, "parentId": 134, "tags": {"compilationName": "edge-server", "mangle": "true"}, "startTime": 1750142113739, "traceId": "1eb9b100fb71111e"}, {"name": "css-minimizer-plugin", "duration": 9, "timestamp": 20201215334, "id": 151, "parentId": 134, "tags": {}, "startTime": 1750142113740, "traceId": "1eb9b100fb71111e"}, {"name": "seal", "duration": 9267, "timestamp": 20201208300, "id": 136, "parentId": 134, "tags": {}, "startTime": 1750142113733, "traceId": "1eb9b100fb71111e"}, {"name": "webpack-compilation", "duration": 12279, "timestamp": 20201205421, "id": 134, "parentId": 13, "tags": {"name": "edge-server"}, "startTime": 1750142113730, "traceId": "1eb9b100fb71111e"}, {"name": "emit", "duration": 1329, "timestamp": 20201217773, "id": 152, "parentId": 13, "tags": {}, "startTime": 1750142113743, "traceId": "1eb9b100fb71111e"}, {"name": "webpack-close", "duration": 331, "timestamp": 20201220135, "id": 153, "parentId": 13, "tags": {"name": "edge-server"}, "startTime": 1750142113745, "traceId": "1eb9b100fb71111e"}, {"name": "webpack-generate-error-stats", "duration": 785, "timestamp": 20201220476, "id": 154, "parentId": 153, "tags": {}, "startTime": 1750142113745, "traceId": "1eb9b100fb71111e"}, {"name": "add-entry", "duration": 381534, "timestamp": 20201230552, "id": 165, "parentId": 156, "tags": {"request": "next-flight-client-entry-loader?server=false!"}, "startTime": 1750142113756, "traceId": "1eb9b100fb71111e"}, {"name": "add-entry", "duration": 381550, "timestamp": 20201230561, "id": 166, "parentId": 156, "tags": {"request": "next-flight-client-entry-loader?server=false!"}, "startTime": 1750142113756, "traceId": "1eb9b100fb71111e"}]