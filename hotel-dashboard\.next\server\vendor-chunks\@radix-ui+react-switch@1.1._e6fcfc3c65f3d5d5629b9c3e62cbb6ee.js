"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-switch@1.1._e6fcfc3c65f3d5d5629b9c3e62cbb6ee";
exports.ids = ["vendor-chunks/@radix-ui+react-switch@1.1._e6fcfc3c65f3d5d5629b9c3e62cbb6ee"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-switch@1.1._e6fcfc3c65f3d5d5629b9c3e62cbb6ee/node_modules/@radix-ui/react-switch/dist/index.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-switch@1.1._e6fcfc3c65f3d5d5629b9c3e62cbb6ee/node_modules/@radix-ui/react-switch/dist/index.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Switch: () => (/* binding */ Switch),\n/* harmony export */   SwitchThumb: () => (/* binding */ SwitchThumb),\n/* harmony export */   Thumb: () => (/* binding */ Thumb),\n/* harmony export */   createSwitchScope: () => (/* binding */ createSwitchScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_2a0e526a8f7e7aada080206d385bb572/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_4fe40d510edca7ae4ca9c92afeb1ae6d/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_84b05e8d8acde331bf79519153011e46/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-previou_f62eb26ebf07111fa37167228db23a06/node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-size@1._25ea7b66547079fee23d7c838aabe8ed/node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_997b35f2e2aa9d3174fc03a0f79e437b/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Root,Switch,SwitchThumb,Thumb,createSwitchScope auto */ // packages/react/switch/src/Switch.tsx\n\n\n\n\n\n\n\n\n\nvar SWITCH_NAME = \"Switch\";\nvar [createSwitchContext, createSwitchScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(SWITCH_NAME);\nvar [SwitchProvider, useSwitchContext] = createSwitchContext(SWITCH_NAME);\nvar Switch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSwitch, name, checked: checkedProp, defaultChecked, required, disabled, value = \"on\", onCheckedChange, form, ...switchProps } = props;\n    const [button, setButton] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"Switch.useComposedRefs[composedRefs]\": (node)=>setButton(node)\n    }[\"Switch.useComposedRefs[composedRefs]\"]);\n    const hasConsumerStoppedPropagationRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const isFormControl = button ? form || !!button.closest(\"form\") : true;\n    const [checked = false, setChecked] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__.useControllableState)({\n        prop: checkedProp,\n        defaultProp: defaultChecked,\n        onChange: onCheckedChange\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(SwitchProvider, {\n        scope: __scopeSwitch,\n        checked,\n        disabled,\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.button, {\n                type: \"button\",\n                role: \"switch\",\n                \"aria-checked\": checked,\n                \"aria-required\": required,\n                \"data-state\": getState(checked),\n                \"data-disabled\": disabled ? \"\" : void 0,\n                disabled,\n                value,\n                ...switchProps,\n                ref: composedRefs,\n                onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onClick, (event)=>{\n                    setChecked((prevChecked)=>!prevChecked);\n                    if (isFormControl) {\n                        hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n                        if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n                    }\n                })\n            }),\n            isFormControl && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(BubbleInput, {\n                control: button,\n                bubbles: !hasConsumerStoppedPropagationRef.current,\n                name,\n                value,\n                checked,\n                required,\n                disabled,\n                form,\n                style: {\n                    transform: \"translateX(-100%)\"\n                }\n            })\n        ]\n    });\n});\nSwitch.displayName = SWITCH_NAME;\nvar THUMB_NAME = \"SwitchThumb\";\nvar SwitchThumb = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSwitch, ...thumbProps } = props;\n    const context = useSwitchContext(THUMB_NAME, __scopeSwitch);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.span, {\n        \"data-state\": getState(context.checked),\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        ...thumbProps,\n        ref: forwardedRef\n    });\n});\nSwitchThumb.displayName = THUMB_NAME;\nvar BubbleInput = (props)=>{\n    const { control, checked, bubbles = true, ...inputProps } = props;\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevChecked = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_7__.usePrevious)(checked);\n    const controlSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_8__.useSize)(control);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"BubbleInput.useEffect\": ()=>{\n            const input = ref.current;\n            const inputProto = window.HTMLInputElement.prototype;\n            const descriptor = Object.getOwnPropertyDescriptor(inputProto, \"checked\");\n            const setChecked = descriptor.set;\n            if (prevChecked !== checked && setChecked) {\n                const event = new Event(\"click\", {\n                    bubbles\n                });\n                setChecked.call(input, checked);\n                input.dispatchEvent(event);\n            }\n        }\n    }[\"BubbleInput.useEffect\"], [\n        prevChecked,\n        checked,\n        bubbles\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"input\", {\n        type: \"checkbox\",\n        \"aria-hidden\": true,\n        defaultChecked: checked,\n        ...inputProps,\n        tabIndex: -1,\n        ref,\n        style: {\n            ...props.style,\n            ...controlSize,\n            position: \"absolute\",\n            pointerEvents: \"none\",\n            opacity: 0,\n            margin: 0\n        }\n    });\n};\nfunction getState(checked) {\n    return checked ? \"checked\" : \"unchecked\";\n}\nvar Root = Switch;\nvar Thumb = SwitchThumb;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-switch@1.1._e6fcfc3c65f3d5d5629b9c3e62cbb6ee/node_modules/@radix-ui/react-switch/dist/index.mjs\n");

/***/ })

};
;