"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-radio-group_f14b22c1a4c655f0e40a4b078456f1e4";
exports.ids = ["vendor-chunks/@radix-ui+react-radio-group_f14b22c1a4c655f0e40a4b078456f1e4"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-radio-group_f14b22c1a4c655f0e40a4b078456f1e4/node_modules/@radix-ui/react-radio-group/dist/index.mjs":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-radio-group_f14b22c1a4c655f0e40a4b078456f1e4/node_modules/@radix-ui/react-radio-group/dist/index.mjs ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Indicator: () => (/* binding */ Indicator),\n/* harmony export */   Item: () => (/* binding */ Item2),\n/* harmony export */   RadioGroup: () => (/* binding */ RadioGroup),\n/* harmony export */   RadioGroupIndicator: () => (/* binding */ RadioGroupIndicator),\n/* harmony export */   RadioGroupItem: () => (/* binding */ RadioGroupItem),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   createRadioGroupScope: () => (/* binding */ createRadioGroupScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_2a0e526a8f7e7aada080206d385bb572/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_4fe40d510edca7ae4ca9c92afeb1ae6d/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_997b35f2e2aa9d3174fc03a0f79e437b/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-roving-focus */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-roving-focu_5bba7bb9eb588b71fd9245f49300584b/node_modules/@radix-ui/react-roving-focus/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_84b05e8d8acde331bf79519153011e46/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1_033f2d9864c311ebc46e3d65a72ec4b7/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-size@1._25ea7b66547079fee23d7c838aabe8ed/node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-previou_f62eb26ebf07111fa37167228db23a06/node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1._d340d9d39508cb250c25fc66451df4dd/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Indicator,Item,RadioGroup,RadioGroupIndicator,RadioGroupItem,Root,createRadioGroupScope auto */ // packages/react/radio-group/src/RadioGroup.tsx\n\n\n\n\n\n\n\n\n\n// packages/react/radio-group/src/Radio.tsx\n\n\n\n\n\n\n\n\n\nvar RADIO_NAME = \"Radio\";\nvar [createRadioContext, createRadioScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(RADIO_NAME);\nvar [RadioProvider, useRadioContext] = createRadioContext(RADIO_NAME);\nvar Radio = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeRadio, name, checked = false, required, disabled, value = \"on\", onCheck, form, ...radioProps } = props;\n    const [button, setButton] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"Radio.useComposedRefs[composedRefs]\": (node)=>setButton(node)\n    }[\"Radio.useComposedRefs[composedRefs]\"]);\n    const hasConsumerStoppedPropagationRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const isFormControl = button ? form || !!button.closest(\"form\") : true;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(RadioProvider, {\n        scope: __scopeRadio,\n        checked,\n        disabled,\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.button, {\n                type: \"button\",\n                role: \"radio\",\n                \"aria-checked\": checked,\n                \"data-state\": getState(checked),\n                \"data-disabled\": disabled ? \"\" : void 0,\n                disabled,\n                value,\n                ...radioProps,\n                ref: composedRefs,\n                onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onClick, (event)=>{\n                    if (!checked) onCheck?.();\n                    if (isFormControl) {\n                        hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n                        if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n                    }\n                })\n            }),\n            isFormControl && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(BubbleInput, {\n                control: button,\n                bubbles: !hasConsumerStoppedPropagationRef.current,\n                name,\n                value,\n                checked,\n                required,\n                disabled,\n                form,\n                style: {\n                    transform: \"translateX(-100%)\"\n                }\n            })\n        ]\n    });\n});\nRadio.displayName = RADIO_NAME;\nvar INDICATOR_NAME = \"RadioIndicator\";\nvar RadioIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeRadio, forceMount, ...indicatorProps } = props;\n    const context = useRadioContext(INDICATOR_NAME, __scopeRadio);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || context.checked,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.span, {\n            \"data-state\": getState(context.checked),\n            \"data-disabled\": context.disabled ? \"\" : void 0,\n            ...indicatorProps,\n            ref: forwardedRef\n        })\n    });\n});\nRadioIndicator.displayName = INDICATOR_NAME;\nvar BubbleInput = (props)=>{\n    const { control, checked, bubbles = true, ...inputProps } = props;\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevChecked = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_7__.usePrevious)(checked);\n    const controlSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_8__.useSize)(control);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"BubbleInput.useEffect\": ()=>{\n            const input = ref.current;\n            const inputProto = window.HTMLInputElement.prototype;\n            const descriptor = Object.getOwnPropertyDescriptor(inputProto, \"checked\");\n            const setChecked = descriptor.set;\n            if (prevChecked !== checked && setChecked) {\n                const event = new Event(\"click\", {\n                    bubbles\n                });\n                setChecked.call(input, checked);\n                input.dispatchEvent(event);\n            }\n        }\n    }[\"BubbleInput.useEffect\"], [\n        prevChecked,\n        checked,\n        bubbles\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"input\", {\n        type: \"radio\",\n        \"aria-hidden\": true,\n        defaultChecked: checked,\n        ...inputProps,\n        tabIndex: -1,\n        ref,\n        style: {\n            ...props.style,\n            ...controlSize,\n            position: \"absolute\",\n            pointerEvents: \"none\",\n            opacity: 0,\n            margin: 0\n        }\n    });\n};\nfunction getState(checked) {\n    return checked ? \"checked\" : \"unchecked\";\n}\n// packages/react/radio-group/src/RadioGroup.tsx\n\nvar ARROW_KEYS = [\n    \"ArrowUp\",\n    \"ArrowDown\",\n    \"ArrowLeft\",\n    \"ArrowRight\"\n];\nvar RADIO_GROUP_NAME = \"RadioGroup\";\nvar [createRadioGroupContext, createRadioGroupScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(RADIO_GROUP_NAME, [\n    _radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_9__.createRovingFocusGroupScope,\n    createRadioScope\n]);\nvar useRovingFocusGroupScope = (0,_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_9__.createRovingFocusGroupScope)();\nvar useRadioScope = createRadioScope();\nvar [RadioGroupProvider, useRadioGroupContext] = createRadioGroupContext(RADIO_GROUP_NAME);\nvar RadioGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeRadioGroup, name, defaultValue, value: valueProp, required = false, disabled = false, orientation, dir, loop = true, onValueChange, ...groupProps } = props;\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeRadioGroup);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_10__.useDirection)(dir);\n    const [value, setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_11__.useControllableState)({\n        prop: valueProp,\n        defaultProp: defaultValue,\n        onChange: onValueChange\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(RadioGroupProvider, {\n        scope: __scopeRadioGroup,\n        name,\n        required,\n        disabled,\n        value,\n        onValueChange: setValue,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_9__.Root, {\n            asChild: true,\n            ...rovingFocusGroupScope,\n            orientation,\n            dir: direction,\n            loop,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n                role: \"radiogroup\",\n                \"aria-required\": required,\n                \"aria-orientation\": orientation,\n                \"data-disabled\": disabled ? \"\" : void 0,\n                dir: direction,\n                ...groupProps,\n                ref: forwardedRef\n            })\n        })\n    });\n});\nRadioGroup.displayName = RADIO_GROUP_NAME;\nvar ITEM_NAME = \"RadioGroupItem\";\nvar RadioGroupItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeRadioGroup, disabled, ...itemProps } = props;\n    const context = useRadioGroupContext(ITEM_NAME, __scopeRadioGroup);\n    const isDisabled = context.disabled || disabled;\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeRadioGroup);\n    const radioScope = useRadioScope(__scopeRadioGroup);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref);\n    const checked = context.value === itemProps.value;\n    const isArrowKeyPressedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"RadioGroupItem.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"RadioGroupItem.useEffect.handleKeyDown\": (event)=>{\n                    if (ARROW_KEYS.includes(event.key)) {\n                        isArrowKeyPressedRef.current = true;\n                    }\n                }\n            }[\"RadioGroupItem.useEffect.handleKeyDown\"];\n            const handleKeyUp = {\n                \"RadioGroupItem.useEffect.handleKeyUp\": ()=>isArrowKeyPressedRef.current = false\n            }[\"RadioGroupItem.useEffect.handleKeyUp\"];\n            document.addEventListener(\"keydown\", handleKeyDown);\n            document.addEventListener(\"keyup\", handleKeyUp);\n            return ({\n                \"RadioGroupItem.useEffect\": ()=>{\n                    document.removeEventListener(\"keydown\", handleKeyDown);\n                    document.removeEventListener(\"keyup\", handleKeyUp);\n                }\n            })[\"RadioGroupItem.useEffect\"];\n        }\n    }[\"RadioGroupItem.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_9__.Item, {\n        asChild: true,\n        ...rovingFocusGroupScope,\n        focusable: !isDisabled,\n        active: checked,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Radio, {\n            disabled: isDisabled,\n            required: context.required,\n            checked,\n            ...radioScope,\n            ...itemProps,\n            name: context.name,\n            ref: composedRefs,\n            onCheck: ()=>context.onValueChange(itemProps.value),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)((event)=>{\n                if (event.key === \"Enter\") event.preventDefault();\n            }),\n            onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(itemProps.onFocus, ()=>{\n                if (isArrowKeyPressedRef.current) ref.current?.click();\n            })\n        })\n    });\n});\nRadioGroupItem.displayName = ITEM_NAME;\nvar INDICATOR_NAME2 = \"RadioGroupIndicator\";\nvar RadioGroupIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeRadioGroup, ...indicatorProps } = props;\n    const radioScope = useRadioScope(__scopeRadioGroup);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(RadioIndicator, {\n        ...radioScope,\n        ...indicatorProps,\n        ref: forwardedRef\n    });\n});\nRadioGroupIndicator.displayName = INDICATOR_NAME2;\nvar Root2 = RadioGroup;\nvar Item2 = RadioGroupItem;\nvar Indicator = RadioGroupIndicator;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-radio-group_f14b22c1a4c655f0e40a4b078456f1e4/node_modules/@radix-ui/react-radio-group/dist/index.mjs\n");

/***/ })

};
;