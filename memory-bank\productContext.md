# Product Context

This file provides a high-level overview of the project and the expected product that will be created. Initially it is based upon projectBrief.md (if provided) and all other available project-related information in the working directory. This file is intended to be updated as the project evolves, and should be used to inform all other modes of the project's goals and context.
2025-06-10 15:10:34 - Log of updates made will be appended as footnotes to the end of this file.

*

## Project Goal

*   将 `web_tool` 项目中的核心日志文件上传、处理和图表展示功能集成到 `hotel-dashboard` 项目的 UI 中。

## Key Features

*   **UI 调整:** 在 `hotel-dashboard` 的侧边栏中添加新的 "日志分析" 部分，并为其创建主内容区域。
*   **React 组件创建:**
    *   `LogFileUpload.tsx`: 处理文件上传、解码和与 Web Worker 的通信。现在还负责在文件处理成功后显示日志文件的总行数、起始时间和结束时间。
    *   `LogDisplayArea.tsx`: 管理已处理的数据块，以可滚动列表形式展示，提供复选框允许用户选择一个或多个数据块进行分析，并包含全选/全不选功能。通过回调将选定数据传递给父页面。
    *   `LogChartView.tsx`: 使用 `recharts` 显示胶厚和准直数据的图表，数据来源于父页面传递的选中数据块。
*   **Web Worker 实现:** 将 `web_tool` 中的日志解析逻辑 (`logProcessor.js`) 迁移到 `hotel-dashboard` 项目中的新 Web Worker (`logParser.worker.js` 或 `.ts`)。
*   **数据流:**
    1.  用户通过 `LogFileUpload.tsx` 上传日志文件。
    2.  Web Worker (`logParser.worker.ts`) 解析文件，生成 `ProcessedBlock[]`。
    3.  `LogFileUpload.tsx` 将 `ProcessedBlock[]` 通过回调传递给父组件 `page.tsx`。
    4.  `page.tsx` 将 `ProcessedBlock[]` 传递给 `LogDisplayArea.tsx` 进行展示。
    5.  用户在 `LogDisplayArea.tsx` 中选择数据块。
    6.  `LogDisplayArea.tsx` 将选定的 `ProcessedBlock[]` 通过回调传递给 `page.tsx`。
    7.  `page.tsx` 将选定的 `ProcessedBlock[]` 传递给 `LogChartView.tsx` 进行可视化。
*   **图片导出功能:**
    *   允许用户将 `LogChartView.tsx` 中的图表导出为 PNG/JPEG 图片。
    *   允许用户将 `LogDisplayArea.tsx` 中的整个内容区域导出为 PNG/JPEG 图片。
    *   使用 `dom-to-image-more` 库进行 DOM 到图片的转换。
    *   通过新的辅助工具模块 `lib/exportUtils.ts` 实现导出逻辑。
*   **面形数据查询 (Surface Data Query):**
    *   新页面 (`surface-data-query`) 用于通过FTP查询和下载面形数据。
    *   支持通过SN（正则表达式）搜索大/小棱镜数据。
    *   结果列表最多显示50条，支持多选下载。
    *   后端API处理FTP连接、文件搜索和文件下载。
*   **面形数据预览 (Surface Data Preview):**
    *   在面形数据查询结果中提供文件预览功能。
    *   后端API负责从FTP下载、解压文件，解析XYZ点云数据，并渲染俯视图。
    *   前端模态框内使用Canvas显示渲染后的预览图。
*   **Feature Code 更新:**
    *   提供一个UI界面，用于输入SN和Feature Code。
    *   通过调用后端的API，触发一个C#编写的包装器控制台应用。
    *   该C#应用负责调用一个特定的DLL，以执行核心的Feature Code更新逻辑。

## Overall Architecture

*   `hotel-dashboard` (Next.js, shadcn/ui) 将作为前端 UI。
*   新的 React 组件将负责日志上传、数据显示和图表渲染。
*   Web Worker 将在后台处理日志解析，以避免阻塞主线程。
*   `recharts` 库将用于图表展示。
*   `dom-to-image-more` 库用于实现DOM到图片的导出功能。
*   新的工具模块 `hotel-dashboard/lib/exportUtils.ts` 将包含通用的图片导出辅助函数。
*   新的API端点 (`/api/ftp/search`, `/api/ftp/download`) 将处理与FTP服务器的交互，用于面形数据查询。
*   新的API端点 (`/api/surface-data/preview`) 将处理面形数据文件的下载、解压、解析和俯视图渲染。
*   FTP凭证安全存储在服务器端环境变量中。
---
**2025-06-11 13:10:46 - 文档更新**
*   更新了 [`LOG_ANALYSIS_DEVELOPER_DOCS.md`](LOG_ANALYSIS_DEVELOPER_DOCS.md:1) 和 [`LOG_ANALYSIS_USER_GUIDE.md`](LOG_ANALYSIS_USER_GUIDE.md:1) 以反映 [`hotel-dashboard/components/log-analysis/LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx:1) 组件中文件输入框 UI 的最新更改（采用自定义两段式样式，隐藏标准 input，并更新了相关状态和处理函数）。

[2025-06-11 13:17:00] - 添加图片导出功能到关键特性和整体架构描述。
---
**2025-06-11 13:27:00 - 文档更新 (图片导出功能)**
*   更新了 [`LOG_ANALYSIS_DEVELOPER_DOCS.md`](LOG_ANALYSIS_DEVELOPER_DOCS.md:1) 以包含对新模块 [`hotel-dashboard/lib/exportUtils.ts`](hotel-dashboard/lib/exportUtils.ts:1) 的说明，以及在 [`hotel-dashboard/components/log-analysis/LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx:1) 和 [`hotel-dashboard/components/log-analysis/LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx:1) 中图片导出功能的实现细节。
*   更新了 [`LOG_ANALYSIS_USER_GUIDE.md`](LOG_ANALYSIS_USER_GUIDE.md:1) 以说明如何使用新增的图片导出功能。
---
**2025-06-11 13:51:00 - 文档更新 (图片导出功能增强)**
*   更新了 [`LOG_ANALYSIS_DEVELOPER_DOCS.md`](LOG_ANALYSIS_DEVELOPER_DOCS.md:1) 中关于 [`hotel-dashboard/lib/exportUtils.ts`](hotel-dashboard/lib/exportUtils.ts:1) 的 `exportElementAsImage` 函数描述，以包含为解决图片内容截断问题而引入的新选项 (`width`, `height`, `style`, `cacheBust`)。这反映了图片导出功能的健壮性改进。
---
**2025-06-11 14:15:22 - 文档更新 (图片导出功能修复)**
*   更新了 [`LOG_ANALYSIS_DEVELOPER_DOCS.md`](LOG_ANALYSIS_DEVELOPER_DOCS.md:1) 中关于 [`hotel-dashboard/lib/exportUtils.ts`](hotel-dashboard/lib/exportUtils.ts:1) 的 `exportElementAsImage` 函数描述。修复包括：
    *   `fileName` 参数现在接收不带扩展名的文件名，并在函数内部附加类型后缀。
    *   `options.style` 中添加了更全面的样式重置 (`border: 'none !important'`, `margin: '0 !important'`, `padding: '0 !important'`, `box-shadow: 'none !important'`)，以消除不必要的边框并改善视觉一致性。
*   这些修复解决了图片导出时文件名错误 (`png.png`) 和视觉不一致（多余边框及截断）的问题，提升了功能的稳定性和输出质量。
*   确认 [`LOG_ANALYSIS_USER_GUIDE.md`](LOG_ANALYSIS_USER_GUIDE.md:1) 无需更新，因为这些是后端修复，不影响用户操作。
---
[2025-06-12 10:18:46] - 添加面形数据查询 (Surface Data Query) 功能到关键特性和整体架构描述。
[2025-06-12 11:12:00] - 更新面形数据查询 (Surface Data Query) 功能描述，搜索结果限制增加到50条。
[2025-06-12 11:34:40] - 添加面形数据预览 (Surface Data Preview) 功能到关键特性和整体架构描述。