# System Patterns *Optional*

This file documents recurring patterns and standards used in the project.
It is optional, but recommended to be updated as the project evolves.
2025-06-10 15:11:00 - Log of updates made.

*

## Coding Patterns

*   

## Architectural Patterns

*   

## Testing Patterns

*
---
### Architectural Patterns
[2025-06-11 10:15:00] - **响应式并列布局模式 (Flexbox)**
*   **描述:** 对于需要将两个或多个主要功能区域在较大屏幕上并排显示，而在较小屏幕上垂直堆叠以优化空间利用率的场景，推荐使用 Flexbox 布局。
*   **实现 (Tailwind CSS):** 在父容器上应用如 `flex flex-col md:flex-row gap-x` (例如 `gap-8`) 的类。
    *   `flex`: 启用 Flexbox。
    *   `flex-col`: 默认垂直排列（移动优先）。
    *   `md:flex-row`: 在中等断点 (md) 及以上切换到水平排列。
    *   `gap-x`: 定义项目之间的水平间距。
*   **适用场景:**
    *   仪表盘页面中不同信息面板的排列。
    *   表单与预览区域的并列。
    *   主内容区与侧边信息栏的组合。
*   **示例:** 用于 [`hotel-dashboard/app/(dashboard)/log-analysis/page.tsx`](hotel-dashboard/app/(dashboard)/log-analysis/page.tsx) 中，将文件上传区 ([`LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx)) 与数据显示/选择区 ([`LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx)) 并排布局。

---
### Architectural Patterns
[2025-06-11 10:50:00] - **嵌套 Flexbox 溢出控制模式 (Tailwind CSS)**
*   **问题描述:** 当一个具有固定高度的父 Flexbox 容器 (例如，`Card` 与 `h-fixed`) 包含一个 Flex item (`flex-grow`，例如 `CardContent`)，而这个 item 自身又是一个 Flex 容器 (`flex flex-col`) 并包含一个可滚动的子元素 (`overflow-y-auto flex-grow`) 时，可能会出现以下问题：
    1.  内部可滚动元素无法正确计算其应占高度，导致不出现滚动条或撑破其直接父级 (`CardContent`)。
    2.  `CardContent` 被其内部可滚动元素撑破，导致最外层的固定高度 `Card` 也被撑破。
*   **解决方案 (组合模式):**
    1.  **内部可滚动元素:** 在设置了 `flex-grow` 和 `overflow-y-auto` 的可滚动 `div` 上添加 `min-h-0`。这为其提供了一个明确的最小高度基线，使其能在其父容器 (`CardContent`) 分配的空间内正确计算高度并启用滚动。
        *   示例: `<div class="flex-grow overflow-y-auto min-h-0">...</div>`
    2.  **中间 Flex Item (`CardContent`):** 在作为可滚动元素直接父级的 Flex item (`flex-grow flex flex-col`) 上添加 `overflow-hidden`。这确保该 item (例如 `CardContent`) 会裁剪掉任何超出其由父容器 (`Card`) 分配高度的内容，防止被内部可滚动元素撑大。
        *   示例: `<CardContent class="flex-grow flex flex-col overflow-hidden">...</CardContent>`
*   **适用场景:**
    *   具有固定高度卡片 (`Card`)，卡片内容 (`CardContent`) 需要填满卡片剩余空间，并且卡片内容中包含一个应独立滚动的列表或区域。
*   **示例:** 用于 [`hotel-dashboard/components/log-analysis/LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx) 中，确保在固定高度的 `Card` 内，`CardContent` 能正确管理其内部可滚动列表的高度和溢出。
---
### Recharts Patterns
[2025-06-11 11:31:00] - **Recharts 标签精确定位模式**
*   **上下文:** 在 Recharts 图表 ([`recharts`](https://recharts.org/)) 中，需要对标签（如轴标签、数据点标签）的 X 和 Y 坐标进行精确控制，特别是在 [`hotel-dashboard/components/log-analysis/LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx:1) 等组件中。
*   **核心策略:**
    1.  **`<Label />` 组件或轴的 `label` 属性:** 这是添加标签的基础。
    2.  **标准定位属性:**
        *   `position`: 字符串值 (如 `'top'`, `'center'`, `'insideLeft'`, `'outerStart'`)，用于粗略定位。
        *   `offset`: 数字，基于 `position` 的偏移。
        *   `x`, `y`: 数字，提供相对于其容器的绝对坐标。具体参照系可能因 `<Label />` 的上下文而异（例如，在 `<XAxis label={{ value: 'Time', position: 'insideBottom', offset: -10 }} />` 中，`offset` 和 `position` 结合影响最终位置；在 `<Label value="Max" x={100} y={50} />` 中，`x` 和 `y` 是直接坐标）。
        *   `dx`, `dy`: 数字，基于 Recharts 计算出的位置进行微调。
    3.  **自定义渲染 (`content` 属性):**
        *   允许传递一个自定义 React 元素或一个返回 React 元素的渲染函数 (e.g., `content={<CustomLabelComponent x={calculatedX} y={calculatedY} />}` or `content={props => <CustomLabelComponent {...props} customX={...} />}`).
        *   **优点:** 提供对标签渲染和定位逻辑的完全控制。可以实现复杂的布局、动态坐标计算、条件渲染等。
        *   **缺点:** 增加代码复杂性，需要维护自定义组件。自定义组件内部的 `x` 和 `y` 属性通常直接作为 SVG 坐标使用。
*   **模式选择指南:**
    *   **简单定位:** 对于标准需求（如轴标题居中、数据点标签在条形内部/外部），优先使用 `position`, `offset`, `dx`, `dy`。
    *   **精确/动态定位:**
        *   如果需要基于图表数据或外部状态动态计算精确坐标，或者标准属性无法满足布局要求，应使用 `content` 属性。
        *   在自定义组件内部，可以直接设置传递给其内部 SVG 文本元素 (或包装器) 的 `x` 和 `y` 属性。
        *   也可以利用传递给 `content` 渲染函数的 `props` (如 `viewBox`, `x`, `y` 等，这些是 Recharts 计算出的原始位置信息) 作为计算自定义坐标的基础。
*   **示例 (使用 `content` 进行自定义定位):**
    ```typescript jsx
    // 在 LogChartView.tsx 或类似组件中
    import { Label, XAxis, Text } from 'recharts';

    const CustomXAxisTick = (props) => {
      const { x, y, payload, anAngle } = props; // payload.value 是刻度值
      // 复杂的定位逻辑可以放在这里
      const customX = x + 5; // 示例：向右偏移5px
      const customY = y + 10; // 示例：向下偏移10px
      return (
        &lt;g transform={`translate(${customX},${customY})`}&gt;
          &lt;Text angle={anAngle || 0} textAnchor="middle" dominantBaseline="middle"&gt;
            {payload.value}
          &lt;/Text&gt;
        &lt;/g&gt;
      );
    };

    // ... later in the chart definition
    // &lt;XAxis dataKey="time" tick={&lt;CustomXAxisTick anAngle={-35} /&gt;} /&gt;

    // 或者用于 Label
    const MyCustomLabel = ({ viewBox, value, x, y, customOffset }) => {
      const { x: vbX, y: vbY, width, height } = viewBox; // viewBox of the element this label is attached to
      // Calculate precise x, y based on viewBox or passed x, y
      const finalX = x + (customOffset?.x || 0); // Use Recharts' calculated x as a base
      const finalY = y + (customOffset?.y || 0); // Use Recharts' calculated y as a base
      return &lt;Text x={finalX} y={finalY} fill="#8884d8" textAnchor="middle" dominantBaseline="middle"&gt;{value}&lt;/Text&gt;;
    };

    // &lt;Label content={&lt;MyCustomLabel value="My Label Text" customOffset={{ x: 10, y: -5 }} /&gt;} /&gt;
    // 或者直接在 LabelList 中使用
    // &lt;LabelList dataKey="name" content={&lt;MyCustomLabel customOffset={{ x: 0, y: -10 }} /&gt;} /&gt;
    ```
*   **注意事项:**
    *   使用 `content` 时，传递给自定义组件的 `props` (如 `x`, `y`, `viewBox`) 是 Recharts 根据其内部布局计算得出的，可以作为自定义定位的起点。
    *   `viewBox` 属性（如果可用）提供了其所附加元素（如条形、扇区）的边界框信息，对于在其内部或周围精确定位非常有用。
    *   在自定义组件内部，最终的 `x` 和 `y` 通常直接应用于 SVG `<text>` 元素或其包装 `<g>` 元素的 `transform`。
*   **架构建议:**
    *   对于项目中重复出现的复杂标签定位逻辑，应封装成可复用的自定义标签组件。
    *   在 [`LOG_ANALYSIS_DEVELOPER_DOCS.md`](LOG_ANALYSIS_DEVELOPER_DOCS.md) 中记录项目中采用的关键标签定位模式和自定义组件。
---
### UI Component Patterns
[2025-06-11 13:06:56] - **自定义文件输入组件模式 (React, Tailwind CSS)**
*   **问题描述:** 需要一个视觉上可定制的文件输入字段，以替代浏览器默认的样式，并能与项目 UI (如 `shadcn/ui`) 风格保持一致。
*   **核心策略:**
    1.  **隐藏实际输入:** 创建一个标准的 `<input type="file">` 元素，并使用 `sr-only` (screen-reader only) 或类似的 CSS 类将其在视觉上隐藏。
    2.  **自定义外观 (`<label>`):** 使用一个 `<label>` 元素作为自定义文件输入框的可见部分。`htmlFor` 属性应指向隐藏的 `<input>` 的 `id`。
    3.  **触发文件选择:** 点击 `<label>` 元素会自然地触发关联的隐藏 `<input type="file">`，从而打开文件选择对话框。
    4.  **布局与样式:**
        *   `<label>` 内部可以使用 Flexbox 或 Grid 来组织其内容，例如一个“上传”按钮区域和一个显示文件名的区域。
        *   使用 Tailwind CSS 或其他样式方案来精确控制 `<label>` 及其内部元素的外观，使其符合设计要求（例如，模拟标准输入框的边框、背景色、文字样式等）。
    5.  **状态管理 (React):**
        *   使用 React state (例如 `selectedFileName: string`) 来存储和显示用户选择的文件名。
        *   实际的 `File` 对象也应存储在另一个 state 中 (例如 `selectedFile: File | null`) 以供后续处理。
    6.  **事件处理 (React):**
        *   在隐藏的 `<input type="file">` 上附加 `onChange` 事件处理器。
        *   该处理器负责：
            *   从 `event.target.files` 获取选中的文件。
            *   更新 `selectedFile` state。
            *   更新 `selectedFileName` state (例如 `file.name`)。
            *   如果需要，清除或重置其他相关状态（如文件统计信息）。
*   **适用场景:**
    *   当需要一个与应用整体设计风格统一的文件上传控件时。
    *   当需要在文件输入旁边直接显示文件名或其他自定义UI元素时。
*   **示例 (伪代码结构):**
    ```typescript jsx
    // In a React component like LogFileUpload.tsx
    const [selectedFileName, setSelectedFileName] = useState("未选择文件");
    const [selectedFile, setSelectedFile] = useState<File | null>(null);

    const handleActualFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      if (event.target.files && event.target.files[0]) {
        const file = event.target.files[0];
        setSelectedFile(file);
        setSelectedFileName(file.name);
        // ... other logic
      } else {
        setSelectedFile(null);
        setSelectedFileName("未选择文件");
        // ... other logic
      }
    };

    return (
      <>
        <label
          htmlFor="hidden-file-input"
          className="w-full h-10 rounded-md border border-input flex items-center cursor-pointer ..."
        >
          <span className="bg-primary text-primary-foreground ...">上传文件</span>
          <span className="flex-grow px-3 ...">{selectedFileName}</span>
        </label>
        <input
          id="hidden-file-input"
          type="file"
          className="sr-only"
          onChange={handleActualFileChange}
          // ... other props like 'disabled', 'accept'
        />
      </>
    );
    ```
*   **优点:**
    *   完全的样式控制。
    *   更好的用户体验和视觉集成。
    *   符合可访问性最佳实践（当正确使用 `label` 和 `id` 时）。
*   **注意事项:**
    *   确保 `<label>` 的 `htmlFor` 属性正确链接到 `<input>` 的 `id`。
    *   确保隐藏的 `<input>` 仍然是可聚焦和可操作的（对于键盘用户和辅助技术），`sr-only` 类通常能处理好这一点。
*   **项目应用:**
    *   [`hotel-dashboard/components/log-analysis/LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx) 用于日志文件上传。