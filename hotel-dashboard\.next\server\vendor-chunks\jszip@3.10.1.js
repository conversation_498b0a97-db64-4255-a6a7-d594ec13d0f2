"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jszip@3.10.1";
exports.ids = ["vendor-chunks/jszip@3.10.1"];
exports.modules = {

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/base64.js":
/*!***************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/base64.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\nvar support = __webpack_require__(/*! ./support */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/support.js\");\n// private property\nvar _keyStr = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\";\n\n\n// public method for encoding\nexports.encode = function(input) {\n    var output = [];\n    var chr1, chr2, chr3, enc1, enc2, enc3, enc4;\n    var i = 0, len = input.length, remainingBytes = len;\n\n    var isArray = utils.getTypeOf(input) !== \"string\";\n    while (i < input.length) {\n        remainingBytes = len - i;\n\n        if (!isArray) {\n            chr1 = input.charCodeAt(i++);\n            chr2 = i < len ? input.charCodeAt(i++) : 0;\n            chr3 = i < len ? input.charCodeAt(i++) : 0;\n        } else {\n            chr1 = input[i++];\n            chr2 = i < len ? input[i++] : 0;\n            chr3 = i < len ? input[i++] : 0;\n        }\n\n        enc1 = chr1 >> 2;\n        enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);\n        enc3 = remainingBytes > 1 ? (((chr2 & 15) << 2) | (chr3 >> 6)) : 64;\n        enc4 = remainingBytes > 2 ? (chr3 & 63) : 64;\n\n        output.push(_keyStr.charAt(enc1) + _keyStr.charAt(enc2) + _keyStr.charAt(enc3) + _keyStr.charAt(enc4));\n\n    }\n\n    return output.join(\"\");\n};\n\n// public method for decoding\nexports.decode = function(input) {\n    var chr1, chr2, chr3;\n    var enc1, enc2, enc3, enc4;\n    var i = 0, resultIndex = 0;\n\n    var dataUrlPrefix = \"data:\";\n\n    if (input.substr(0, dataUrlPrefix.length) === dataUrlPrefix) {\n        // This is a common error: people give a data url\n        // (data:image/png;base64,iVBOR...) with a {base64: true} and\n        // wonders why things don't work.\n        // We can detect that the string input looks like a data url but we\n        // *can't* be sure it is one: removing everything up to the comma would\n        // be too dangerous.\n        throw new Error(\"Invalid base64 input, it looks like a data url.\");\n    }\n\n    input = input.replace(/[^A-Za-z0-9+/=]/g, \"\");\n\n    var totalLength = input.length * 3 / 4;\n    if(input.charAt(input.length - 1) === _keyStr.charAt(64)) {\n        totalLength--;\n    }\n    if(input.charAt(input.length - 2) === _keyStr.charAt(64)) {\n        totalLength--;\n    }\n    if (totalLength % 1 !== 0) {\n        // totalLength is not an integer, the length does not match a valid\n        // base64 content. That can happen if:\n        // - the input is not a base64 content\n        // - the input is *almost* a base64 content, with a extra chars at the\n        //   beginning or at the end\n        // - the input uses a base64 variant (base64url for example)\n        throw new Error(\"Invalid base64 input, bad content length.\");\n    }\n    var output;\n    if (support.uint8array) {\n        output = new Uint8Array(totalLength|0);\n    } else {\n        output = new Array(totalLength|0);\n    }\n\n    while (i < input.length) {\n\n        enc1 = _keyStr.indexOf(input.charAt(i++));\n        enc2 = _keyStr.indexOf(input.charAt(i++));\n        enc3 = _keyStr.indexOf(input.charAt(i++));\n        enc4 = _keyStr.indexOf(input.charAt(i++));\n\n        chr1 = (enc1 << 2) | (enc2 >> 4);\n        chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);\n        chr3 = ((enc3 & 3) << 6) | enc4;\n\n        output[resultIndex++] = chr1;\n\n        if (enc3 !== 64) {\n            output[resultIndex++] = chr2;\n        }\n        if (enc4 !== 64) {\n            output[resultIndex++] = chr3;\n        }\n\n    }\n\n    return output;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/base64.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/compressedObject.js":
/*!*************************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/compressedObject.js ***!
  \*************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar external = __webpack_require__(/*! ./external */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/external.js\");\nvar DataWorker = __webpack_require__(/*! ./stream/DataWorker */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/DataWorker.js\");\nvar Crc32Probe = __webpack_require__(/*! ./stream/Crc32Probe */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/Crc32Probe.js\");\nvar DataLengthProbe = __webpack_require__(/*! ./stream/DataLengthProbe */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/DataLengthProbe.js\");\n\n/**\n * Represent a compressed object, with everything needed to decompress it.\n * @constructor\n * @param {number} compressedSize the size of the data compressed.\n * @param {number} uncompressedSize the size of the data after decompression.\n * @param {number} crc32 the crc32 of the decompressed file.\n * @param {object} compression the type of compression, see lib/compressions.js.\n * @param {String|ArrayBuffer|Uint8Array|Buffer} data the compressed data.\n */\nfunction CompressedObject(compressedSize, uncompressedSize, crc32, compression, data) {\n    this.compressedSize = compressedSize;\n    this.uncompressedSize = uncompressedSize;\n    this.crc32 = crc32;\n    this.compression = compression;\n    this.compressedContent = data;\n}\n\nCompressedObject.prototype = {\n    /**\n     * Create a worker to get the uncompressed content.\n     * @return {GenericWorker} the worker.\n     */\n    getContentWorker: function () {\n        var worker = new DataWorker(external.Promise.resolve(this.compressedContent))\n            .pipe(this.compression.uncompressWorker())\n            .pipe(new DataLengthProbe(\"data_length\"));\n\n        var that = this;\n        worker.on(\"end\", function () {\n            if (this.streamInfo[\"data_length\"] !== that.uncompressedSize) {\n                throw new Error(\"Bug : uncompressed data size mismatch\");\n            }\n        });\n        return worker;\n    },\n    /**\n     * Create a worker to get the compressed content.\n     * @return {GenericWorker} the worker.\n     */\n    getCompressedWorker: function () {\n        return new DataWorker(external.Promise.resolve(this.compressedContent))\n            .withStreamInfo(\"compressedSize\", this.compressedSize)\n            .withStreamInfo(\"uncompressedSize\", this.uncompressedSize)\n            .withStreamInfo(\"crc32\", this.crc32)\n            .withStreamInfo(\"compression\", this.compression)\n        ;\n    }\n};\n\n/**\n * Chain the given worker with other workers to compress the content with the\n * given compression.\n * @param {GenericWorker} uncompressedWorker the worker to pipe.\n * @param {Object} compression the compression object.\n * @param {Object} compressionOptions the options to use when compressing.\n * @return {GenericWorker} the new worker compressing the content.\n */\nCompressedObject.createWorkerFrom = function (uncompressedWorker, compression, compressionOptions) {\n    return uncompressedWorker\n        .pipe(new Crc32Probe())\n        .pipe(new DataLengthProbe(\"uncompressedSize\"))\n        .pipe(compression.compressWorker(compressionOptions))\n        .pipe(new DataLengthProbe(\"compressedSize\"))\n        .withStreamInfo(\"compression\", compression);\n};\n\nmodule.exports = CompressedObject;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pzemlwQDMuMTAuMS9ub2RlX21vZHVsZXMvanN6aXAvbGliL2NvbXByZXNzZWRPYmplY3QuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsZUFBZSxtQkFBTyxDQUFDLCtGQUFZO0FBQ25DLGlCQUFpQixtQkFBTyxDQUFDLGlIQUFxQjtBQUM5QyxpQkFBaUIsbUJBQU8sQ0FBQyxpSEFBcUI7QUFDOUMsc0JBQXNCLG1CQUFPLENBQUMsMkhBQTBCOztBQUV4RDtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxRQUFRO0FBQ25CLFdBQVcsUUFBUTtBQUNuQixXQUFXLFFBQVE7QUFDbkIsV0FBVyxzQ0FBc0M7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsZUFBZTtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLGdCQUFnQixlQUFlO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsZUFBZTtBQUMxQixXQUFXLFFBQVE7QUFDbkIsV0FBVyxRQUFRO0FBQ25CLFlBQVksZUFBZTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkQ6XFxweWNvZGVcXHN1cHBvcnRfY2hhcnQyXFxub2RlX21vZHVsZXNcXC5wbnBtXFxqc3ppcEAzLjEwLjFcXG5vZGVfbW9kdWxlc1xcanN6aXBcXGxpYlxcY29tcHJlc3NlZE9iamVjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxudmFyIGV4dGVybmFsID0gcmVxdWlyZShcIi4vZXh0ZXJuYWxcIik7XG52YXIgRGF0YVdvcmtlciA9IHJlcXVpcmUoXCIuL3N0cmVhbS9EYXRhV29ya2VyXCIpO1xudmFyIENyYzMyUHJvYmUgPSByZXF1aXJlKFwiLi9zdHJlYW0vQ3JjMzJQcm9iZVwiKTtcbnZhciBEYXRhTGVuZ3RoUHJvYmUgPSByZXF1aXJlKFwiLi9zdHJlYW0vRGF0YUxlbmd0aFByb2JlXCIpO1xuXG4vKipcbiAqIFJlcHJlc2VudCBhIGNvbXByZXNzZWQgb2JqZWN0LCB3aXRoIGV2ZXJ5dGhpbmcgbmVlZGVkIHRvIGRlY29tcHJlc3MgaXQuXG4gKiBAY29uc3RydWN0b3JcbiAqIEBwYXJhbSB7bnVtYmVyfSBjb21wcmVzc2VkU2l6ZSB0aGUgc2l6ZSBvZiB0aGUgZGF0YSBjb21wcmVzc2VkLlxuICogQHBhcmFtIHtudW1iZXJ9IHVuY29tcHJlc3NlZFNpemUgdGhlIHNpemUgb2YgdGhlIGRhdGEgYWZ0ZXIgZGVjb21wcmVzc2lvbi5cbiAqIEBwYXJhbSB7bnVtYmVyfSBjcmMzMiB0aGUgY3JjMzIgb2YgdGhlIGRlY29tcHJlc3NlZCBmaWxlLlxuICogQHBhcmFtIHtvYmplY3R9IGNvbXByZXNzaW9uIHRoZSB0eXBlIG9mIGNvbXByZXNzaW9uLCBzZWUgbGliL2NvbXByZXNzaW9ucy5qcy5cbiAqIEBwYXJhbSB7U3RyaW5nfEFycmF5QnVmZmVyfFVpbnQ4QXJyYXl8QnVmZmVyfSBkYXRhIHRoZSBjb21wcmVzc2VkIGRhdGEuXG4gKi9cbmZ1bmN0aW9uIENvbXByZXNzZWRPYmplY3QoY29tcHJlc3NlZFNpemUsIHVuY29tcHJlc3NlZFNpemUsIGNyYzMyLCBjb21wcmVzc2lvbiwgZGF0YSkge1xuICAgIHRoaXMuY29tcHJlc3NlZFNpemUgPSBjb21wcmVzc2VkU2l6ZTtcbiAgICB0aGlzLnVuY29tcHJlc3NlZFNpemUgPSB1bmNvbXByZXNzZWRTaXplO1xuICAgIHRoaXMuY3JjMzIgPSBjcmMzMjtcbiAgICB0aGlzLmNvbXByZXNzaW9uID0gY29tcHJlc3Npb247XG4gICAgdGhpcy5jb21wcmVzc2VkQ29udGVudCA9IGRhdGE7XG59XG5cbkNvbXByZXNzZWRPYmplY3QucHJvdG90eXBlID0ge1xuICAgIC8qKlxuICAgICAqIENyZWF0ZSBhIHdvcmtlciB0byBnZXQgdGhlIHVuY29tcHJlc3NlZCBjb250ZW50LlxuICAgICAqIEByZXR1cm4ge0dlbmVyaWNXb3JrZXJ9IHRoZSB3b3JrZXIuXG4gICAgICovXG4gICAgZ2V0Q29udGVudFdvcmtlcjogZnVuY3Rpb24gKCkge1xuICAgICAgICB2YXIgd29ya2VyID0gbmV3IERhdGFXb3JrZXIoZXh0ZXJuYWwuUHJvbWlzZS5yZXNvbHZlKHRoaXMuY29tcHJlc3NlZENvbnRlbnQpKVxuICAgICAgICAgICAgLnBpcGUodGhpcy5jb21wcmVzc2lvbi51bmNvbXByZXNzV29ya2VyKCkpXG4gICAgICAgICAgICAucGlwZShuZXcgRGF0YUxlbmd0aFByb2JlKFwiZGF0YV9sZW5ndGhcIikpO1xuXG4gICAgICAgIHZhciB0aGF0ID0gdGhpcztcbiAgICAgICAgd29ya2VyLm9uKFwiZW5kXCIsIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgIGlmICh0aGlzLnN0cmVhbUluZm9bXCJkYXRhX2xlbmd0aFwiXSAhPT0gdGhhdC51bmNvbXByZXNzZWRTaXplKSB7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiQnVnIDogdW5jb21wcmVzc2VkIGRhdGEgc2l6ZSBtaXNtYXRjaFwiKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICAgIHJldHVybiB3b3JrZXI7XG4gICAgfSxcbiAgICAvKipcbiAgICAgKiBDcmVhdGUgYSB3b3JrZXIgdG8gZ2V0IHRoZSBjb21wcmVzc2VkIGNvbnRlbnQuXG4gICAgICogQHJldHVybiB7R2VuZXJpY1dvcmtlcn0gdGhlIHdvcmtlci5cbiAgICAgKi9cbiAgICBnZXRDb21wcmVzc2VkV29ya2VyOiBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiBuZXcgRGF0YVdvcmtlcihleHRlcm5hbC5Qcm9taXNlLnJlc29sdmUodGhpcy5jb21wcmVzc2VkQ29udGVudCkpXG4gICAgICAgICAgICAud2l0aFN0cmVhbUluZm8oXCJjb21wcmVzc2VkU2l6ZVwiLCB0aGlzLmNvbXByZXNzZWRTaXplKVxuICAgICAgICAgICAgLndpdGhTdHJlYW1JbmZvKFwidW5jb21wcmVzc2VkU2l6ZVwiLCB0aGlzLnVuY29tcHJlc3NlZFNpemUpXG4gICAgICAgICAgICAud2l0aFN0cmVhbUluZm8oXCJjcmMzMlwiLCB0aGlzLmNyYzMyKVxuICAgICAgICAgICAgLndpdGhTdHJlYW1JbmZvKFwiY29tcHJlc3Npb25cIiwgdGhpcy5jb21wcmVzc2lvbilcbiAgICAgICAgO1xuICAgIH1cbn07XG5cbi8qKlxuICogQ2hhaW4gdGhlIGdpdmVuIHdvcmtlciB3aXRoIG90aGVyIHdvcmtlcnMgdG8gY29tcHJlc3MgdGhlIGNvbnRlbnQgd2l0aCB0aGVcbiAqIGdpdmVuIGNvbXByZXNzaW9uLlxuICogQHBhcmFtIHtHZW5lcmljV29ya2VyfSB1bmNvbXByZXNzZWRXb3JrZXIgdGhlIHdvcmtlciB0byBwaXBlLlxuICogQHBhcmFtIHtPYmplY3R9IGNvbXByZXNzaW9uIHRoZSBjb21wcmVzc2lvbiBvYmplY3QuXG4gKiBAcGFyYW0ge09iamVjdH0gY29tcHJlc3Npb25PcHRpb25zIHRoZSBvcHRpb25zIHRvIHVzZSB3aGVuIGNvbXByZXNzaW5nLlxuICogQHJldHVybiB7R2VuZXJpY1dvcmtlcn0gdGhlIG5ldyB3b3JrZXIgY29tcHJlc3NpbmcgdGhlIGNvbnRlbnQuXG4gKi9cbkNvbXByZXNzZWRPYmplY3QuY3JlYXRlV29ya2VyRnJvbSA9IGZ1bmN0aW9uICh1bmNvbXByZXNzZWRXb3JrZXIsIGNvbXByZXNzaW9uLCBjb21wcmVzc2lvbk9wdGlvbnMpIHtcbiAgICByZXR1cm4gdW5jb21wcmVzc2VkV29ya2VyXG4gICAgICAgIC5waXBlKG5ldyBDcmMzMlByb2JlKCkpXG4gICAgICAgIC5waXBlKG5ldyBEYXRhTGVuZ3RoUHJvYmUoXCJ1bmNvbXByZXNzZWRTaXplXCIpKVxuICAgICAgICAucGlwZShjb21wcmVzc2lvbi5jb21wcmVzc1dvcmtlcihjb21wcmVzc2lvbk9wdGlvbnMpKVxuICAgICAgICAucGlwZShuZXcgRGF0YUxlbmd0aFByb2JlKFwiY29tcHJlc3NlZFNpemVcIikpXG4gICAgICAgIC53aXRoU3RyZWFtSW5mbyhcImNvbXByZXNzaW9uXCIsIGNvbXByZXNzaW9uKTtcbn07XG5cbm1vZHVsZS5leHBvcnRzID0gQ29tcHJlc3NlZE9iamVjdDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/compressedObject.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/compressions.js":
/*!*********************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/compressions.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar GenericWorker = __webpack_require__(/*! ./stream/GenericWorker */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js\");\n\nexports.STORE = {\n    magic: \"\\x00\\x00\",\n    compressWorker : function () {\n        return new GenericWorker(\"STORE compression\");\n    },\n    uncompressWorker : function () {\n        return new GenericWorker(\"STORE decompression\");\n    }\n};\nexports.DEFLATE = __webpack_require__(/*! ./flate */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/flate.js\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pzemlwQDMuMTAuMS9ub2RlX21vZHVsZXMvanN6aXAvbGliL2NvbXByZXNzaW9ucy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixvQkFBb0IsbUJBQU8sQ0FBQyx1SEFBd0I7O0FBRXBELGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnSUFBb0MiLCJzb3VyY2VzIjpbIkQ6XFxweWNvZGVcXHN1cHBvcnRfY2hhcnQyXFxub2RlX21vZHVsZXNcXC5wbnBtXFxqc3ppcEAzLjEwLjFcXG5vZGVfbW9kdWxlc1xcanN6aXBcXGxpYlxcY29tcHJlc3Npb25zLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG52YXIgR2VuZXJpY1dvcmtlciA9IHJlcXVpcmUoXCIuL3N0cmVhbS9HZW5lcmljV29ya2VyXCIpO1xuXG5leHBvcnRzLlNUT1JFID0ge1xuICAgIG1hZ2ljOiBcIlxceDAwXFx4MDBcIixcbiAgICBjb21wcmVzc1dvcmtlciA6IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIG5ldyBHZW5lcmljV29ya2VyKFwiU1RPUkUgY29tcHJlc3Npb25cIik7XG4gICAgfSxcbiAgICB1bmNvbXByZXNzV29ya2VyIDogZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gbmV3IEdlbmVyaWNXb3JrZXIoXCJTVE9SRSBkZWNvbXByZXNzaW9uXCIpO1xuICAgIH1cbn07XG5leHBvcnRzLkRFRkxBVEUgPSByZXF1aXJlKFwiLi9mbGF0ZVwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/compressions.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/crc32.js":
/*!**************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/crc32.js ***!
  \**************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\n\n/**\n * The following functions come from pako, from pako/lib/zlib/crc32.js\n * released under the MIT license, see pako https://github.com/nodeca/pako/\n */\n\n// Use ordinary array, since untyped makes no boost here\nfunction makeTable() {\n    var c, table = [];\n\n    for(var n =0; n < 256; n++){\n        c = n;\n        for(var k =0; k < 8; k++){\n            c = ((c&1) ? (0xEDB88320 ^ (c >>> 1)) : (c >>> 1));\n        }\n        table[n] = c;\n    }\n\n    return table;\n}\n\n// Create table on load. Just 255 signed longs. Not a problem.\nvar crcTable = makeTable();\n\n\nfunction crc32(crc, buf, len, pos) {\n    var t = crcTable, end = pos + len;\n\n    crc = crc ^ (-1);\n\n    for (var i = pos; i < end; i++ ) {\n        crc = (crc >>> 8) ^ t[(crc ^ buf[i]) & 0xFF];\n    }\n\n    return (crc ^ (-1)); // >>> 0;\n}\n\n// That's all for the pako functions.\n\n/**\n * Compute the crc32 of a string.\n * This is almost the same as the function crc32, but for strings. Using the\n * same function for the two use cases leads to horrible performances.\n * @param {Number} crc the starting value of the crc.\n * @param {String} str the string to use.\n * @param {Number} len the length of the string.\n * @param {Number} pos the starting position for the crc32 computation.\n * @return {Number} the computed crc32.\n */\nfunction crc32str(crc, str, len, pos) {\n    var t = crcTable, end = pos + len;\n\n    crc = crc ^ (-1);\n\n    for (var i = pos; i < end; i++ ) {\n        crc = (crc >>> 8) ^ t[(crc ^ str.charCodeAt(i)) & 0xFF];\n    }\n\n    return (crc ^ (-1)); // >>> 0;\n}\n\nmodule.exports = function crc32wrapper(input, crc) {\n    if (typeof input === \"undefined\" || !input.length) {\n        return 0;\n    }\n\n    var isArray = utils.getTypeOf(input) !== \"string\";\n\n    if(isArray) {\n        return crc32(crc|0, input, input.length, 0);\n    } else {\n        return crc32str(crc|0, input, input.length, 0);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/crc32.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/defaults.js":
/*!*****************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/defaults.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nexports.base64 = false;\nexports.binary = false;\nexports.dir = false;\nexports.createFolders = true;\nexports.date = null;\nexports.compression = null;\nexports.compressionOptions = null;\nexports.comment = null;\nexports.unixPermissions = null;\nexports.dosPermissions = null;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pzemlwQDMuMTAuMS9ub2RlX21vZHVsZXMvanN6aXAvbGliL2RlZmF1bHRzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsY0FBYztBQUNkLGNBQWM7QUFDZCxXQUFXO0FBQ1gscUJBQXFCO0FBQ3JCLFlBQVk7QUFDWixtQkFBbUI7QUFDbkIsMEJBQTBCO0FBQzFCLGVBQWU7QUFDZix1QkFBdUI7QUFDdkIsc0JBQXNCIiwic291cmNlcyI6WyJEOlxccHljb2RlXFxzdXBwb3J0X2NoYXJ0Mlxcbm9kZV9tb2R1bGVzXFwucG5wbVxcanN6aXBAMy4xMC4xXFxub2RlX21vZHVsZXNcXGpzemlwXFxsaWJcXGRlZmF1bHRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuZXhwb3J0cy5iYXNlNjQgPSBmYWxzZTtcbmV4cG9ydHMuYmluYXJ5ID0gZmFsc2U7XG5leHBvcnRzLmRpciA9IGZhbHNlO1xuZXhwb3J0cy5jcmVhdGVGb2xkZXJzID0gdHJ1ZTtcbmV4cG9ydHMuZGF0ZSA9IG51bGw7XG5leHBvcnRzLmNvbXByZXNzaW9uID0gbnVsbDtcbmV4cG9ydHMuY29tcHJlc3Npb25PcHRpb25zID0gbnVsbDtcbmV4cG9ydHMuY29tbWVudCA9IG51bGw7XG5leHBvcnRzLnVuaXhQZXJtaXNzaW9ucyA9IG51bGw7XG5leHBvcnRzLmRvc1Blcm1pc3Npb25zID0gbnVsbDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/defaults.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/external.js":
/*!*****************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/external.js ***!
  \*****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n// load the global object first:\n// - it should be better integrated in the system (unhandledRejection in node)\n// - the environment may have a custom Promise implementation (see zone.js)\nvar ES6Promise = null;\nif (typeof Promise !== \"undefined\") {\n    ES6Promise = Promise;\n} else {\n    ES6Promise = __webpack_require__(/*! lie */ \"(ssr)/../node_modules/.pnpm/lie@3.3.0/node_modules/lie/lib/index.js\");\n}\n\n/**\n * Let the user use/change some implementations.\n */\nmodule.exports = {\n    Promise: ES6Promise\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pzemlwQDMuMTAuMS9ub2RlX21vZHVsZXMvanN6aXAvbGliL2V4dGVybmFsLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEVBQUU7QUFDRixpQkFBaUIsbUJBQU8sQ0FBQyxnRkFBSztBQUM5Qjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxweWNvZGVcXHN1cHBvcnRfY2hhcnQyXFxub2RlX21vZHVsZXNcXC5wbnBtXFxqc3ppcEAzLjEwLjFcXG5vZGVfbW9kdWxlc1xcanN6aXBcXGxpYlxcZXh0ZXJuYWwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbi8vIGxvYWQgdGhlIGdsb2JhbCBvYmplY3QgZmlyc3Q6XG4vLyAtIGl0IHNob3VsZCBiZSBiZXR0ZXIgaW50ZWdyYXRlZCBpbiB0aGUgc3lzdGVtICh1bmhhbmRsZWRSZWplY3Rpb24gaW4gbm9kZSlcbi8vIC0gdGhlIGVudmlyb25tZW50IG1heSBoYXZlIGEgY3VzdG9tIFByb21pc2UgaW1wbGVtZW50YXRpb24gKHNlZSB6b25lLmpzKVxudmFyIEVTNlByb21pc2UgPSBudWxsO1xuaWYgKHR5cGVvZiBQcm9taXNlICE9PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgRVM2UHJvbWlzZSA9IFByb21pc2U7XG59IGVsc2Uge1xuICAgIEVTNlByb21pc2UgPSByZXF1aXJlKFwibGllXCIpO1xufVxuXG4vKipcbiAqIExldCB0aGUgdXNlciB1c2UvY2hhbmdlIHNvbWUgaW1wbGVtZW50YXRpb25zLlxuICovXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgICBQcm9taXNlOiBFUzZQcm9taXNlXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/external.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/flate.js":
/*!**************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/flate.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar USE_TYPEDARRAY = (typeof Uint8Array !== \"undefined\") && (typeof Uint16Array !== \"undefined\") && (typeof Uint32Array !== \"undefined\");\n\nvar pako = __webpack_require__(/*! pako */ \"(ssr)/../node_modules/.pnpm/pako@1.0.11/node_modules/pako/index.js\");\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ./stream/GenericWorker */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js\");\n\nvar ARRAY_TYPE = USE_TYPEDARRAY ? \"uint8array\" : \"array\";\n\nexports.magic = \"\\x08\\x00\";\n\n/**\n * Create a worker that uses pako to inflate/deflate.\n * @constructor\n * @param {String} action the name of the pako function to call : either \"Deflate\" or \"Inflate\".\n * @param {Object} options the options to use when (de)compressing.\n */\nfunction FlateWorker(action, options) {\n    GenericWorker.call(this, \"FlateWorker/\" + action);\n\n    this._pako = null;\n    this._pakoAction = action;\n    this._pakoOptions = options;\n    // the `meta` object from the last chunk received\n    // this allow this worker to pass around metadata\n    this.meta = {};\n}\n\nutils.inherits(FlateWorker, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nFlateWorker.prototype.processChunk = function (chunk) {\n    this.meta = chunk.meta;\n    if (this._pako === null) {\n        this._createPako();\n    }\n    this._pako.push(utils.transformTo(ARRAY_TYPE, chunk.data), false);\n};\n\n/**\n * @see GenericWorker.flush\n */\nFlateWorker.prototype.flush = function () {\n    GenericWorker.prototype.flush.call(this);\n    if (this._pako === null) {\n        this._createPako();\n    }\n    this._pako.push([], true);\n};\n/**\n * @see GenericWorker.cleanUp\n */\nFlateWorker.prototype.cleanUp = function () {\n    GenericWorker.prototype.cleanUp.call(this);\n    this._pako = null;\n};\n\n/**\n * Create the _pako object.\n * TODO: lazy-loading this object isn't the best solution but it's the\n * quickest. The best solution is to lazy-load the worker list. See also the\n * issue #446.\n */\nFlateWorker.prototype._createPako = function () {\n    this._pako = new pako[this._pakoAction]({\n        raw: true,\n        level: this._pakoOptions.level || -1 // default compression\n    });\n    var self = this;\n    this._pako.onData = function(data) {\n        self.push({\n            data : data,\n            meta : self.meta\n        });\n    };\n};\n\nexports.compressWorker = function (compressionOptions) {\n    return new FlateWorker(\"Deflate\", compressionOptions);\n};\nexports.uncompressWorker = function () {\n    return new FlateWorker(\"Inflate\", {});\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/flate.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/generate/ZipFileWorker.js":
/*!*******************************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/generate/ZipFileWorker.js ***!
  \*******************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ../stream/GenericWorker */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js\");\nvar utf8 = __webpack_require__(/*! ../utf8 */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utf8.js\");\nvar crc32 = __webpack_require__(/*! ../crc32 */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/crc32.js\");\nvar signature = __webpack_require__(/*! ../signature */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/signature.js\");\n\n/**\n * Transform an integer into a string in hexadecimal.\n * @private\n * @param {number} dec the number to convert.\n * @param {number} bytes the number of bytes to generate.\n * @returns {string} the result.\n */\nvar decToHex = function(dec, bytes) {\n    var hex = \"\", i;\n    for (i = 0; i < bytes; i++) {\n        hex += String.fromCharCode(dec & 0xff);\n        dec = dec >>> 8;\n    }\n    return hex;\n};\n\n/**\n * Generate the UNIX part of the external file attributes.\n * @param {Object} unixPermissions the unix permissions or null.\n * @param {Boolean} isDir true if the entry is a directory, false otherwise.\n * @return {Number} a 32 bit integer.\n *\n * adapted from http://unix.stackexchange.com/questions/14705/the-zip-formats-external-file-attribute :\n *\n * TTTTsstrwxrwxrwx0000000000ADVSHR\n * ^^^^____________________________ file type, see zipinfo.c (UNX_*)\n *     ^^^_________________________ setuid, setgid, sticky\n *        ^^^^^^^^^________________ permissions\n *                 ^^^^^^^^^^______ not used ?\n *                           ^^^^^^ DOS attribute bits : Archive, Directory, Volume label, System file, Hidden, Read only\n */\nvar generateUnixExternalFileAttr = function (unixPermissions, isDir) {\n\n    var result = unixPermissions;\n    if (!unixPermissions) {\n        // I can't use octal values in strict mode, hence the hexa.\n        //  040775 => 0x41fd\n        // 0100664 => 0x81b4\n        result = isDir ? 0x41fd : 0x81b4;\n    }\n    return (result & 0xFFFF) << 16;\n};\n\n/**\n * Generate the DOS part of the external file attributes.\n * @param {Object} dosPermissions the dos permissions or null.\n * @param {Boolean} isDir true if the entry is a directory, false otherwise.\n * @return {Number} a 32 bit integer.\n *\n * Bit 0     Read-Only\n * Bit 1     Hidden\n * Bit 2     System\n * Bit 3     Volume Label\n * Bit 4     Directory\n * Bit 5     Archive\n */\nvar generateDosExternalFileAttr = function (dosPermissions) {\n    // the dir flag is already set for compatibility\n    return (dosPermissions || 0)  & 0x3F;\n};\n\n/**\n * Generate the various parts used in the construction of the final zip file.\n * @param {Object} streamInfo the hash with information about the compressed file.\n * @param {Boolean} streamedContent is the content streamed ?\n * @param {Boolean} streamingEnded is the stream finished ?\n * @param {number} offset the current offset from the start of the zip file.\n * @param {String} platform let's pretend we are this platform (change platform dependents fields)\n * @param {Function} encodeFileName the function to encode the file name / comment.\n * @return {Object} the zip parts.\n */\nvar generateZipParts = function(streamInfo, streamedContent, streamingEnded, offset, platform, encodeFileName) {\n    var file = streamInfo[\"file\"],\n        compression = streamInfo[\"compression\"],\n        useCustomEncoding = encodeFileName !== utf8.utf8encode,\n        encodedFileName = utils.transformTo(\"string\", encodeFileName(file.name)),\n        utfEncodedFileName = utils.transformTo(\"string\", utf8.utf8encode(file.name)),\n        comment = file.comment,\n        encodedComment = utils.transformTo(\"string\", encodeFileName(comment)),\n        utfEncodedComment = utils.transformTo(\"string\", utf8.utf8encode(comment)),\n        useUTF8ForFileName = utfEncodedFileName.length !== file.name.length,\n        useUTF8ForComment = utfEncodedComment.length !== comment.length,\n        dosTime,\n        dosDate,\n        extraFields = \"\",\n        unicodePathExtraField = \"\",\n        unicodeCommentExtraField = \"\",\n        dir = file.dir,\n        date = file.date;\n\n\n    var dataInfo = {\n        crc32 : 0,\n        compressedSize : 0,\n        uncompressedSize : 0\n    };\n\n    // if the content is streamed, the sizes/crc32 are only available AFTER\n    // the end of the stream.\n    if (!streamedContent || streamingEnded) {\n        dataInfo.crc32 = streamInfo[\"crc32\"];\n        dataInfo.compressedSize = streamInfo[\"compressedSize\"];\n        dataInfo.uncompressedSize = streamInfo[\"uncompressedSize\"];\n    }\n\n    var bitflag = 0;\n    if (streamedContent) {\n        // Bit 3: the sizes/crc32 are set to zero in the local header.\n        // The correct values are put in the data descriptor immediately\n        // following the compressed data.\n        bitflag |= 0x0008;\n    }\n    if (!useCustomEncoding && (useUTF8ForFileName || useUTF8ForComment)) {\n        // Bit 11: Language encoding flag (EFS).\n        bitflag |= 0x0800;\n    }\n\n\n    var extFileAttr = 0;\n    var versionMadeBy = 0;\n    if (dir) {\n        // dos or unix, we set the dos dir flag\n        extFileAttr |= 0x00010;\n    }\n    if(platform === \"UNIX\") {\n        versionMadeBy = 0x031E; // UNIX, version 3.0\n        extFileAttr |= generateUnixExternalFileAttr(file.unixPermissions, dir);\n    } else { // DOS or other, fallback to DOS\n        versionMadeBy = 0x0014; // DOS, version 2.0\n        extFileAttr |= generateDosExternalFileAttr(file.dosPermissions, dir);\n    }\n\n    // date\n    // @see http://www.delorie.com/djgpp/doc/rbinter/it/52/13.html\n    // @see http://www.delorie.com/djgpp/doc/rbinter/it/65/16.html\n    // @see http://www.delorie.com/djgpp/doc/rbinter/it/66/16.html\n\n    dosTime = date.getUTCHours();\n    dosTime = dosTime << 6;\n    dosTime = dosTime | date.getUTCMinutes();\n    dosTime = dosTime << 5;\n    dosTime = dosTime | date.getUTCSeconds() / 2;\n\n    dosDate = date.getUTCFullYear() - 1980;\n    dosDate = dosDate << 4;\n    dosDate = dosDate | (date.getUTCMonth() + 1);\n    dosDate = dosDate << 5;\n    dosDate = dosDate | date.getUTCDate();\n\n    if (useUTF8ForFileName) {\n        // set the unicode path extra field. unzip needs at least one extra\n        // field to correctly handle unicode path, so using the path is as good\n        // as any other information. This could improve the situation with\n        // other archive managers too.\n        // This field is usually used without the utf8 flag, with a non\n        // unicode path in the header (winrar, winzip). This helps (a bit)\n        // with the messy Windows' default compressed folders feature but\n        // breaks on p7zip which doesn't seek the unicode path extra field.\n        // So for now, UTF-8 everywhere !\n        unicodePathExtraField =\n            // Version\n            decToHex(1, 1) +\n            // NameCRC32\n            decToHex(crc32(encodedFileName), 4) +\n            // UnicodeName\n            utfEncodedFileName;\n\n        extraFields +=\n            // Info-ZIP Unicode Path Extra Field\n            \"\\x75\\x70\" +\n            // size\n            decToHex(unicodePathExtraField.length, 2) +\n            // content\n            unicodePathExtraField;\n    }\n\n    if(useUTF8ForComment) {\n\n        unicodeCommentExtraField =\n            // Version\n            decToHex(1, 1) +\n            // CommentCRC32\n            decToHex(crc32(encodedComment), 4) +\n            // UnicodeName\n            utfEncodedComment;\n\n        extraFields +=\n            // Info-ZIP Unicode Path Extra Field\n            \"\\x75\\x63\" +\n            // size\n            decToHex(unicodeCommentExtraField.length, 2) +\n            // content\n            unicodeCommentExtraField;\n    }\n\n    var header = \"\";\n\n    // version needed to extract\n    header += \"\\x0A\\x00\";\n    // general purpose bit flag\n    header += decToHex(bitflag, 2);\n    // compression method\n    header += compression.magic;\n    // last mod file time\n    header += decToHex(dosTime, 2);\n    // last mod file date\n    header += decToHex(dosDate, 2);\n    // crc-32\n    header += decToHex(dataInfo.crc32, 4);\n    // compressed size\n    header += decToHex(dataInfo.compressedSize, 4);\n    // uncompressed size\n    header += decToHex(dataInfo.uncompressedSize, 4);\n    // file name length\n    header += decToHex(encodedFileName.length, 2);\n    // extra field length\n    header += decToHex(extraFields.length, 2);\n\n\n    var fileRecord = signature.LOCAL_FILE_HEADER + header + encodedFileName + extraFields;\n\n    var dirRecord = signature.CENTRAL_FILE_HEADER +\n        // version made by (00: DOS)\n        decToHex(versionMadeBy, 2) +\n        // file header (common to file and central directory)\n        header +\n        // file comment length\n        decToHex(encodedComment.length, 2) +\n        // disk number start\n        \"\\x00\\x00\" +\n        // internal file attributes TODO\n        \"\\x00\\x00\" +\n        // external file attributes\n        decToHex(extFileAttr, 4) +\n        // relative offset of local header\n        decToHex(offset, 4) +\n        // file name\n        encodedFileName +\n        // extra field\n        extraFields +\n        // file comment\n        encodedComment;\n\n    return {\n        fileRecord: fileRecord,\n        dirRecord: dirRecord\n    };\n};\n\n/**\n * Generate the EOCD record.\n * @param {Number} entriesCount the number of entries in the zip file.\n * @param {Number} centralDirLength the length (in bytes) of the central dir.\n * @param {Number} localDirLength the length (in bytes) of the local dir.\n * @param {String} comment the zip file comment as a binary string.\n * @param {Function} encodeFileName the function to encode the comment.\n * @return {String} the EOCD record.\n */\nvar generateCentralDirectoryEnd = function (entriesCount, centralDirLength, localDirLength, comment, encodeFileName) {\n    var dirEnd = \"\";\n    var encodedComment = utils.transformTo(\"string\", encodeFileName(comment));\n\n    // end of central dir signature\n    dirEnd = signature.CENTRAL_DIRECTORY_END +\n        // number of this disk\n        \"\\x00\\x00\" +\n        // number of the disk with the start of the central directory\n        \"\\x00\\x00\" +\n        // total number of entries in the central directory on this disk\n        decToHex(entriesCount, 2) +\n        // total number of entries in the central directory\n        decToHex(entriesCount, 2) +\n        // size of the central directory   4 bytes\n        decToHex(centralDirLength, 4) +\n        // offset of start of central directory with respect to the starting disk number\n        decToHex(localDirLength, 4) +\n        // .ZIP file comment length\n        decToHex(encodedComment.length, 2) +\n        // .ZIP file comment\n        encodedComment;\n\n    return dirEnd;\n};\n\n/**\n * Generate data descriptors for a file entry.\n * @param {Object} streamInfo the hash generated by a worker, containing information\n * on the file entry.\n * @return {String} the data descriptors.\n */\nvar generateDataDescriptors = function (streamInfo) {\n    var descriptor = \"\";\n    descriptor = signature.DATA_DESCRIPTOR +\n        // crc-32                          4 bytes\n        decToHex(streamInfo[\"crc32\"], 4) +\n        // compressed size                 4 bytes\n        decToHex(streamInfo[\"compressedSize\"], 4) +\n        // uncompressed size               4 bytes\n        decToHex(streamInfo[\"uncompressedSize\"], 4);\n\n    return descriptor;\n};\n\n\n/**\n * A worker to concatenate other workers to create a zip file.\n * @param {Boolean} streamFiles `true` to stream the content of the files,\n * `false` to accumulate it.\n * @param {String} comment the comment to use.\n * @param {String} platform the platform to use, \"UNIX\" or \"DOS\".\n * @param {Function} encodeFileName the function to encode file names and comments.\n */\nfunction ZipFileWorker(streamFiles, comment, platform, encodeFileName) {\n    GenericWorker.call(this, \"ZipFileWorker\");\n    // The number of bytes written so far. This doesn't count accumulated chunks.\n    this.bytesWritten = 0;\n    // The comment of the zip file\n    this.zipComment = comment;\n    // The platform \"generating\" the zip file.\n    this.zipPlatform = platform;\n    // the function to encode file names and comments.\n    this.encodeFileName = encodeFileName;\n    // Should we stream the content of the files ?\n    this.streamFiles = streamFiles;\n    // If `streamFiles` is false, we will need to accumulate the content of the\n    // files to calculate sizes / crc32 (and write them *before* the content).\n    // This boolean indicates if we are accumulating chunks (it will change a lot\n    // during the lifetime of this worker).\n    this.accumulate = false;\n    // The buffer receiving chunks when accumulating content.\n    this.contentBuffer = [];\n    // The list of generated directory records.\n    this.dirRecords = [];\n    // The offset (in bytes) from the beginning of the zip file for the current source.\n    this.currentSourceOffset = 0;\n    // The total number of entries in this zip file.\n    this.entriesCount = 0;\n    // the name of the file currently being added, null when handling the end of the zip file.\n    // Used for the emitted metadata.\n    this.currentFile = null;\n\n\n\n    this._sources = [];\n}\nutils.inherits(ZipFileWorker, GenericWorker);\n\n/**\n * @see GenericWorker.push\n */\nZipFileWorker.prototype.push = function (chunk) {\n\n    var currentFilePercent = chunk.meta.percent || 0;\n    var entriesCount = this.entriesCount;\n    var remainingFiles = this._sources.length;\n\n    if(this.accumulate) {\n        this.contentBuffer.push(chunk);\n    } else {\n        this.bytesWritten += chunk.data.length;\n\n        GenericWorker.prototype.push.call(this, {\n            data : chunk.data,\n            meta : {\n                currentFile : this.currentFile,\n                percent : entriesCount ? (currentFilePercent + 100 * (entriesCount - remainingFiles - 1)) / entriesCount : 100\n            }\n        });\n    }\n};\n\n/**\n * The worker started a new source (an other worker).\n * @param {Object} streamInfo the streamInfo object from the new source.\n */\nZipFileWorker.prototype.openedSource = function (streamInfo) {\n    this.currentSourceOffset = this.bytesWritten;\n    this.currentFile = streamInfo[\"file\"].name;\n\n    var streamedContent = this.streamFiles && !streamInfo[\"file\"].dir;\n\n    // don't stream folders (because they don't have any content)\n    if(streamedContent) {\n        var record = generateZipParts(streamInfo, streamedContent, false, this.currentSourceOffset, this.zipPlatform, this.encodeFileName);\n        this.push({\n            data : record.fileRecord,\n            meta : {percent:0}\n        });\n    } else {\n        // we need to wait for the whole file before pushing anything\n        this.accumulate = true;\n    }\n};\n\n/**\n * The worker finished a source (an other worker).\n * @param {Object} streamInfo the streamInfo object from the finished source.\n */\nZipFileWorker.prototype.closedSource = function (streamInfo) {\n    this.accumulate = false;\n    var streamedContent = this.streamFiles && !streamInfo[\"file\"].dir;\n    var record = generateZipParts(streamInfo, streamedContent, true, this.currentSourceOffset, this.zipPlatform, this.encodeFileName);\n\n    this.dirRecords.push(record.dirRecord);\n    if(streamedContent) {\n        // after the streamed file, we put data descriptors\n        this.push({\n            data : generateDataDescriptors(streamInfo),\n            meta : {percent:100}\n        });\n    } else {\n        // the content wasn't streamed, we need to push everything now\n        // first the file record, then the content\n        this.push({\n            data : record.fileRecord,\n            meta : {percent:0}\n        });\n        while(this.contentBuffer.length) {\n            this.push(this.contentBuffer.shift());\n        }\n    }\n    this.currentFile = null;\n};\n\n/**\n * @see GenericWorker.flush\n */\nZipFileWorker.prototype.flush = function () {\n\n    var localDirLength = this.bytesWritten;\n    for(var i = 0; i < this.dirRecords.length; i++) {\n        this.push({\n            data : this.dirRecords[i],\n            meta : {percent:100}\n        });\n    }\n    var centralDirLength = this.bytesWritten - localDirLength;\n\n    var dirEnd = generateCentralDirectoryEnd(this.dirRecords.length, centralDirLength, localDirLength, this.zipComment, this.encodeFileName);\n\n    this.push({\n        data : dirEnd,\n        meta : {percent:100}\n    });\n};\n\n/**\n * Prepare the next source to be read.\n */\nZipFileWorker.prototype.prepareNextSource = function () {\n    this.previous = this._sources.shift();\n    this.openedSource(this.previous.streamInfo);\n    if (this.isPaused) {\n        this.previous.pause();\n    } else {\n        this.previous.resume();\n    }\n};\n\n/**\n * @see GenericWorker.registerPrevious\n */\nZipFileWorker.prototype.registerPrevious = function (previous) {\n    this._sources.push(previous);\n    var self = this;\n\n    previous.on(\"data\", function (chunk) {\n        self.processChunk(chunk);\n    });\n    previous.on(\"end\", function () {\n        self.closedSource(self.previous.streamInfo);\n        if(self._sources.length) {\n            self.prepareNextSource();\n        } else {\n            self.end();\n        }\n    });\n    previous.on(\"error\", function (e) {\n        self.error(e);\n    });\n    return this;\n};\n\n/**\n * @see GenericWorker.resume\n */\nZipFileWorker.prototype.resume = function () {\n    if(!GenericWorker.prototype.resume.call(this)) {\n        return false;\n    }\n\n    if (!this.previous && this._sources.length) {\n        this.prepareNextSource();\n        return true;\n    }\n    if (!this.previous && !this._sources.length && !this.generatedError) {\n        this.end();\n        return true;\n    }\n};\n\n/**\n * @see GenericWorker.error\n */\nZipFileWorker.prototype.error = function (e) {\n    var sources = this._sources;\n    if(!GenericWorker.prototype.error.call(this, e)) {\n        return false;\n    }\n    for(var i = 0; i < sources.length; i++) {\n        try {\n            sources[i].error(e);\n        } catch(e) {\n            // the `error` exploded, nothing to do\n        }\n    }\n    return true;\n};\n\n/**\n * @see GenericWorker.lock\n */\nZipFileWorker.prototype.lock = function () {\n    GenericWorker.prototype.lock.call(this);\n    var sources = this._sources;\n    for(var i = 0; i < sources.length; i++) {\n        sources[i].lock();\n    }\n};\n\nmodule.exports = ZipFileWorker;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/generate/ZipFileWorker.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/generate/index.js":
/*!***********************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/generate/index.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar compressions = __webpack_require__(/*! ../compressions */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/compressions.js\");\nvar ZipFileWorker = __webpack_require__(/*! ./ZipFileWorker */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/generate/ZipFileWorker.js\");\n\n/**\n * Find the compression to use.\n * @param {String} fileCompression the compression defined at the file level, if any.\n * @param {String} zipCompression the compression defined at the load() level.\n * @return {Object} the compression object to use.\n */\nvar getCompression = function (fileCompression, zipCompression) {\n\n    var compressionName = fileCompression || zipCompression;\n    var compression = compressions[compressionName];\n    if (!compression) {\n        throw new Error(compressionName + \" is not a valid compression method !\");\n    }\n    return compression;\n};\n\n/**\n * Create a worker to generate a zip file.\n * @param {JSZip} zip the JSZip instance at the right root level.\n * @param {Object} options to generate the zip file.\n * @param {String} comment the comment to use.\n */\nexports.generateWorker = function (zip, options, comment) {\n\n    var zipFileWorker = new ZipFileWorker(options.streamFiles, comment, options.platform, options.encodeFileName);\n    var entriesCount = 0;\n    try {\n\n        zip.forEach(function (relativePath, file) {\n            entriesCount++;\n            var compression = getCompression(file.options.compression, options.compression);\n            var compressionOptions = file.options.compressionOptions || options.compressionOptions || {};\n            var dir = file.dir, date = file.date;\n\n            file._compressWorker(compression, compressionOptions)\n                .withStreamInfo(\"file\", {\n                    name : relativePath,\n                    dir : dir,\n                    date : date,\n                    comment : file.comment || \"\",\n                    unixPermissions : file.unixPermissions,\n                    dosPermissions : file.dosPermissions\n                })\n                .pipe(zipFileWorker);\n        });\n        zipFileWorker.entriesCount = entriesCount;\n    } catch (e) {\n        zipFileWorker.error(e);\n    }\n\n    return zipFileWorker;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/generate/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/index.js":
/*!**************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/index.js ***!
  \**************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n/**\n * Representation a of zip file in js\n * @constructor\n */\nfunction JSZip() {\n    // if this constructor is used without `new`, it adds `new` before itself:\n    if(!(this instanceof JSZip)) {\n        return new JSZip();\n    }\n\n    if(arguments.length) {\n        throw new Error(\"The constructor with parameters has been removed in JSZip 3.0, please check the upgrade guide.\");\n    }\n\n    // object containing the files :\n    // {\n    //   \"folder/\" : {...},\n    //   \"folder/data.txt\" : {...}\n    // }\n    // NOTE: we use a null prototype because we do not\n    // want filenames like \"toString\" coming from a zip file\n    // to overwrite methods and attributes in a normal Object.\n    this.files = Object.create(null);\n\n    this.comment = null;\n\n    // Where we are in the hierarchy\n    this.root = \"\";\n    this.clone = function() {\n        var newObj = new JSZip();\n        for (var i in this) {\n            if (typeof this[i] !== \"function\") {\n                newObj[i] = this[i];\n            }\n        }\n        return newObj;\n    };\n}\nJSZip.prototype = __webpack_require__(/*! ./object */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/object.js\");\nJSZip.prototype.loadAsync = __webpack_require__(/*! ./load */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/load.js\");\nJSZip.support = __webpack_require__(/*! ./support */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/support.js\");\nJSZip.defaults = __webpack_require__(/*! ./defaults */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/defaults.js\");\n\n// TODO find a better way to handle this version,\n// a require('package.json').version doesn't work with webpack, see #327\nJSZip.version = \"3.10.1\";\n\nJSZip.loadAsync = function (content, options) {\n    return new JSZip().loadAsync(content, options);\n};\n\nJSZip.external = __webpack_require__(/*! ./external */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/external.js\");\nmodule.exports = JSZip;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/load.js":
/*!*************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/load.js ***!
  \*************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\nvar external = __webpack_require__(/*! ./external */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/external.js\");\nvar utf8 = __webpack_require__(/*! ./utf8 */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utf8.js\");\nvar ZipEntries = __webpack_require__(/*! ./zipEntries */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/zipEntries.js\");\nvar Crc32Probe = __webpack_require__(/*! ./stream/Crc32Probe */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/Crc32Probe.js\");\nvar nodejsUtils = __webpack_require__(/*! ./nodejsUtils */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejsUtils.js\");\n\n/**\n * Check the CRC32 of an entry.\n * @param {ZipEntry} zipEntry the zip entry to check.\n * @return {Promise} the result.\n */\nfunction checkEntryCRC32(zipEntry) {\n    return new external.Promise(function (resolve, reject) {\n        var worker = zipEntry.decompressed.getContentWorker().pipe(new Crc32Probe());\n        worker.on(\"error\", function (e) {\n            reject(e);\n        })\n            .on(\"end\", function () {\n                if (worker.streamInfo.crc32 !== zipEntry.decompressed.crc32) {\n                    reject(new Error(\"Corrupted zip : CRC32 mismatch\"));\n                } else {\n                    resolve();\n                }\n            })\n            .resume();\n    });\n}\n\nmodule.exports = function (data, options) {\n    var zip = this;\n    options = utils.extend(options || {}, {\n        base64: false,\n        checkCRC32: false,\n        optimizedBinaryString: false,\n        createFolders: false,\n        decodeFileName: utf8.utf8decode\n    });\n\n    if (nodejsUtils.isNode && nodejsUtils.isStream(data)) {\n        return external.Promise.reject(new Error(\"JSZip can't accept a stream when loading a zip file.\"));\n    }\n\n    return utils.prepareContent(\"the loaded zip file\", data, true, options.optimizedBinaryString, options.base64)\n        .then(function (data) {\n            var zipEntries = new ZipEntries(options);\n            zipEntries.load(data);\n            return zipEntries;\n        }).then(function checkCRC32(zipEntries) {\n            var promises = [external.Promise.resolve(zipEntries)];\n            var files = zipEntries.files;\n            if (options.checkCRC32) {\n                for (var i = 0; i < files.length; i++) {\n                    promises.push(checkEntryCRC32(files[i]));\n                }\n            }\n            return external.Promise.all(promises);\n        }).then(function addFiles(results) {\n            var zipEntries = results.shift();\n            var files = zipEntries.files;\n            for (var i = 0; i < files.length; i++) {\n                var input = files[i];\n\n                var unsafeName = input.fileNameStr;\n                var safeName = utils.resolve(input.fileNameStr);\n\n                zip.file(safeName, input.decompressed, {\n                    binary: true,\n                    optimizedBinaryString: true,\n                    date: input.date,\n                    dir: input.dir,\n                    comment: input.fileCommentStr.length ? input.fileCommentStr : null,\n                    unixPermissions: input.unixPermissions,\n                    dosPermissions: input.dosPermissions,\n                    createFolders: options.createFolders\n                });\n                if (!input.dir) {\n                    zip.file(safeName).unsafeOriginalName = unsafeName;\n                }\n            }\n            if (zipEntries.zipComment.length) {\n                zip.comment = zipEntries.zipComment;\n            }\n\n            return zip;\n        });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/load.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejs/NodejsStreamInputAdapter.js":
/*!****************************************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejs/NodejsStreamInputAdapter.js ***!
  \****************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ../stream/GenericWorker */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js\");\n\n/**\n * A worker that use a nodejs stream as source.\n * @constructor\n * @param {String} filename the name of the file entry for this stream.\n * @param {Readable} stream the nodejs stream.\n */\nfunction NodejsStreamInputAdapter(filename, stream) {\n    GenericWorker.call(this, \"Nodejs stream input adapter for \" + filename);\n    this._upstreamEnded = false;\n    this._bindStream(stream);\n}\n\nutils.inherits(NodejsStreamInputAdapter, GenericWorker);\n\n/**\n * Prepare the stream and bind the callbacks on it.\n * Do this ASAP on node 0.10 ! A lazy binding doesn't always work.\n * @param {Stream} stream the nodejs stream to use.\n */\nNodejsStreamInputAdapter.prototype._bindStream = function (stream) {\n    var self = this;\n    this._stream = stream;\n    stream.pause();\n    stream\n        .on(\"data\", function (chunk) {\n            self.push({\n                data: chunk,\n                meta : {\n                    percent : 0\n                }\n            });\n        })\n        .on(\"error\", function (e) {\n            if(self.isPaused) {\n                this.generatedError = e;\n            } else {\n                self.error(e);\n            }\n        })\n        .on(\"end\", function () {\n            if(self.isPaused) {\n                self._upstreamEnded = true;\n            } else {\n                self.end();\n            }\n        });\n};\nNodejsStreamInputAdapter.prototype.pause = function () {\n    if(!GenericWorker.prototype.pause.call(this)) {\n        return false;\n    }\n    this._stream.pause();\n    return true;\n};\nNodejsStreamInputAdapter.prototype.resume = function () {\n    if(!GenericWorker.prototype.resume.call(this)) {\n        return false;\n    }\n\n    if(this._upstreamEnded) {\n        this.end();\n    } else {\n        this._stream.resume();\n    }\n\n    return true;\n};\n\nmodule.exports = NodejsStreamInputAdapter;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejs/NodejsStreamInputAdapter.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejs/NodejsStreamOutputAdapter.js":
/*!*****************************************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejs/NodejsStreamOutputAdapter.js ***!
  \*****************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Readable = (__webpack_require__(/*! readable-stream */ \"(ssr)/../node_modules/.pnpm/readable-stream@2.3.8/node_modules/readable-stream/readable.js\").Readable);\n\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\nutils.inherits(NodejsStreamOutputAdapter, Readable);\n\n/**\n* A nodejs stream using a worker as source.\n* @see the SourceWrapper in http://nodejs.org/api/stream.html\n* @constructor\n* @param {StreamHelper} helper the helper wrapping the worker\n* @param {Object} options the nodejs stream options\n* @param {Function} updateCb the update callback.\n*/\nfunction NodejsStreamOutputAdapter(helper, options, updateCb) {\n    Readable.call(this, options);\n    this._helper = helper;\n\n    var self = this;\n    helper.on(\"data\", function (data, meta) {\n        if (!self.push(data)) {\n            self._helper.pause();\n        }\n        if(updateCb) {\n            updateCb(meta);\n        }\n    })\n        .on(\"error\", function(e) {\n            self.emit(\"error\", e);\n        })\n        .on(\"end\", function () {\n            self.push(null);\n        });\n}\n\n\nNodejsStreamOutputAdapter.prototype._read = function() {\n    this._helper.resume();\n};\n\nmodule.exports = NodejsStreamOutputAdapter;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejs/NodejsStreamOutputAdapter.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejsUtils.js":
/*!********************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejsUtils.js ***!
  \********************************************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = {\n    /**\n     * True if this is running in Nodejs, will be undefined in a browser.\n     * In a browser, browserify won't include this file and the whole module\n     * will be resolved an empty object.\n     */\n    isNode : typeof Buffer !== \"undefined\",\n    /**\n     * Create a new nodejs Buffer from an existing content.\n     * @param {Object} data the data to pass to the constructor.\n     * @param {String} encoding the encoding to use.\n     * @return {Buffer} a new Buffer.\n     */\n    newBufferFrom: function(data, encoding) {\n        if (Buffer.from && Buffer.from !== Uint8Array.from) {\n            return Buffer.from(data, encoding);\n        } else {\n            if (typeof data === \"number\") {\n                // Safeguard for old Node.js versions. On newer versions,\n                // Buffer.from(number) / Buffer(number, encoding) already throw.\n                throw new Error(\"The \\\"data\\\" argument must not be a number\");\n            }\n            return new Buffer(data, encoding);\n        }\n    },\n    /**\n     * Create a new nodejs Buffer with the specified size.\n     * @param {Integer} size the size of the buffer.\n     * @return {Buffer} a new Buffer.\n     */\n    allocBuffer: function (size) {\n        if (Buffer.alloc) {\n            return Buffer.alloc(size);\n        } else {\n            var buf = new Buffer(size);\n            buf.fill(0);\n            return buf;\n        }\n    },\n    /**\n     * Find out if an object is a Buffer.\n     * @param {Object} b the object to test.\n     * @return {Boolean} true if the object is a Buffer, false otherwise.\n     */\n    isBuffer : function(b){\n        return Buffer.isBuffer(b);\n    },\n\n    isStream : function (obj) {\n        return obj &&\n            typeof obj.on === \"function\" &&\n            typeof obj.pause === \"function\" &&\n            typeof obj.resume === \"function\";\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pzemlwQDMuMTAuMS9ub2RlX21vZHVsZXMvanN6aXAvbGliL25vZGVqc1V0aWxzLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZ0JBQWdCLFFBQVE7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLGVBQWUsU0FBUztBQUN4QixnQkFBZ0IsUUFBUTtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGdCQUFnQixTQUFTO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBLEtBQUs7O0FBRUw7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxweWNvZGVcXHN1cHBvcnRfY2hhcnQyXFxub2RlX21vZHVsZXNcXC5wbnBtXFxqc3ppcEAzLjEwLjFcXG5vZGVfbW9kdWxlc1xcanN6aXBcXGxpYlxcbm9kZWpzVXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICAgIC8qKlxuICAgICAqIFRydWUgaWYgdGhpcyBpcyBydW5uaW5nIGluIE5vZGVqcywgd2lsbCBiZSB1bmRlZmluZWQgaW4gYSBicm93c2VyLlxuICAgICAqIEluIGEgYnJvd3NlciwgYnJvd3NlcmlmeSB3b24ndCBpbmNsdWRlIHRoaXMgZmlsZSBhbmQgdGhlIHdob2xlIG1vZHVsZVxuICAgICAqIHdpbGwgYmUgcmVzb2x2ZWQgYW4gZW1wdHkgb2JqZWN0LlxuICAgICAqL1xuICAgIGlzTm9kZSA6IHR5cGVvZiBCdWZmZXIgIT09IFwidW5kZWZpbmVkXCIsXG4gICAgLyoqXG4gICAgICogQ3JlYXRlIGEgbmV3IG5vZGVqcyBCdWZmZXIgZnJvbSBhbiBleGlzdGluZyBjb250ZW50LlxuICAgICAqIEBwYXJhbSB7T2JqZWN0fSBkYXRhIHRoZSBkYXRhIHRvIHBhc3MgdG8gdGhlIGNvbnN0cnVjdG9yLlxuICAgICAqIEBwYXJhbSB7U3RyaW5nfSBlbmNvZGluZyB0aGUgZW5jb2RpbmcgdG8gdXNlLlxuICAgICAqIEByZXR1cm4ge0J1ZmZlcn0gYSBuZXcgQnVmZmVyLlxuICAgICAqL1xuICAgIG5ld0J1ZmZlckZyb206IGZ1bmN0aW9uKGRhdGEsIGVuY29kaW5nKSB7XG4gICAgICAgIGlmIChCdWZmZXIuZnJvbSAmJiBCdWZmZXIuZnJvbSAhPT0gVWludDhBcnJheS5mcm9tKSB7XG4gICAgICAgICAgICByZXR1cm4gQnVmZmVyLmZyb20oZGF0YSwgZW5jb2RpbmcpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgaWYgKHR5cGVvZiBkYXRhID09PSBcIm51bWJlclwiKSB7XG4gICAgICAgICAgICAgICAgLy8gU2FmZWd1YXJkIGZvciBvbGQgTm9kZS5qcyB2ZXJzaW9ucy4gT24gbmV3ZXIgdmVyc2lvbnMsXG4gICAgICAgICAgICAgICAgLy8gQnVmZmVyLmZyb20obnVtYmVyKSAvIEJ1ZmZlcihudW1iZXIsIGVuY29kaW5nKSBhbHJlYWR5IHRocm93LlxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIlRoZSBcXFwiZGF0YVxcXCIgYXJndW1lbnQgbXVzdCBub3QgYmUgYSBudW1iZXJcIik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gbmV3IEJ1ZmZlcihkYXRhLCBlbmNvZGluZyk7XG4gICAgICAgIH1cbiAgICB9LFxuICAgIC8qKlxuICAgICAqIENyZWF0ZSBhIG5ldyBub2RlanMgQnVmZmVyIHdpdGggdGhlIHNwZWNpZmllZCBzaXplLlxuICAgICAqIEBwYXJhbSB7SW50ZWdlcn0gc2l6ZSB0aGUgc2l6ZSBvZiB0aGUgYnVmZmVyLlxuICAgICAqIEByZXR1cm4ge0J1ZmZlcn0gYSBuZXcgQnVmZmVyLlxuICAgICAqL1xuICAgIGFsbG9jQnVmZmVyOiBmdW5jdGlvbiAoc2l6ZSkge1xuICAgICAgICBpZiAoQnVmZmVyLmFsbG9jKSB7XG4gICAgICAgICAgICByZXR1cm4gQnVmZmVyLmFsbG9jKHNpemUpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgdmFyIGJ1ZiA9IG5ldyBCdWZmZXIoc2l6ZSk7XG4gICAgICAgICAgICBidWYuZmlsbCgwKTtcbiAgICAgICAgICAgIHJldHVybiBidWY7XG4gICAgICAgIH1cbiAgICB9LFxuICAgIC8qKlxuICAgICAqIEZpbmQgb3V0IGlmIGFuIG9iamVjdCBpcyBhIEJ1ZmZlci5cbiAgICAgKiBAcGFyYW0ge09iamVjdH0gYiB0aGUgb2JqZWN0IHRvIHRlc3QuXG4gICAgICogQHJldHVybiB7Qm9vbGVhbn0gdHJ1ZSBpZiB0aGUgb2JqZWN0IGlzIGEgQnVmZmVyLCBmYWxzZSBvdGhlcndpc2UuXG4gICAgICovXG4gICAgaXNCdWZmZXIgOiBmdW5jdGlvbihiKXtcbiAgICAgICAgcmV0dXJuIEJ1ZmZlci5pc0J1ZmZlcihiKTtcbiAgICB9LFxuXG4gICAgaXNTdHJlYW0gOiBmdW5jdGlvbiAob2JqKSB7XG4gICAgICAgIHJldHVybiBvYmogJiZcbiAgICAgICAgICAgIHR5cGVvZiBvYmoub24gPT09IFwiZnVuY3Rpb25cIiAmJlxuICAgICAgICAgICAgdHlwZW9mIG9iai5wYXVzZSA9PT0gXCJmdW5jdGlvblwiICYmXG4gICAgICAgICAgICB0eXBlb2Ygb2JqLnJlc3VtZSA9PT0gXCJmdW5jdGlvblwiO1xuICAgIH1cbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejsUtils.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/object.js":
/*!***************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/object.js ***!
  \***************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar utf8 = __webpack_require__(/*! ./utf8 */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utf8.js\");\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ./stream/GenericWorker */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js\");\nvar StreamHelper = __webpack_require__(/*! ./stream/StreamHelper */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/StreamHelper.js\");\nvar defaults = __webpack_require__(/*! ./defaults */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/defaults.js\");\nvar CompressedObject = __webpack_require__(/*! ./compressedObject */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/compressedObject.js\");\nvar ZipObject = __webpack_require__(/*! ./zipObject */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/zipObject.js\");\nvar generate = __webpack_require__(/*! ./generate */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/generate/index.js\");\nvar nodejsUtils = __webpack_require__(/*! ./nodejsUtils */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejsUtils.js\");\nvar NodejsStreamInputAdapter = __webpack_require__(/*! ./nodejs/NodejsStreamInputAdapter */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejs/NodejsStreamInputAdapter.js\");\n\n\n/**\n * Add a file in the current folder.\n * @private\n * @param {string} name the name of the file\n * @param {String|ArrayBuffer|Uint8Array|Buffer} data the data of the file\n * @param {Object} originalOptions the options of the file\n * @return {Object} the new file.\n */\nvar fileAdd = function(name, data, originalOptions) {\n    // be sure sub folders exist\n    var dataType = utils.getTypeOf(data),\n        parent;\n\n\n    /*\n     * Correct options.\n     */\n\n    var o = utils.extend(originalOptions || {}, defaults);\n    o.date = o.date || new Date();\n    if (o.compression !== null) {\n        o.compression = o.compression.toUpperCase();\n    }\n\n    if (typeof o.unixPermissions === \"string\") {\n        o.unixPermissions = parseInt(o.unixPermissions, 8);\n    }\n\n    // UNX_IFDIR  0040000 see zipinfo.c\n    if (o.unixPermissions && (o.unixPermissions & 0x4000)) {\n        o.dir = true;\n    }\n    // Bit 4    Directory\n    if (o.dosPermissions && (o.dosPermissions & 0x0010)) {\n        o.dir = true;\n    }\n\n    if (o.dir) {\n        name = forceTrailingSlash(name);\n    }\n    if (o.createFolders && (parent = parentFolder(name))) {\n        folderAdd.call(this, parent, true);\n    }\n\n    var isUnicodeString = dataType === \"string\" && o.binary === false && o.base64 === false;\n    if (!originalOptions || typeof originalOptions.binary === \"undefined\") {\n        o.binary = !isUnicodeString;\n    }\n\n\n    var isCompressedEmpty = (data instanceof CompressedObject) && data.uncompressedSize === 0;\n\n    if (isCompressedEmpty || o.dir || !data || data.length === 0) {\n        o.base64 = false;\n        o.binary = true;\n        data = \"\";\n        o.compression = \"STORE\";\n        dataType = \"string\";\n    }\n\n    /*\n     * Convert content to fit.\n     */\n\n    var zipObjectContent = null;\n    if (data instanceof CompressedObject || data instanceof GenericWorker) {\n        zipObjectContent = data;\n    } else if (nodejsUtils.isNode && nodejsUtils.isStream(data)) {\n        zipObjectContent = new NodejsStreamInputAdapter(name, data);\n    } else {\n        zipObjectContent = utils.prepareContent(name, data, o.binary, o.optimizedBinaryString, o.base64);\n    }\n\n    var object = new ZipObject(name, zipObjectContent, o);\n    this.files[name] = object;\n    /*\n    TODO: we can't throw an exception because we have async promises\n    (we can have a promise of a Date() for example) but returning a\n    promise is useless because file(name, data) returns the JSZip\n    object for chaining. Should we break that to allow the user\n    to catch the error ?\n\n    return external.Promise.resolve(zipObjectContent)\n    .then(function () {\n        return object;\n    });\n    */\n};\n\n/**\n * Find the parent folder of the path.\n * @private\n * @param {string} path the path to use\n * @return {string} the parent folder, or \"\"\n */\nvar parentFolder = function (path) {\n    if (path.slice(-1) === \"/\") {\n        path = path.substring(0, path.length - 1);\n    }\n    var lastSlash = path.lastIndexOf(\"/\");\n    return (lastSlash > 0) ? path.substring(0, lastSlash) : \"\";\n};\n\n/**\n * Returns the path with a slash at the end.\n * @private\n * @param {String} path the path to check.\n * @return {String} the path with a trailing slash.\n */\nvar forceTrailingSlash = function(path) {\n    // Check the name ends with a /\n    if (path.slice(-1) !== \"/\") {\n        path += \"/\"; // IE doesn't like substr(-1)\n    }\n    return path;\n};\n\n/**\n * Add a (sub) folder in the current folder.\n * @private\n * @param {string} name the folder's name\n * @param {boolean=} [createFolders] If true, automatically create sub\n *  folders. Defaults to false.\n * @return {Object} the new folder.\n */\nvar folderAdd = function(name, createFolders) {\n    createFolders = (typeof createFolders !== \"undefined\") ? createFolders : defaults.createFolders;\n\n    name = forceTrailingSlash(name);\n\n    // Does this folder already exist?\n    if (!this.files[name]) {\n        fileAdd.call(this, name, null, {\n            dir: true,\n            createFolders: createFolders\n        });\n    }\n    return this.files[name];\n};\n\n/**\n* Cross-window, cross-Node-context regular expression detection\n* @param  {Object}  object Anything\n* @return {Boolean}        true if the object is a regular expression,\n* false otherwise\n*/\nfunction isRegExp(object) {\n    return Object.prototype.toString.call(object) === \"[object RegExp]\";\n}\n\n// return the actual prototype of JSZip\nvar out = {\n    /**\n     * @see loadAsync\n     */\n    load: function() {\n        throw new Error(\"This method has been removed in JSZip 3.0, please check the upgrade guide.\");\n    },\n\n\n    /**\n     * Call a callback function for each entry at this folder level.\n     * @param {Function} cb the callback function:\n     * function (relativePath, file) {...}\n     * It takes 2 arguments : the relative path and the file.\n     */\n    forEach: function(cb) {\n        var filename, relativePath, file;\n        // ignore warning about unwanted properties because this.files is a null prototype object\n        /* eslint-disable-next-line guard-for-in */\n        for (filename in this.files) {\n            file = this.files[filename];\n            relativePath = filename.slice(this.root.length, filename.length);\n            if (relativePath && filename.slice(0, this.root.length) === this.root) { // the file is in the current root\n                cb(relativePath, file); // TODO reverse the parameters ? need to be clean AND consistent with the filter search fn...\n            }\n        }\n    },\n\n    /**\n     * Filter nested files/folders with the specified function.\n     * @param {Function} search the predicate to use :\n     * function (relativePath, file) {...}\n     * It takes 2 arguments : the relative path and the file.\n     * @return {Array} An array of matching elements.\n     */\n    filter: function(search) {\n        var result = [];\n        this.forEach(function (relativePath, entry) {\n            if (search(relativePath, entry)) { // the file matches the function\n                result.push(entry);\n            }\n\n        });\n        return result;\n    },\n\n    /**\n     * Add a file to the zip file, or search a file.\n     * @param   {string|RegExp} name The name of the file to add (if data is defined),\n     * the name of the file to find (if no data) or a regex to match files.\n     * @param   {String|ArrayBuffer|Uint8Array|Buffer} data  The file data, either raw or base64 encoded\n     * @param   {Object} o     File options\n     * @return  {JSZip|Object|Array} this JSZip object (when adding a file),\n     * a file (when searching by string) or an array of files (when searching by regex).\n     */\n    file: function(name, data, o) {\n        if (arguments.length === 1) {\n            if (isRegExp(name)) {\n                var regexp = name;\n                return this.filter(function(relativePath, file) {\n                    return !file.dir && regexp.test(relativePath);\n                });\n            }\n            else { // text\n                var obj = this.files[this.root + name];\n                if (obj && !obj.dir) {\n                    return obj;\n                } else {\n                    return null;\n                }\n            }\n        }\n        else { // more than one argument : we have data !\n            name = this.root + name;\n            fileAdd.call(this, name, data, o);\n        }\n        return this;\n    },\n\n    /**\n     * Add a directory to the zip file, or search.\n     * @param   {String|RegExp} arg The name of the directory to add, or a regex to search folders.\n     * @return  {JSZip} an object with the new directory as the root, or an array containing matching folders.\n     */\n    folder: function(arg) {\n        if (!arg) {\n            return this;\n        }\n\n        if (isRegExp(arg)) {\n            return this.filter(function(relativePath, file) {\n                return file.dir && arg.test(relativePath);\n            });\n        }\n\n        // else, name is a new folder\n        var name = this.root + arg;\n        var newFolder = folderAdd.call(this, name);\n\n        // Allow chaining by returning a new object with this folder as the root\n        var ret = this.clone();\n        ret.root = newFolder.name;\n        return ret;\n    },\n\n    /**\n     * Delete a file, or a directory and all sub-files, from the zip\n     * @param {string} name the name of the file to delete\n     * @return {JSZip} this JSZip object\n     */\n    remove: function(name) {\n        name = this.root + name;\n        var file = this.files[name];\n        if (!file) {\n            // Look for any folders\n            if (name.slice(-1) !== \"/\") {\n                name += \"/\";\n            }\n            file = this.files[name];\n        }\n\n        if (file && !file.dir) {\n            // file\n            delete this.files[name];\n        } else {\n            // maybe a folder, delete recursively\n            var kids = this.filter(function(relativePath, file) {\n                return file.name.slice(0, name.length) === name;\n            });\n            for (var i = 0; i < kids.length; i++) {\n                delete this.files[kids[i].name];\n            }\n        }\n\n        return this;\n    },\n\n    /**\n     * @deprecated This method has been removed in JSZip 3.0, please check the upgrade guide.\n     */\n    generate: function() {\n        throw new Error(\"This method has been removed in JSZip 3.0, please check the upgrade guide.\");\n    },\n\n    /**\n     * Generate the complete zip file as an internal stream.\n     * @param {Object} options the options to generate the zip file :\n     * - compression, \"STORE\" by default.\n     * - type, \"base64\" by default. Values are : string, base64, uint8array, arraybuffer, blob.\n     * @return {StreamHelper} the streamed zip file.\n     */\n    generateInternalStream: function(options) {\n        var worker, opts = {};\n        try {\n            opts = utils.extend(options || {}, {\n                streamFiles: false,\n                compression: \"STORE\",\n                compressionOptions : null,\n                type: \"\",\n                platform: \"DOS\",\n                comment: null,\n                mimeType: \"application/zip\",\n                encodeFileName: utf8.utf8encode\n            });\n\n            opts.type = opts.type.toLowerCase();\n            opts.compression = opts.compression.toUpperCase();\n\n            // \"binarystring\" is preferred but the internals use \"string\".\n            if(opts.type === \"binarystring\") {\n                opts.type = \"string\";\n            }\n\n            if (!opts.type) {\n                throw new Error(\"No output type specified.\");\n            }\n\n            utils.checkSupport(opts.type);\n\n            // accept nodejs `process.platform`\n            if(\n                opts.platform === \"darwin\" ||\n                opts.platform === \"freebsd\" ||\n                opts.platform === \"linux\" ||\n                opts.platform === \"sunos\"\n            ) {\n                opts.platform = \"UNIX\";\n            }\n            if (opts.platform === \"win32\") {\n                opts.platform = \"DOS\";\n            }\n\n            var comment = opts.comment || this.comment || \"\";\n            worker = generate.generateWorker(this, opts, comment);\n        } catch (e) {\n            worker = new GenericWorker(\"error\");\n            worker.error(e);\n        }\n        return new StreamHelper(worker, opts.type || \"string\", opts.mimeType);\n    },\n    /**\n     * Generate the complete zip file asynchronously.\n     * @see generateInternalStream\n     */\n    generateAsync: function(options, onUpdate) {\n        return this.generateInternalStream(options).accumulate(onUpdate);\n    },\n    /**\n     * Generate the complete zip file asynchronously.\n     * @see generateInternalStream\n     */\n    generateNodeStream: function(options, onUpdate) {\n        options = options || {};\n        if (!options.type) {\n            options.type = \"nodebuffer\";\n        }\n        return this.generateInternalStream(options).toNodejsStream(onUpdate);\n    }\n};\nmodule.exports = out;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/object.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/ArrayReader.js":
/*!***************************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/ArrayReader.js ***!
  \***************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar DataReader = __webpack_require__(/*! ./DataReader */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/DataReader.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\n\nfunction ArrayReader(data) {\n    DataReader.call(this, data);\n    for(var i = 0; i < this.data.length; i++) {\n        data[i] = data[i] & 0xFF;\n    }\n}\nutils.inherits(ArrayReader, DataReader);\n/**\n * @see DataReader.byteAt\n */\nArrayReader.prototype.byteAt = function(i) {\n    return this.data[this.zero + i];\n};\n/**\n * @see DataReader.lastIndexOfSignature\n */\nArrayReader.prototype.lastIndexOfSignature = function(sig) {\n    var sig0 = sig.charCodeAt(0),\n        sig1 = sig.charCodeAt(1),\n        sig2 = sig.charCodeAt(2),\n        sig3 = sig.charCodeAt(3);\n    for (var i = this.length - 4; i >= 0; --i) {\n        if (this.data[i] === sig0 && this.data[i + 1] === sig1 && this.data[i + 2] === sig2 && this.data[i + 3] === sig3) {\n            return i - this.zero;\n        }\n    }\n\n    return -1;\n};\n/**\n * @see DataReader.readAndCheckSignature\n */\nArrayReader.prototype.readAndCheckSignature = function (sig) {\n    var sig0 = sig.charCodeAt(0),\n        sig1 = sig.charCodeAt(1),\n        sig2 = sig.charCodeAt(2),\n        sig3 = sig.charCodeAt(3),\n        data = this.readData(4);\n    return sig0 === data[0] && sig1 === data[1] && sig2 === data[2] && sig3 === data[3];\n};\n/**\n * @see DataReader.readData\n */\nArrayReader.prototype.readData = function(size) {\n    this.checkOffset(size);\n    if(size === 0) {\n        return [];\n    }\n    var result = this.data.slice(this.zero + this.index, this.zero + this.index + size);\n    this.index += size;\n    return result;\n};\nmodule.exports = ArrayReader;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/ArrayReader.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/DataReader.js":
/*!**************************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/DataReader.js ***!
  \**************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\n\nfunction DataReader(data) {\n    this.data = data; // type : see implementation\n    this.length = data.length;\n    this.index = 0;\n    this.zero = 0;\n}\nDataReader.prototype = {\n    /**\n     * Check that the offset will not go too far.\n     * @param {string} offset the additional offset to check.\n     * @throws {Error} an Error if the offset is out of bounds.\n     */\n    checkOffset: function(offset) {\n        this.checkIndex(this.index + offset);\n    },\n    /**\n     * Check that the specified index will not be too far.\n     * @param {string} newIndex the index to check.\n     * @throws {Error} an Error if the index is out of bounds.\n     */\n    checkIndex: function(newIndex) {\n        if (this.length < this.zero + newIndex || newIndex < 0) {\n            throw new Error(\"End of data reached (data length = \" + this.length + \", asked index = \" + (newIndex) + \"). Corrupted zip ?\");\n        }\n    },\n    /**\n     * Change the index.\n     * @param {number} newIndex The new index.\n     * @throws {Error} if the new index is out of the data.\n     */\n    setIndex: function(newIndex) {\n        this.checkIndex(newIndex);\n        this.index = newIndex;\n    },\n    /**\n     * Skip the next n bytes.\n     * @param {number} n the number of bytes to skip.\n     * @throws {Error} if the new index is out of the data.\n     */\n    skip: function(n) {\n        this.setIndex(this.index + n);\n    },\n    /**\n     * Get the byte at the specified index.\n     * @param {number} i the index to use.\n     * @return {number} a byte.\n     */\n    byteAt: function() {\n        // see implementations\n    },\n    /**\n     * Get the next number with a given byte size.\n     * @param {number} size the number of bytes to read.\n     * @return {number} the corresponding number.\n     */\n    readInt: function(size) {\n        var result = 0,\n            i;\n        this.checkOffset(size);\n        for (i = this.index + size - 1; i >= this.index; i--) {\n            result = (result << 8) + this.byteAt(i);\n        }\n        this.index += size;\n        return result;\n    },\n    /**\n     * Get the next string with a given byte size.\n     * @param {number} size the number of bytes to read.\n     * @return {string} the corresponding string.\n     */\n    readString: function(size) {\n        return utils.transformTo(\"string\", this.readData(size));\n    },\n    /**\n     * Get raw data without conversion, <size> bytes.\n     * @param {number} size the number of bytes to read.\n     * @return {Object} the raw data, implementation specific.\n     */\n    readData: function() {\n        // see implementations\n    },\n    /**\n     * Find the last occurrence of a zip signature (4 bytes).\n     * @param {string} sig the signature to find.\n     * @return {number} the index of the last occurrence, -1 if not found.\n     */\n    lastIndexOfSignature: function() {\n        // see implementations\n    },\n    /**\n     * Read the signature (4 bytes) at the current position and compare it with sig.\n     * @param {string} sig the expected signature\n     * @return {boolean} true if the signature matches, false otherwise.\n     */\n    readAndCheckSignature: function() {\n        // see implementations\n    },\n    /**\n     * Get the next date.\n     * @return {Date} the date.\n     */\n    readDate: function() {\n        var dostime = this.readInt(4);\n        return new Date(Date.UTC(\n            ((dostime >> 25) & 0x7f) + 1980, // year\n            ((dostime >> 21) & 0x0f) - 1, // month\n            (dostime >> 16) & 0x1f, // day\n            (dostime >> 11) & 0x1f, // hour\n            (dostime >> 5) & 0x3f, // minute\n            (dostime & 0x1f) << 1)); // second\n    }\n};\nmodule.exports = DataReader;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/DataReader.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/NodeBufferReader.js":
/*!********************************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/NodeBufferReader.js ***!
  \********************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar Uint8ArrayReader = __webpack_require__(/*! ./Uint8ArrayReader */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/Uint8ArrayReader.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\n\nfunction NodeBufferReader(data) {\n    Uint8ArrayReader.call(this, data);\n}\nutils.inherits(NodeBufferReader, Uint8ArrayReader);\n\n/**\n * @see DataReader.readData\n */\nNodeBufferReader.prototype.readData = function(size) {\n    this.checkOffset(size);\n    var result = this.data.slice(this.zero + this.index, this.zero + this.index + size);\n    this.index += size;\n    return result;\n};\nmodule.exports = NodeBufferReader;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pzemlwQDMuMTAuMS9ub2RlX21vZHVsZXMvanN6aXAvbGliL3JlYWRlci9Ob2RlQnVmZmVyUmVhZGVyLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsdUJBQXVCLG1CQUFPLENBQUMsc0hBQW9CO0FBQ25ELFlBQVksbUJBQU8sQ0FBQywwRkFBVTs7QUFFOUI7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxweWNvZGVcXHN1cHBvcnRfY2hhcnQyXFxub2RlX21vZHVsZXNcXC5wbnBtXFxqc3ppcEAzLjEwLjFcXG5vZGVfbW9kdWxlc1xcanN6aXBcXGxpYlxccmVhZGVyXFxOb2RlQnVmZmVyUmVhZGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xudmFyIFVpbnQ4QXJyYXlSZWFkZXIgPSByZXF1aXJlKFwiLi9VaW50OEFycmF5UmVhZGVyXCIpO1xudmFyIHV0aWxzID0gcmVxdWlyZShcIi4uL3V0aWxzXCIpO1xuXG5mdW5jdGlvbiBOb2RlQnVmZmVyUmVhZGVyKGRhdGEpIHtcbiAgICBVaW50OEFycmF5UmVhZGVyLmNhbGwodGhpcywgZGF0YSk7XG59XG51dGlscy5pbmhlcml0cyhOb2RlQnVmZmVyUmVhZGVyLCBVaW50OEFycmF5UmVhZGVyKTtcblxuLyoqXG4gKiBAc2VlIERhdGFSZWFkZXIucmVhZERhdGFcbiAqL1xuTm9kZUJ1ZmZlclJlYWRlci5wcm90b3R5cGUucmVhZERhdGEgPSBmdW5jdGlvbihzaXplKSB7XG4gICAgdGhpcy5jaGVja09mZnNldChzaXplKTtcbiAgICB2YXIgcmVzdWx0ID0gdGhpcy5kYXRhLnNsaWNlKHRoaXMuemVybyArIHRoaXMuaW5kZXgsIHRoaXMuemVybyArIHRoaXMuaW5kZXggKyBzaXplKTtcbiAgICB0aGlzLmluZGV4ICs9IHNpemU7XG4gICAgcmV0dXJuIHJlc3VsdDtcbn07XG5tb2R1bGUuZXhwb3J0cyA9IE5vZGVCdWZmZXJSZWFkZXI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/NodeBufferReader.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/StringReader.js":
/*!****************************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/StringReader.js ***!
  \****************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar DataReader = __webpack_require__(/*! ./DataReader */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/DataReader.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\n\nfunction StringReader(data) {\n    DataReader.call(this, data);\n}\nutils.inherits(StringReader, DataReader);\n/**\n * @see DataReader.byteAt\n */\nStringReader.prototype.byteAt = function(i) {\n    return this.data.charCodeAt(this.zero + i);\n};\n/**\n * @see DataReader.lastIndexOfSignature\n */\nStringReader.prototype.lastIndexOfSignature = function(sig) {\n    return this.data.lastIndexOf(sig) - this.zero;\n};\n/**\n * @see DataReader.readAndCheckSignature\n */\nStringReader.prototype.readAndCheckSignature = function (sig) {\n    var data = this.readData(4);\n    return sig === data;\n};\n/**\n * @see DataReader.readData\n */\nStringReader.prototype.readData = function(size) {\n    this.checkOffset(size);\n    // this will work because the constructor applied the \"& 0xff\" mask.\n    var result = this.data.slice(this.zero + this.index, this.zero + this.index + size);\n    this.index += size;\n    return result;\n};\nmodule.exports = StringReader;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/StringReader.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/Uint8ArrayReader.js":
/*!********************************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/Uint8ArrayReader.js ***!
  \********************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar ArrayReader = __webpack_require__(/*! ./ArrayReader */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/ArrayReader.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\n\nfunction Uint8ArrayReader(data) {\n    ArrayReader.call(this, data);\n}\nutils.inherits(Uint8ArrayReader, ArrayReader);\n/**\n * @see DataReader.readData\n */\nUint8ArrayReader.prototype.readData = function(size) {\n    this.checkOffset(size);\n    if(size === 0) {\n        // in IE10, when using subarray(idx, idx), we get the array [0x00] instead of [].\n        return new Uint8Array(0);\n    }\n    var result = this.data.subarray(this.zero + this.index, this.zero + this.index + size);\n    this.index += size;\n    return result;\n};\nmodule.exports = Uint8ArrayReader;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pzemlwQDMuMTAuMS9ub2RlX21vZHVsZXMvanN6aXAvbGliL3JlYWRlci9VaW50OEFycmF5UmVhZGVyLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2Isa0JBQWtCLG1CQUFPLENBQUMsNEdBQWU7QUFDekMsWUFBWSxtQkFBTyxDQUFDLDBGQUFVOztBQUU5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxweWNvZGVcXHN1cHBvcnRfY2hhcnQyXFxub2RlX21vZHVsZXNcXC5wbnBtXFxqc3ppcEAzLjEwLjFcXG5vZGVfbW9kdWxlc1xcanN6aXBcXGxpYlxccmVhZGVyXFxVaW50OEFycmF5UmVhZGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xudmFyIEFycmF5UmVhZGVyID0gcmVxdWlyZShcIi4vQXJyYXlSZWFkZXJcIik7XG52YXIgdXRpbHMgPSByZXF1aXJlKFwiLi4vdXRpbHNcIik7XG5cbmZ1bmN0aW9uIFVpbnQ4QXJyYXlSZWFkZXIoZGF0YSkge1xuICAgIEFycmF5UmVhZGVyLmNhbGwodGhpcywgZGF0YSk7XG59XG51dGlscy5pbmhlcml0cyhVaW50OEFycmF5UmVhZGVyLCBBcnJheVJlYWRlcik7XG4vKipcbiAqIEBzZWUgRGF0YVJlYWRlci5yZWFkRGF0YVxuICovXG5VaW50OEFycmF5UmVhZGVyLnByb3RvdHlwZS5yZWFkRGF0YSA9IGZ1bmN0aW9uKHNpemUpIHtcbiAgICB0aGlzLmNoZWNrT2Zmc2V0KHNpemUpO1xuICAgIGlmKHNpemUgPT09IDApIHtcbiAgICAgICAgLy8gaW4gSUUxMCwgd2hlbiB1c2luZyBzdWJhcnJheShpZHgsIGlkeCksIHdlIGdldCB0aGUgYXJyYXkgWzB4MDBdIGluc3RlYWQgb2YgW10uXG4gICAgICAgIHJldHVybiBuZXcgVWludDhBcnJheSgwKTtcbiAgICB9XG4gICAgdmFyIHJlc3VsdCA9IHRoaXMuZGF0YS5zdWJhcnJheSh0aGlzLnplcm8gKyB0aGlzLmluZGV4LCB0aGlzLnplcm8gKyB0aGlzLmluZGV4ICsgc2l6ZSk7XG4gICAgdGhpcy5pbmRleCArPSBzaXplO1xuICAgIHJldHVybiByZXN1bHQ7XG59O1xubW9kdWxlLmV4cG9ydHMgPSBVaW50OEFycmF5UmVhZGVyO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/Uint8ArrayReader.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/readerFor.js":
/*!*************************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/readerFor.js ***!
  \*************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\nvar support = __webpack_require__(/*! ../support */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/support.js\");\nvar ArrayReader = __webpack_require__(/*! ./ArrayReader */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/ArrayReader.js\");\nvar StringReader = __webpack_require__(/*! ./StringReader */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/StringReader.js\");\nvar NodeBufferReader = __webpack_require__(/*! ./NodeBufferReader */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/NodeBufferReader.js\");\nvar Uint8ArrayReader = __webpack_require__(/*! ./Uint8ArrayReader */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/Uint8ArrayReader.js\");\n\n/**\n * Create a reader adapted to the data.\n * @param {String|ArrayBuffer|Uint8Array|Buffer} data the data to read.\n * @return {DataReader} the data reader.\n */\nmodule.exports = function (data) {\n    var type = utils.getTypeOf(data);\n    utils.checkSupport(type);\n    if (type === \"string\" && !support.uint8array) {\n        return new StringReader(data);\n    }\n    if (type === \"nodebuffer\") {\n        return new NodeBufferReader(data);\n    }\n    if (support.uint8array) {\n        return new Uint8ArrayReader(utils.transformTo(\"uint8array\", data));\n    }\n    return new ArrayReader(utils.transformTo(\"array\", data));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/readerFor.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/signature.js":
/*!******************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/signature.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nexports.LOCAL_FILE_HEADER = \"PK\\x03\\x04\";\nexports.CENTRAL_FILE_HEADER = \"PK\\x01\\x02\";\nexports.CENTRAL_DIRECTORY_END = \"PK\\x05\\x06\";\nexports.ZIP64_CENTRAL_DIRECTORY_LOCATOR = \"PK\\x06\\x07\";\nexports.ZIP64_CENTRAL_DIRECTORY_END = \"PK\\x06\\x06\";\nexports.DATA_DESCRIPTOR = \"PK\\x07\\x08\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pzemlwQDMuMTAuMS9ub2RlX21vZHVsZXMvanN6aXAvbGliL3NpZ25hdHVyZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLHlCQUF5QjtBQUN6QiwyQkFBMkI7QUFDM0IsNkJBQTZCO0FBQzdCLHVDQUF1QztBQUN2QyxtQ0FBbUM7QUFDbkMsdUJBQXVCIiwic291cmNlcyI6WyJEOlxccHljb2RlXFxzdXBwb3J0X2NoYXJ0Mlxcbm9kZV9tb2R1bGVzXFwucG5wbVxcanN6aXBAMy4xMC4xXFxub2RlX21vZHVsZXNcXGpzemlwXFxsaWJcXHNpZ25hdHVyZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbmV4cG9ydHMuTE9DQUxfRklMRV9IRUFERVIgPSBcIlBLXFx4MDNcXHgwNFwiO1xuZXhwb3J0cy5DRU5UUkFMX0ZJTEVfSEVBREVSID0gXCJQS1xceDAxXFx4MDJcIjtcbmV4cG9ydHMuQ0VOVFJBTF9ESVJFQ1RPUllfRU5EID0gXCJQS1xceDA1XFx4MDZcIjtcbmV4cG9ydHMuWklQNjRfQ0VOVFJBTF9ESVJFQ1RPUllfTE9DQVRPUiA9IFwiUEtcXHgwNlxceDA3XCI7XG5leHBvcnRzLlpJUDY0X0NFTlRSQUxfRElSRUNUT1JZX0VORCA9IFwiUEtcXHgwNlxceDA2XCI7XG5leHBvcnRzLkRBVEFfREVTQ1JJUFRPUiA9IFwiUEtcXHgwN1xceDA4XCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/signature.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/ConvertWorker.js":
/*!*****************************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/ConvertWorker.js ***!
  \*****************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar GenericWorker = __webpack_require__(/*! ./GenericWorker */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\n\n/**\n * A worker which convert chunks to a specified type.\n * @constructor\n * @param {String} destType the destination type.\n */\nfunction ConvertWorker(destType) {\n    GenericWorker.call(this, \"ConvertWorker to \" + destType);\n    this.destType = destType;\n}\nutils.inherits(ConvertWorker, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nConvertWorker.prototype.processChunk = function (chunk) {\n    this.push({\n        data : utils.transformTo(this.destType, chunk.data),\n        meta : chunk.meta\n    });\n};\nmodule.exports = ConvertWorker;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pzemlwQDMuMTAuMS9ub2RlX21vZHVsZXMvanN6aXAvbGliL3N0cmVhbS9Db252ZXJ0V29ya2VyLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLG9CQUFvQixtQkFBTyxDQUFDLGdIQUFpQjtBQUM3QyxZQUFZLG1CQUFPLENBQUMsMEZBQVU7O0FBRTlCO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHB5Y29kZVxcc3VwcG9ydF9jaGFydDJcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGpzemlwQDMuMTAuMVxcbm9kZV9tb2R1bGVzXFxqc3ppcFxcbGliXFxzdHJlYW1cXENvbnZlcnRXb3JrZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbnZhciBHZW5lcmljV29ya2VyID0gcmVxdWlyZShcIi4vR2VuZXJpY1dvcmtlclwiKTtcbnZhciB1dGlscyA9IHJlcXVpcmUoXCIuLi91dGlsc1wiKTtcblxuLyoqXG4gKiBBIHdvcmtlciB3aGljaCBjb252ZXJ0IGNodW5rcyB0byBhIHNwZWNpZmllZCB0eXBlLlxuICogQGNvbnN0cnVjdG9yXG4gKiBAcGFyYW0ge1N0cmluZ30gZGVzdFR5cGUgdGhlIGRlc3RpbmF0aW9uIHR5cGUuXG4gKi9cbmZ1bmN0aW9uIENvbnZlcnRXb3JrZXIoZGVzdFR5cGUpIHtcbiAgICBHZW5lcmljV29ya2VyLmNhbGwodGhpcywgXCJDb252ZXJ0V29ya2VyIHRvIFwiICsgZGVzdFR5cGUpO1xuICAgIHRoaXMuZGVzdFR5cGUgPSBkZXN0VHlwZTtcbn1cbnV0aWxzLmluaGVyaXRzKENvbnZlcnRXb3JrZXIsIEdlbmVyaWNXb3JrZXIpO1xuXG4vKipcbiAqIEBzZWUgR2VuZXJpY1dvcmtlci5wcm9jZXNzQ2h1bmtcbiAqL1xuQ29udmVydFdvcmtlci5wcm90b3R5cGUucHJvY2Vzc0NodW5rID0gZnVuY3Rpb24gKGNodW5rKSB7XG4gICAgdGhpcy5wdXNoKHtcbiAgICAgICAgZGF0YSA6IHV0aWxzLnRyYW5zZm9ybVRvKHRoaXMuZGVzdFR5cGUsIGNodW5rLmRhdGEpLFxuICAgICAgICBtZXRhIDogY2h1bmsubWV0YVxuICAgIH0pO1xufTtcbm1vZHVsZS5leHBvcnRzID0gQ29udmVydFdvcmtlcjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/ConvertWorker.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/Crc32Probe.js":
/*!**************************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/Crc32Probe.js ***!
  \**************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar GenericWorker = __webpack_require__(/*! ./GenericWorker */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js\");\nvar crc32 = __webpack_require__(/*! ../crc32 */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/crc32.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\n\n/**\n * A worker which calculate the crc32 of the data flowing through.\n * @constructor\n */\nfunction Crc32Probe() {\n    GenericWorker.call(this, \"Crc32Probe\");\n    this.withStreamInfo(\"crc32\", 0);\n}\nutils.inherits(Crc32Probe, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nCrc32Probe.prototype.processChunk = function (chunk) {\n    this.streamInfo.crc32 = crc32(chunk.data, this.streamInfo.crc32 || 0);\n    this.push(chunk);\n};\nmodule.exports = Crc32Probe;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pzemlwQDMuMTAuMS9ub2RlX21vZHVsZXMvanN6aXAvbGliL3N0cmVhbS9DcmMzMlByb2JlLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLG9CQUFvQixtQkFBTyxDQUFDLGdIQUFpQjtBQUM3QyxZQUFZLG1CQUFPLENBQUMsMEZBQVU7QUFDOUIsWUFBWSxtQkFBTyxDQUFDLDBGQUFVOztBQUU5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHB5Y29kZVxcc3VwcG9ydF9jaGFydDJcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGpzemlwQDMuMTAuMVxcbm9kZV9tb2R1bGVzXFxqc3ppcFxcbGliXFxzdHJlYW1cXENyYzMyUHJvYmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbnZhciBHZW5lcmljV29ya2VyID0gcmVxdWlyZShcIi4vR2VuZXJpY1dvcmtlclwiKTtcbnZhciBjcmMzMiA9IHJlcXVpcmUoXCIuLi9jcmMzMlwiKTtcbnZhciB1dGlscyA9IHJlcXVpcmUoXCIuLi91dGlsc1wiKTtcblxuLyoqXG4gKiBBIHdvcmtlciB3aGljaCBjYWxjdWxhdGUgdGhlIGNyYzMyIG9mIHRoZSBkYXRhIGZsb3dpbmcgdGhyb3VnaC5cbiAqIEBjb25zdHJ1Y3RvclxuICovXG5mdW5jdGlvbiBDcmMzMlByb2JlKCkge1xuICAgIEdlbmVyaWNXb3JrZXIuY2FsbCh0aGlzLCBcIkNyYzMyUHJvYmVcIik7XG4gICAgdGhpcy53aXRoU3RyZWFtSW5mbyhcImNyYzMyXCIsIDApO1xufVxudXRpbHMuaW5oZXJpdHMoQ3JjMzJQcm9iZSwgR2VuZXJpY1dvcmtlcik7XG5cbi8qKlxuICogQHNlZSBHZW5lcmljV29ya2VyLnByb2Nlc3NDaHVua1xuICovXG5DcmMzMlByb2JlLnByb3RvdHlwZS5wcm9jZXNzQ2h1bmsgPSBmdW5jdGlvbiAoY2h1bmspIHtcbiAgICB0aGlzLnN0cmVhbUluZm8uY3JjMzIgPSBjcmMzMihjaHVuay5kYXRhLCB0aGlzLnN0cmVhbUluZm8uY3JjMzIgfHwgMCk7XG4gICAgdGhpcy5wdXNoKGNodW5rKTtcbn07XG5tb2R1bGUuZXhwb3J0cyA9IENyYzMyUHJvYmU7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/Crc32Probe.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/DataLengthProbe.js":
/*!*******************************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/DataLengthProbe.js ***!
  \*******************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ./GenericWorker */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js\");\n\n/**\n * A worker which calculate the total length of the data flowing through.\n * @constructor\n * @param {String} propName the name used to expose the length\n */\nfunction DataLengthProbe(propName) {\n    GenericWorker.call(this, \"DataLengthProbe for \" + propName);\n    this.propName = propName;\n    this.withStreamInfo(propName, 0);\n}\nutils.inherits(DataLengthProbe, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nDataLengthProbe.prototype.processChunk = function (chunk) {\n    if(chunk) {\n        var length = this.streamInfo[this.propName] || 0;\n        this.streamInfo[this.propName] = length + chunk.data.length;\n    }\n    GenericWorker.prototype.processChunk.call(this, chunk);\n};\nmodule.exports = DataLengthProbe;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pzemlwQDMuMTAuMS9ub2RlX21vZHVsZXMvanN6aXAvbGliL3N0cmVhbS9EYXRhTGVuZ3RoUHJvYmUuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsWUFBWSxtQkFBTyxDQUFDLDBGQUFVO0FBQzlCLG9CQUFvQixtQkFBTyxDQUFDLGdIQUFpQjs7QUFFN0M7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxweWNvZGVcXHN1cHBvcnRfY2hhcnQyXFxub2RlX21vZHVsZXNcXC5wbnBtXFxqc3ppcEAzLjEwLjFcXG5vZGVfbW9kdWxlc1xcanN6aXBcXGxpYlxcc3RyZWFtXFxEYXRhTGVuZ3RoUHJvYmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbnZhciB1dGlscyA9IHJlcXVpcmUoXCIuLi91dGlsc1wiKTtcbnZhciBHZW5lcmljV29ya2VyID0gcmVxdWlyZShcIi4vR2VuZXJpY1dvcmtlclwiKTtcblxuLyoqXG4gKiBBIHdvcmtlciB3aGljaCBjYWxjdWxhdGUgdGhlIHRvdGFsIGxlbmd0aCBvZiB0aGUgZGF0YSBmbG93aW5nIHRocm91Z2guXG4gKiBAY29uc3RydWN0b3JcbiAqIEBwYXJhbSB7U3RyaW5nfSBwcm9wTmFtZSB0aGUgbmFtZSB1c2VkIHRvIGV4cG9zZSB0aGUgbGVuZ3RoXG4gKi9cbmZ1bmN0aW9uIERhdGFMZW5ndGhQcm9iZShwcm9wTmFtZSkge1xuICAgIEdlbmVyaWNXb3JrZXIuY2FsbCh0aGlzLCBcIkRhdGFMZW5ndGhQcm9iZSBmb3IgXCIgKyBwcm9wTmFtZSk7XG4gICAgdGhpcy5wcm9wTmFtZSA9IHByb3BOYW1lO1xuICAgIHRoaXMud2l0aFN0cmVhbUluZm8ocHJvcE5hbWUsIDApO1xufVxudXRpbHMuaW5oZXJpdHMoRGF0YUxlbmd0aFByb2JlLCBHZW5lcmljV29ya2VyKTtcblxuLyoqXG4gKiBAc2VlIEdlbmVyaWNXb3JrZXIucHJvY2Vzc0NodW5rXG4gKi9cbkRhdGFMZW5ndGhQcm9iZS5wcm90b3R5cGUucHJvY2Vzc0NodW5rID0gZnVuY3Rpb24gKGNodW5rKSB7XG4gICAgaWYoY2h1bmspIHtcbiAgICAgICAgdmFyIGxlbmd0aCA9IHRoaXMuc3RyZWFtSW5mb1t0aGlzLnByb3BOYW1lXSB8fCAwO1xuICAgICAgICB0aGlzLnN0cmVhbUluZm9bdGhpcy5wcm9wTmFtZV0gPSBsZW5ndGggKyBjaHVuay5kYXRhLmxlbmd0aDtcbiAgICB9XG4gICAgR2VuZXJpY1dvcmtlci5wcm90b3R5cGUucHJvY2Vzc0NodW5rLmNhbGwodGhpcywgY2h1bmspO1xufTtcbm1vZHVsZS5leHBvcnRzID0gRGF0YUxlbmd0aFByb2JlO1xuXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/DataLengthProbe.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/DataWorker.js":
/*!**************************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/DataWorker.js ***!
  \**************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ./GenericWorker */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js\");\n\n// the size of the generated chunks\n// TODO expose this as a public variable\nvar DEFAULT_BLOCK_SIZE = 16 * 1024;\n\n/**\n * A worker that reads a content and emits chunks.\n * @constructor\n * @param {Promise} dataP the promise of the data to split\n */\nfunction DataWorker(dataP) {\n    GenericWorker.call(this, \"DataWorker\");\n    var self = this;\n    this.dataIsReady = false;\n    this.index = 0;\n    this.max = 0;\n    this.data = null;\n    this.type = \"\";\n\n    this._tickScheduled = false;\n\n    dataP.then(function (data) {\n        self.dataIsReady = true;\n        self.data = data;\n        self.max = data && data.length || 0;\n        self.type = utils.getTypeOf(data);\n        if(!self.isPaused) {\n            self._tickAndRepeat();\n        }\n    }, function (e) {\n        self.error(e);\n    });\n}\n\nutils.inherits(DataWorker, GenericWorker);\n\n/**\n * @see GenericWorker.cleanUp\n */\nDataWorker.prototype.cleanUp = function () {\n    GenericWorker.prototype.cleanUp.call(this);\n    this.data = null;\n};\n\n/**\n * @see GenericWorker.resume\n */\nDataWorker.prototype.resume = function () {\n    if(!GenericWorker.prototype.resume.call(this)) {\n        return false;\n    }\n\n    if (!this._tickScheduled && this.dataIsReady) {\n        this._tickScheduled = true;\n        utils.delay(this._tickAndRepeat, [], this);\n    }\n    return true;\n};\n\n/**\n * Trigger a tick a schedule an other call to this function.\n */\nDataWorker.prototype._tickAndRepeat = function() {\n    this._tickScheduled = false;\n    if(this.isPaused || this.isFinished) {\n        return;\n    }\n    this._tick();\n    if(!this.isFinished) {\n        utils.delay(this._tickAndRepeat, [], this);\n        this._tickScheduled = true;\n    }\n};\n\n/**\n * Read and push a chunk.\n */\nDataWorker.prototype._tick = function() {\n\n    if(this.isPaused || this.isFinished) {\n        return false;\n    }\n\n    var size = DEFAULT_BLOCK_SIZE;\n    var data = null, nextIndex = Math.min(this.max, this.index + size);\n    if (this.index >= this.max) {\n        // EOF\n        return this.end();\n    } else {\n        switch(this.type) {\n        case \"string\":\n            data = this.data.substring(this.index, nextIndex);\n            break;\n        case \"uint8array\":\n            data = this.data.subarray(this.index, nextIndex);\n            break;\n        case \"array\":\n        case \"nodebuffer\":\n            data = this.data.slice(this.index, nextIndex);\n            break;\n        }\n        this.index = nextIndex;\n        return this.push({\n            data : data,\n            meta : {\n                percent : this.max ? this.index / this.max * 100 : 0\n            }\n        });\n    }\n};\n\nmodule.exports = DataWorker;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pzemlwQDMuMTAuMS9ub2RlX21vZHVsZXMvanN6aXAvbGliL3N0cmVhbS9EYXRhV29ya2VyLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLFlBQVksbUJBQU8sQ0FBQywwRkFBVTtBQUM5QixvQkFBb0IsbUJBQU8sQ0FBQyxnSEFBaUI7O0FBRTdDO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSxLQUFLO0FBQ0w7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkQ6XFxweWNvZGVcXHN1cHBvcnRfY2hhcnQyXFxub2RlX21vZHVsZXNcXC5wbnBtXFxqc3ppcEAzLjEwLjFcXG5vZGVfbW9kdWxlc1xcanN6aXBcXGxpYlxcc3RyZWFtXFxEYXRhV29ya2VyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG52YXIgdXRpbHMgPSByZXF1aXJlKFwiLi4vdXRpbHNcIik7XG52YXIgR2VuZXJpY1dvcmtlciA9IHJlcXVpcmUoXCIuL0dlbmVyaWNXb3JrZXJcIik7XG5cbi8vIHRoZSBzaXplIG9mIHRoZSBnZW5lcmF0ZWQgY2h1bmtzXG4vLyBUT0RPIGV4cG9zZSB0aGlzIGFzIGEgcHVibGljIHZhcmlhYmxlXG52YXIgREVGQVVMVF9CTE9DS19TSVpFID0gMTYgKiAxMDI0O1xuXG4vKipcbiAqIEEgd29ya2VyIHRoYXQgcmVhZHMgYSBjb250ZW50IGFuZCBlbWl0cyBjaHVua3MuXG4gKiBAY29uc3RydWN0b3JcbiAqIEBwYXJhbSB7UHJvbWlzZX0gZGF0YVAgdGhlIHByb21pc2Ugb2YgdGhlIGRhdGEgdG8gc3BsaXRcbiAqL1xuZnVuY3Rpb24gRGF0YVdvcmtlcihkYXRhUCkge1xuICAgIEdlbmVyaWNXb3JrZXIuY2FsbCh0aGlzLCBcIkRhdGFXb3JrZXJcIik7XG4gICAgdmFyIHNlbGYgPSB0aGlzO1xuICAgIHRoaXMuZGF0YUlzUmVhZHkgPSBmYWxzZTtcbiAgICB0aGlzLmluZGV4ID0gMDtcbiAgICB0aGlzLm1heCA9IDA7XG4gICAgdGhpcy5kYXRhID0gbnVsbDtcbiAgICB0aGlzLnR5cGUgPSBcIlwiO1xuXG4gICAgdGhpcy5fdGlja1NjaGVkdWxlZCA9IGZhbHNlO1xuXG4gICAgZGF0YVAudGhlbihmdW5jdGlvbiAoZGF0YSkge1xuICAgICAgICBzZWxmLmRhdGFJc1JlYWR5ID0gdHJ1ZTtcbiAgICAgICAgc2VsZi5kYXRhID0gZGF0YTtcbiAgICAgICAgc2VsZi5tYXggPSBkYXRhICYmIGRhdGEubGVuZ3RoIHx8IDA7XG4gICAgICAgIHNlbGYudHlwZSA9IHV0aWxzLmdldFR5cGVPZihkYXRhKTtcbiAgICAgICAgaWYoIXNlbGYuaXNQYXVzZWQpIHtcbiAgICAgICAgICAgIHNlbGYuX3RpY2tBbmRSZXBlYXQoKTtcbiAgICAgICAgfVxuICAgIH0sIGZ1bmN0aW9uIChlKSB7XG4gICAgICAgIHNlbGYuZXJyb3IoZSk7XG4gICAgfSk7XG59XG5cbnV0aWxzLmluaGVyaXRzKERhdGFXb3JrZXIsIEdlbmVyaWNXb3JrZXIpO1xuXG4vKipcbiAqIEBzZWUgR2VuZXJpY1dvcmtlci5jbGVhblVwXG4gKi9cbkRhdGFXb3JrZXIucHJvdG90eXBlLmNsZWFuVXAgPSBmdW5jdGlvbiAoKSB7XG4gICAgR2VuZXJpY1dvcmtlci5wcm90b3R5cGUuY2xlYW5VcC5jYWxsKHRoaXMpO1xuICAgIHRoaXMuZGF0YSA9IG51bGw7XG59O1xuXG4vKipcbiAqIEBzZWUgR2VuZXJpY1dvcmtlci5yZXN1bWVcbiAqL1xuRGF0YVdvcmtlci5wcm90b3R5cGUucmVzdW1lID0gZnVuY3Rpb24gKCkge1xuICAgIGlmKCFHZW5lcmljV29ya2VyLnByb3RvdHlwZS5yZXN1bWUuY2FsbCh0aGlzKSkge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuXG4gICAgaWYgKCF0aGlzLl90aWNrU2NoZWR1bGVkICYmIHRoaXMuZGF0YUlzUmVhZHkpIHtcbiAgICAgICAgdGhpcy5fdGlja1NjaGVkdWxlZCA9IHRydWU7XG4gICAgICAgIHV0aWxzLmRlbGF5KHRoaXMuX3RpY2tBbmRSZXBlYXQsIFtdLCB0aGlzKTtcbiAgICB9XG4gICAgcmV0dXJuIHRydWU7XG59O1xuXG4vKipcbiAqIFRyaWdnZXIgYSB0aWNrIGEgc2NoZWR1bGUgYW4gb3RoZXIgY2FsbCB0byB0aGlzIGZ1bmN0aW9uLlxuICovXG5EYXRhV29ya2VyLnByb3RvdHlwZS5fdGlja0FuZFJlcGVhdCA9IGZ1bmN0aW9uKCkge1xuICAgIHRoaXMuX3RpY2tTY2hlZHVsZWQgPSBmYWxzZTtcbiAgICBpZih0aGlzLmlzUGF1c2VkIHx8IHRoaXMuaXNGaW5pc2hlZCkge1xuICAgICAgICByZXR1cm47XG4gICAgfVxuICAgIHRoaXMuX3RpY2soKTtcbiAgICBpZighdGhpcy5pc0ZpbmlzaGVkKSB7XG4gICAgICAgIHV0aWxzLmRlbGF5KHRoaXMuX3RpY2tBbmRSZXBlYXQsIFtdLCB0aGlzKTtcbiAgICAgICAgdGhpcy5fdGlja1NjaGVkdWxlZCA9IHRydWU7XG4gICAgfVxufTtcblxuLyoqXG4gKiBSZWFkIGFuZCBwdXNoIGEgY2h1bmsuXG4gKi9cbkRhdGFXb3JrZXIucHJvdG90eXBlLl90aWNrID0gZnVuY3Rpb24oKSB7XG5cbiAgICBpZih0aGlzLmlzUGF1c2VkIHx8IHRoaXMuaXNGaW5pc2hlZCkge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuXG4gICAgdmFyIHNpemUgPSBERUZBVUxUX0JMT0NLX1NJWkU7XG4gICAgdmFyIGRhdGEgPSBudWxsLCBuZXh0SW5kZXggPSBNYXRoLm1pbih0aGlzLm1heCwgdGhpcy5pbmRleCArIHNpemUpO1xuICAgIGlmICh0aGlzLmluZGV4ID49IHRoaXMubWF4KSB7XG4gICAgICAgIC8vIEVPRlxuICAgICAgICByZXR1cm4gdGhpcy5lbmQoKTtcbiAgICB9IGVsc2Uge1xuICAgICAgICBzd2l0Y2godGhpcy50eXBlKSB7XG4gICAgICAgIGNhc2UgXCJzdHJpbmdcIjpcbiAgICAgICAgICAgIGRhdGEgPSB0aGlzLmRhdGEuc3Vic3RyaW5nKHRoaXMuaW5kZXgsIG5leHRJbmRleCk7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSBcInVpbnQ4YXJyYXlcIjpcbiAgICAgICAgICAgIGRhdGEgPSB0aGlzLmRhdGEuc3ViYXJyYXkodGhpcy5pbmRleCwgbmV4dEluZGV4KTtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIFwiYXJyYXlcIjpcbiAgICAgICAgY2FzZSBcIm5vZGVidWZmZXJcIjpcbiAgICAgICAgICAgIGRhdGEgPSB0aGlzLmRhdGEuc2xpY2UodGhpcy5pbmRleCwgbmV4dEluZGV4KTtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuaW5kZXggPSBuZXh0SW5kZXg7XG4gICAgICAgIHJldHVybiB0aGlzLnB1c2goe1xuICAgICAgICAgICAgZGF0YSA6IGRhdGEsXG4gICAgICAgICAgICBtZXRhIDoge1xuICAgICAgICAgICAgICAgIHBlcmNlbnQgOiB0aGlzLm1heCA/IHRoaXMuaW5kZXggLyB0aGlzLm1heCAqIDEwMCA6IDBcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgfVxufTtcblxubW9kdWxlLmV4cG9ydHMgPSBEYXRhV29ya2VyO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/DataWorker.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js":
/*!*****************************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js ***!
  \*****************************************************************************************/
/***/ ((module) => {

eval("\n\n/**\n * A worker that does nothing but passing chunks to the next one. This is like\n * a nodejs stream but with some differences. On the good side :\n * - it works on IE 6-9 without any issue / polyfill\n * - it weights less than the full dependencies bundled with browserify\n * - it forwards errors (no need to declare an error handler EVERYWHERE)\n *\n * A chunk is an object with 2 attributes : `meta` and `data`. The former is an\n * object containing anything (`percent` for example), see each worker for more\n * details. The latter is the real data (String, Uint8Array, etc).\n *\n * @constructor\n * @param {String} name the name of the stream (mainly used for debugging purposes)\n */\nfunction GenericWorker(name) {\n    // the name of the worker\n    this.name = name || \"default\";\n    // an object containing metadata about the workers chain\n    this.streamInfo = {};\n    // an error which happened when the worker was paused\n    this.generatedError = null;\n    // an object containing metadata to be merged by this worker into the general metadata\n    this.extraStreamInfo = {};\n    // true if the stream is paused (and should not do anything), false otherwise\n    this.isPaused = true;\n    // true if the stream is finished (and should not do anything), false otherwise\n    this.isFinished = false;\n    // true if the stream is locked to prevent further structure updates (pipe), false otherwise\n    this.isLocked = false;\n    // the event listeners\n    this._listeners = {\n        \"data\":[],\n        \"end\":[],\n        \"error\":[]\n    };\n    // the previous worker, if any\n    this.previous = null;\n}\n\nGenericWorker.prototype = {\n    /**\n     * Push a chunk to the next workers.\n     * @param {Object} chunk the chunk to push\n     */\n    push : function (chunk) {\n        this.emit(\"data\", chunk);\n    },\n    /**\n     * End the stream.\n     * @return {Boolean} true if this call ended the worker, false otherwise.\n     */\n    end : function () {\n        if (this.isFinished) {\n            return false;\n        }\n\n        this.flush();\n        try {\n            this.emit(\"end\");\n            this.cleanUp();\n            this.isFinished = true;\n        } catch (e) {\n            this.emit(\"error\", e);\n        }\n        return true;\n    },\n    /**\n     * End the stream with an error.\n     * @param {Error} e the error which caused the premature end.\n     * @return {Boolean} true if this call ended the worker with an error, false otherwise.\n     */\n    error : function (e) {\n        if (this.isFinished) {\n            return false;\n        }\n\n        if(this.isPaused) {\n            this.generatedError = e;\n        } else {\n            this.isFinished = true;\n\n            this.emit(\"error\", e);\n\n            // in the workers chain exploded in the middle of the chain,\n            // the error event will go downward but we also need to notify\n            // workers upward that there has been an error.\n            if(this.previous) {\n                this.previous.error(e);\n            }\n\n            this.cleanUp();\n        }\n        return true;\n    },\n    /**\n     * Add a callback on an event.\n     * @param {String} name the name of the event (data, end, error)\n     * @param {Function} listener the function to call when the event is triggered\n     * @return {GenericWorker} the current object for chainability\n     */\n    on : function (name, listener) {\n        this._listeners[name].push(listener);\n        return this;\n    },\n    /**\n     * Clean any references when a worker is ending.\n     */\n    cleanUp : function () {\n        this.streamInfo = this.generatedError = this.extraStreamInfo = null;\n        this._listeners = [];\n    },\n    /**\n     * Trigger an event. This will call registered callback with the provided arg.\n     * @param {String} name the name of the event (data, end, error)\n     * @param {Object} arg the argument to call the callback with.\n     */\n    emit : function (name, arg) {\n        if (this._listeners[name]) {\n            for(var i = 0; i < this._listeners[name].length; i++) {\n                this._listeners[name][i].call(this, arg);\n            }\n        }\n    },\n    /**\n     * Chain a worker with an other.\n     * @param {Worker} next the worker receiving events from the current one.\n     * @return {worker} the next worker for chainability\n     */\n    pipe : function (next) {\n        return next.registerPrevious(this);\n    },\n    /**\n     * Same as `pipe` in the other direction.\n     * Using an API with `pipe(next)` is very easy.\n     * Implementing the API with the point of view of the next one registering\n     * a source is easier, see the ZipFileWorker.\n     * @param {Worker} previous the previous worker, sending events to this one\n     * @return {Worker} the current worker for chainability\n     */\n    registerPrevious : function (previous) {\n        if (this.isLocked) {\n            throw new Error(\"The stream '\" + this + \"' has already been used.\");\n        }\n\n        // sharing the streamInfo...\n        this.streamInfo = previous.streamInfo;\n        // ... and adding our own bits\n        this.mergeStreamInfo();\n        this.previous =  previous;\n        var self = this;\n        previous.on(\"data\", function (chunk) {\n            self.processChunk(chunk);\n        });\n        previous.on(\"end\", function () {\n            self.end();\n        });\n        previous.on(\"error\", function (e) {\n            self.error(e);\n        });\n        return this;\n    },\n    /**\n     * Pause the stream so it doesn't send events anymore.\n     * @return {Boolean} true if this call paused the worker, false otherwise.\n     */\n    pause : function () {\n        if(this.isPaused || this.isFinished) {\n            return false;\n        }\n        this.isPaused = true;\n\n        if(this.previous) {\n            this.previous.pause();\n        }\n        return true;\n    },\n    /**\n     * Resume a paused stream.\n     * @return {Boolean} true if this call resumed the worker, false otherwise.\n     */\n    resume : function () {\n        if(!this.isPaused || this.isFinished) {\n            return false;\n        }\n        this.isPaused = false;\n\n        // if true, the worker tried to resume but failed\n        var withError = false;\n        if(this.generatedError) {\n            this.error(this.generatedError);\n            withError = true;\n        }\n        if(this.previous) {\n            this.previous.resume();\n        }\n\n        return !withError;\n    },\n    /**\n     * Flush any remaining bytes as the stream is ending.\n     */\n    flush : function () {},\n    /**\n     * Process a chunk. This is usually the method overridden.\n     * @param {Object} chunk the chunk to process.\n     */\n    processChunk : function(chunk) {\n        this.push(chunk);\n    },\n    /**\n     * Add a key/value to be added in the workers chain streamInfo once activated.\n     * @param {String} key the key to use\n     * @param {Object} value the associated value\n     * @return {Worker} the current worker for chainability\n     */\n    withStreamInfo : function (key, value) {\n        this.extraStreamInfo[key] = value;\n        this.mergeStreamInfo();\n        return this;\n    },\n    /**\n     * Merge this worker's streamInfo into the chain's streamInfo.\n     */\n    mergeStreamInfo : function () {\n        for(var key in this.extraStreamInfo) {\n            if (!Object.prototype.hasOwnProperty.call(this.extraStreamInfo, key)) {\n                continue;\n            }\n            this.streamInfo[key] = this.extraStreamInfo[key];\n        }\n    },\n\n    /**\n     * Lock the stream to prevent further updates on the workers chain.\n     * After calling this method, all calls to pipe will fail.\n     */\n    lock: function () {\n        if (this.isLocked) {\n            throw new Error(\"The stream '\" + this + \"' has already been used.\");\n        }\n        this.isLocked = true;\n        if (this.previous) {\n            this.previous.lock();\n        }\n    },\n\n    /**\n     *\n     * Pretty print the workers chain.\n     */\n    toString : function () {\n        var me = \"Worker \" + this.name;\n        if (this.previous) {\n            return this.previous + \" -> \" + me;\n        } else {\n            return me;\n        }\n    }\n};\n\nmodule.exports = GenericWorker;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/StreamHelper.js":
/*!****************************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/StreamHelper.js ***!
  \****************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\nvar ConvertWorker = __webpack_require__(/*! ./ConvertWorker */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/ConvertWorker.js\");\nvar GenericWorker = __webpack_require__(/*! ./GenericWorker */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js\");\nvar base64 = __webpack_require__(/*! ../base64 */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/base64.js\");\nvar support = __webpack_require__(/*! ../support */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/support.js\");\nvar external = __webpack_require__(/*! ../external */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/external.js\");\n\nvar NodejsStreamOutputAdapter = null;\nif (support.nodestream) {\n    try {\n        NodejsStreamOutputAdapter = __webpack_require__(/*! ../nodejs/NodejsStreamOutputAdapter */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejs/NodejsStreamOutputAdapter.js\");\n    } catch(e) {\n        // ignore\n    }\n}\n\n/**\n * Apply the final transformation of the data. If the user wants a Blob for\n * example, it's easier to work with an U8intArray and finally do the\n * ArrayBuffer/Blob conversion.\n * @param {String} type the name of the final type\n * @param {String|Uint8Array|Buffer} content the content to transform\n * @param {String} mimeType the mime type of the content, if applicable.\n * @return {String|Uint8Array|ArrayBuffer|Buffer|Blob} the content in the right format.\n */\nfunction transformZipOutput(type, content, mimeType) {\n    switch(type) {\n    case \"blob\" :\n        return utils.newBlob(utils.transformTo(\"arraybuffer\", content), mimeType);\n    case \"base64\" :\n        return base64.encode(content);\n    default :\n        return utils.transformTo(type, content);\n    }\n}\n\n/**\n * Concatenate an array of data of the given type.\n * @param {String} type the type of the data in the given array.\n * @param {Array} dataArray the array containing the data chunks to concatenate\n * @return {String|Uint8Array|Buffer} the concatenated data\n * @throws Error if the asked type is unsupported\n */\nfunction concat (type, dataArray) {\n    var i, index = 0, res = null, totalLength = 0;\n    for(i = 0; i < dataArray.length; i++) {\n        totalLength += dataArray[i].length;\n    }\n    switch(type) {\n    case \"string\":\n        return dataArray.join(\"\");\n    case \"array\":\n        return Array.prototype.concat.apply([], dataArray);\n    case \"uint8array\":\n        res = new Uint8Array(totalLength);\n        for(i = 0; i < dataArray.length; i++) {\n            res.set(dataArray[i], index);\n            index += dataArray[i].length;\n        }\n        return res;\n    case \"nodebuffer\":\n        return Buffer.concat(dataArray);\n    default:\n        throw new Error(\"concat : unsupported type '\"  + type + \"'\");\n    }\n}\n\n/**\n * Listen a StreamHelper, accumulate its content and concatenate it into a\n * complete block.\n * @param {StreamHelper} helper the helper to use.\n * @param {Function} updateCallback a callback called on each update. Called\n * with one arg :\n * - the metadata linked to the update received.\n * @return Promise the promise for the accumulation.\n */\nfunction accumulate(helper, updateCallback) {\n    return new external.Promise(function (resolve, reject){\n        var dataArray = [];\n        var chunkType = helper._internalType,\n            resultType = helper._outputType,\n            mimeType = helper._mimeType;\n        helper\n            .on(\"data\", function (data, meta) {\n                dataArray.push(data);\n                if(updateCallback) {\n                    updateCallback(meta);\n                }\n            })\n            .on(\"error\", function(err) {\n                dataArray = [];\n                reject(err);\n            })\n            .on(\"end\", function (){\n                try {\n                    var result = transformZipOutput(resultType, concat(chunkType, dataArray), mimeType);\n                    resolve(result);\n                } catch (e) {\n                    reject(e);\n                }\n                dataArray = [];\n            })\n            .resume();\n    });\n}\n\n/**\n * An helper to easily use workers outside of JSZip.\n * @constructor\n * @param {Worker} worker the worker to wrap\n * @param {String} outputType the type of data expected by the use\n * @param {String} mimeType the mime type of the content, if applicable.\n */\nfunction StreamHelper(worker, outputType, mimeType) {\n    var internalType = outputType;\n    switch(outputType) {\n    case \"blob\":\n    case \"arraybuffer\":\n        internalType = \"uint8array\";\n        break;\n    case \"base64\":\n        internalType = \"string\";\n        break;\n    }\n\n    try {\n        // the type used internally\n        this._internalType = internalType;\n        // the type used to output results\n        this._outputType = outputType;\n        // the mime type\n        this._mimeType = mimeType;\n        utils.checkSupport(internalType);\n        this._worker = worker.pipe(new ConvertWorker(internalType));\n        // the last workers can be rewired without issues but we need to\n        // prevent any updates on previous workers.\n        worker.lock();\n    } catch(e) {\n        this._worker = new GenericWorker(\"error\");\n        this._worker.error(e);\n    }\n}\n\nStreamHelper.prototype = {\n    /**\n     * Listen a StreamHelper, accumulate its content and concatenate it into a\n     * complete block.\n     * @param {Function} updateCb the update callback.\n     * @return Promise the promise for the accumulation.\n     */\n    accumulate : function (updateCb) {\n        return accumulate(this, updateCb);\n    },\n    /**\n     * Add a listener on an event triggered on a stream.\n     * @param {String} evt the name of the event\n     * @param {Function} fn the listener\n     * @return {StreamHelper} the current helper.\n     */\n    on : function (evt, fn) {\n        var self = this;\n\n        if(evt === \"data\") {\n            this._worker.on(evt, function (chunk) {\n                fn.call(self, chunk.data, chunk.meta);\n            });\n        } else {\n            this._worker.on(evt, function () {\n                utils.delay(fn, arguments, self);\n            });\n        }\n        return this;\n    },\n    /**\n     * Resume the flow of chunks.\n     * @return {StreamHelper} the current helper.\n     */\n    resume : function () {\n        utils.delay(this._worker.resume, [], this._worker);\n        return this;\n    },\n    /**\n     * Pause the flow of chunks.\n     * @return {StreamHelper} the current helper.\n     */\n    pause : function () {\n        this._worker.pause();\n        return this;\n    },\n    /**\n     * Return a nodejs stream for this helper.\n     * @param {Function} updateCb the update callback.\n     * @return {NodejsStreamOutputAdapter} the nodejs stream.\n     */\n    toNodejsStream : function (updateCb) {\n        utils.checkSupport(\"nodestream\");\n        if (this._outputType !== \"nodebuffer\") {\n            // an object stream containing blob/arraybuffer/uint8array/string\n            // is strange and I don't know if it would be useful.\n            // I you find this comment and have a good usecase, please open a\n            // bug report !\n            throw new Error(this._outputType + \" is not supported by this method\");\n        }\n\n        return new NodejsStreamOutputAdapter(this, {\n            objectMode : this._outputType !== \"nodebuffer\"\n        }, updateCb);\n    }\n};\n\n\nmodule.exports = StreamHelper;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pzemlwQDMuMTAuMS9ub2RlX21vZHVsZXMvanN6aXAvbGliL3N0cmVhbS9TdHJlYW1IZWxwZXIuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsWUFBWSxtQkFBTyxDQUFDLDBGQUFVO0FBQzlCLG9CQUFvQixtQkFBTyxDQUFDLGdIQUFpQjtBQUM3QyxvQkFBb0IsbUJBQU8sQ0FBQyxnSEFBaUI7QUFDN0MsYUFBYSxtQkFBTyxDQUFDLDRGQUFXO0FBQ2hDLGNBQWMsbUJBQU8sQ0FBQyw4RkFBWTtBQUNsQyxlQUFlLG1CQUFPLENBQUMsZ0dBQWE7O0FBRXBDO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxtQkFBTyxDQUFDLGdKQUFxQztBQUNqRixNQUFNO0FBQ047QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsMEJBQTBCO0FBQ3JDLFdBQVcsUUFBUTtBQUNuQixZQUFZLDJDQUEyQztBQUN2RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxPQUFPO0FBQ2xCLFlBQVksMEJBQTBCO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxzQkFBc0I7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLHNCQUFzQjtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLGNBQWM7QUFDekIsV0FBVyxVQUFVO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQSxLQUFLO0FBQ0w7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsUUFBUTtBQUNuQixXQUFXLFFBQVE7QUFDbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsVUFBVTtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsVUFBVTtBQUN6QixnQkFBZ0IsY0FBYztBQUM5QjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLFVBQVU7QUFDVjtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLGdCQUFnQixjQUFjO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxnQkFBZ0IsY0FBYztBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsZUFBZSxVQUFVO0FBQ3pCLGdCQUFnQiwyQkFBMkI7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBOzs7QUFHQSIsInNvdXJjZXMiOlsiRDpcXHB5Y29kZVxcc3VwcG9ydF9jaGFydDJcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGpzemlwQDMuMTAuMVxcbm9kZV9tb2R1bGVzXFxqc3ppcFxcbGliXFxzdHJlYW1cXFN0cmVhbUhlbHBlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxudmFyIHV0aWxzID0gcmVxdWlyZShcIi4uL3V0aWxzXCIpO1xudmFyIENvbnZlcnRXb3JrZXIgPSByZXF1aXJlKFwiLi9Db252ZXJ0V29ya2VyXCIpO1xudmFyIEdlbmVyaWNXb3JrZXIgPSByZXF1aXJlKFwiLi9HZW5lcmljV29ya2VyXCIpO1xudmFyIGJhc2U2NCA9IHJlcXVpcmUoXCIuLi9iYXNlNjRcIik7XG52YXIgc3VwcG9ydCA9IHJlcXVpcmUoXCIuLi9zdXBwb3J0XCIpO1xudmFyIGV4dGVybmFsID0gcmVxdWlyZShcIi4uL2V4dGVybmFsXCIpO1xuXG52YXIgTm9kZWpzU3RyZWFtT3V0cHV0QWRhcHRlciA9IG51bGw7XG5pZiAoc3VwcG9ydC5ub2Rlc3RyZWFtKSB7XG4gICAgdHJ5IHtcbiAgICAgICAgTm9kZWpzU3RyZWFtT3V0cHV0QWRhcHRlciA9IHJlcXVpcmUoXCIuLi9ub2RlanMvTm9kZWpzU3RyZWFtT3V0cHV0QWRhcHRlclwiKTtcbiAgICB9IGNhdGNoKGUpIHtcbiAgICAgICAgLy8gaWdub3JlXG4gICAgfVxufVxuXG4vKipcbiAqIEFwcGx5IHRoZSBmaW5hbCB0cmFuc2Zvcm1hdGlvbiBvZiB0aGUgZGF0YS4gSWYgdGhlIHVzZXIgd2FudHMgYSBCbG9iIGZvclxuICogZXhhbXBsZSwgaXQncyBlYXNpZXIgdG8gd29yayB3aXRoIGFuIFU4aW50QXJyYXkgYW5kIGZpbmFsbHkgZG8gdGhlXG4gKiBBcnJheUJ1ZmZlci9CbG9iIGNvbnZlcnNpb24uXG4gKiBAcGFyYW0ge1N0cmluZ30gdHlwZSB0aGUgbmFtZSBvZiB0aGUgZmluYWwgdHlwZVxuICogQHBhcmFtIHtTdHJpbmd8VWludDhBcnJheXxCdWZmZXJ9IGNvbnRlbnQgdGhlIGNvbnRlbnQgdG8gdHJhbnNmb3JtXG4gKiBAcGFyYW0ge1N0cmluZ30gbWltZVR5cGUgdGhlIG1pbWUgdHlwZSBvZiB0aGUgY29udGVudCwgaWYgYXBwbGljYWJsZS5cbiAqIEByZXR1cm4ge1N0cmluZ3xVaW50OEFycmF5fEFycmF5QnVmZmVyfEJ1ZmZlcnxCbG9ifSB0aGUgY29udGVudCBpbiB0aGUgcmlnaHQgZm9ybWF0LlxuICovXG5mdW5jdGlvbiB0cmFuc2Zvcm1aaXBPdXRwdXQodHlwZSwgY29udGVudCwgbWltZVR5cGUpIHtcbiAgICBzd2l0Y2godHlwZSkge1xuICAgIGNhc2UgXCJibG9iXCIgOlxuICAgICAgICByZXR1cm4gdXRpbHMubmV3QmxvYih1dGlscy50cmFuc2Zvcm1UbyhcImFycmF5YnVmZmVyXCIsIGNvbnRlbnQpLCBtaW1lVHlwZSk7XG4gICAgY2FzZSBcImJhc2U2NFwiIDpcbiAgICAgICAgcmV0dXJuIGJhc2U2NC5lbmNvZGUoY29udGVudCk7XG4gICAgZGVmYXVsdCA6XG4gICAgICAgIHJldHVybiB1dGlscy50cmFuc2Zvcm1Ubyh0eXBlLCBjb250ZW50KTtcbiAgICB9XG59XG5cbi8qKlxuICogQ29uY2F0ZW5hdGUgYW4gYXJyYXkgb2YgZGF0YSBvZiB0aGUgZ2l2ZW4gdHlwZS5cbiAqIEBwYXJhbSB7U3RyaW5nfSB0eXBlIHRoZSB0eXBlIG9mIHRoZSBkYXRhIGluIHRoZSBnaXZlbiBhcnJheS5cbiAqIEBwYXJhbSB7QXJyYXl9IGRhdGFBcnJheSB0aGUgYXJyYXkgY29udGFpbmluZyB0aGUgZGF0YSBjaHVua3MgdG8gY29uY2F0ZW5hdGVcbiAqIEByZXR1cm4ge1N0cmluZ3xVaW50OEFycmF5fEJ1ZmZlcn0gdGhlIGNvbmNhdGVuYXRlZCBkYXRhXG4gKiBAdGhyb3dzIEVycm9yIGlmIHRoZSBhc2tlZCB0eXBlIGlzIHVuc3VwcG9ydGVkXG4gKi9cbmZ1bmN0aW9uIGNvbmNhdCAodHlwZSwgZGF0YUFycmF5KSB7XG4gICAgdmFyIGksIGluZGV4ID0gMCwgcmVzID0gbnVsbCwgdG90YWxMZW5ndGggPSAwO1xuICAgIGZvcihpID0gMDsgaSA8IGRhdGFBcnJheS5sZW5ndGg7IGkrKykge1xuICAgICAgICB0b3RhbExlbmd0aCArPSBkYXRhQXJyYXlbaV0ubGVuZ3RoO1xuICAgIH1cbiAgICBzd2l0Y2godHlwZSkge1xuICAgIGNhc2UgXCJzdHJpbmdcIjpcbiAgICAgICAgcmV0dXJuIGRhdGFBcnJheS5qb2luKFwiXCIpO1xuICAgIGNhc2UgXCJhcnJheVwiOlxuICAgICAgICByZXR1cm4gQXJyYXkucHJvdG90eXBlLmNvbmNhdC5hcHBseShbXSwgZGF0YUFycmF5KTtcbiAgICBjYXNlIFwidWludDhhcnJheVwiOlxuICAgICAgICByZXMgPSBuZXcgVWludDhBcnJheSh0b3RhbExlbmd0aCk7XG4gICAgICAgIGZvcihpID0gMDsgaSA8IGRhdGFBcnJheS5sZW5ndGg7IGkrKykge1xuICAgICAgICAgICAgcmVzLnNldChkYXRhQXJyYXlbaV0sIGluZGV4KTtcbiAgICAgICAgICAgIGluZGV4ICs9IGRhdGFBcnJheVtpXS5sZW5ndGg7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHJlcztcbiAgICBjYXNlIFwibm9kZWJ1ZmZlclwiOlxuICAgICAgICByZXR1cm4gQnVmZmVyLmNvbmNhdChkYXRhQXJyYXkpO1xuICAgIGRlZmF1bHQ6XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcImNvbmNhdCA6IHVuc3VwcG9ydGVkIHR5cGUgJ1wiICArIHR5cGUgKyBcIidcIik7XG4gICAgfVxufVxuXG4vKipcbiAqIExpc3RlbiBhIFN0cmVhbUhlbHBlciwgYWNjdW11bGF0ZSBpdHMgY29udGVudCBhbmQgY29uY2F0ZW5hdGUgaXQgaW50byBhXG4gKiBjb21wbGV0ZSBibG9jay5cbiAqIEBwYXJhbSB7U3RyZWFtSGVscGVyfSBoZWxwZXIgdGhlIGhlbHBlciB0byB1c2UuXG4gKiBAcGFyYW0ge0Z1bmN0aW9ufSB1cGRhdGVDYWxsYmFjayBhIGNhbGxiYWNrIGNhbGxlZCBvbiBlYWNoIHVwZGF0ZS4gQ2FsbGVkXG4gKiB3aXRoIG9uZSBhcmcgOlxuICogLSB0aGUgbWV0YWRhdGEgbGlua2VkIHRvIHRoZSB1cGRhdGUgcmVjZWl2ZWQuXG4gKiBAcmV0dXJuIFByb21pc2UgdGhlIHByb21pc2UgZm9yIHRoZSBhY2N1bXVsYXRpb24uXG4gKi9cbmZ1bmN0aW9uIGFjY3VtdWxhdGUoaGVscGVyLCB1cGRhdGVDYWxsYmFjaykge1xuICAgIHJldHVybiBuZXcgZXh0ZXJuYWwuUHJvbWlzZShmdW5jdGlvbiAocmVzb2x2ZSwgcmVqZWN0KXtcbiAgICAgICAgdmFyIGRhdGFBcnJheSA9IFtdO1xuICAgICAgICB2YXIgY2h1bmtUeXBlID0gaGVscGVyLl9pbnRlcm5hbFR5cGUsXG4gICAgICAgICAgICByZXN1bHRUeXBlID0gaGVscGVyLl9vdXRwdXRUeXBlLFxuICAgICAgICAgICAgbWltZVR5cGUgPSBoZWxwZXIuX21pbWVUeXBlO1xuICAgICAgICBoZWxwZXJcbiAgICAgICAgICAgIC5vbihcImRhdGFcIiwgZnVuY3Rpb24gKGRhdGEsIG1ldGEpIHtcbiAgICAgICAgICAgICAgICBkYXRhQXJyYXkucHVzaChkYXRhKTtcbiAgICAgICAgICAgICAgICBpZih1cGRhdGVDYWxsYmFjaykge1xuICAgICAgICAgICAgICAgICAgICB1cGRhdGVDYWxsYmFjayhtZXRhKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KVxuICAgICAgICAgICAgLm9uKFwiZXJyb3JcIiwgZnVuY3Rpb24oZXJyKSB7XG4gICAgICAgICAgICAgICAgZGF0YUFycmF5ID0gW107XG4gICAgICAgICAgICAgICAgcmVqZWN0KGVycik7XG4gICAgICAgICAgICB9KVxuICAgICAgICAgICAgLm9uKFwiZW5kXCIsIGZ1bmN0aW9uICgpe1xuICAgICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgICAgIHZhciByZXN1bHQgPSB0cmFuc2Zvcm1aaXBPdXRwdXQocmVzdWx0VHlwZSwgY29uY2F0KGNodW5rVHlwZSwgZGF0YUFycmF5KSwgbWltZVR5cGUpO1xuICAgICAgICAgICAgICAgICAgICByZXNvbHZlKHJlc3VsdCk7XG4gICAgICAgICAgICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICAgICAgICAgICAgICByZWplY3QoZSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGRhdGFBcnJheSA9IFtdO1xuICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIC5yZXN1bWUoKTtcbiAgICB9KTtcbn1cblxuLyoqXG4gKiBBbiBoZWxwZXIgdG8gZWFzaWx5IHVzZSB3b3JrZXJzIG91dHNpZGUgb2YgSlNaaXAuXG4gKiBAY29uc3RydWN0b3JcbiAqIEBwYXJhbSB7V29ya2VyfSB3b3JrZXIgdGhlIHdvcmtlciB0byB3cmFwXG4gKiBAcGFyYW0ge1N0cmluZ30gb3V0cHV0VHlwZSB0aGUgdHlwZSBvZiBkYXRhIGV4cGVjdGVkIGJ5IHRoZSB1c2VcbiAqIEBwYXJhbSB7U3RyaW5nfSBtaW1lVHlwZSB0aGUgbWltZSB0eXBlIG9mIHRoZSBjb250ZW50LCBpZiBhcHBsaWNhYmxlLlxuICovXG5mdW5jdGlvbiBTdHJlYW1IZWxwZXIod29ya2VyLCBvdXRwdXRUeXBlLCBtaW1lVHlwZSkge1xuICAgIHZhciBpbnRlcm5hbFR5cGUgPSBvdXRwdXRUeXBlO1xuICAgIHN3aXRjaChvdXRwdXRUeXBlKSB7XG4gICAgY2FzZSBcImJsb2JcIjpcbiAgICBjYXNlIFwiYXJyYXlidWZmZXJcIjpcbiAgICAgICAgaW50ZXJuYWxUeXBlID0gXCJ1aW50OGFycmF5XCI7XG4gICAgICAgIGJyZWFrO1xuICAgIGNhc2UgXCJiYXNlNjRcIjpcbiAgICAgICAgaW50ZXJuYWxUeXBlID0gXCJzdHJpbmdcIjtcbiAgICAgICAgYnJlYWs7XG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgICAgLy8gdGhlIHR5cGUgdXNlZCBpbnRlcm5hbGx5XG4gICAgICAgIHRoaXMuX2ludGVybmFsVHlwZSA9IGludGVybmFsVHlwZTtcbiAgICAgICAgLy8gdGhlIHR5cGUgdXNlZCB0byBvdXRwdXQgcmVzdWx0c1xuICAgICAgICB0aGlzLl9vdXRwdXRUeXBlID0gb3V0cHV0VHlwZTtcbiAgICAgICAgLy8gdGhlIG1pbWUgdHlwZVxuICAgICAgICB0aGlzLl9taW1lVHlwZSA9IG1pbWVUeXBlO1xuICAgICAgICB1dGlscy5jaGVja1N1cHBvcnQoaW50ZXJuYWxUeXBlKTtcbiAgICAgICAgdGhpcy5fd29ya2VyID0gd29ya2VyLnBpcGUobmV3IENvbnZlcnRXb3JrZXIoaW50ZXJuYWxUeXBlKSk7XG4gICAgICAgIC8vIHRoZSBsYXN0IHdvcmtlcnMgY2FuIGJlIHJld2lyZWQgd2l0aG91dCBpc3N1ZXMgYnV0IHdlIG5lZWQgdG9cbiAgICAgICAgLy8gcHJldmVudCBhbnkgdXBkYXRlcyBvbiBwcmV2aW91cyB3b3JrZXJzLlxuICAgICAgICB3b3JrZXIubG9jaygpO1xuICAgIH0gY2F0Y2goZSkge1xuICAgICAgICB0aGlzLl93b3JrZXIgPSBuZXcgR2VuZXJpY1dvcmtlcihcImVycm9yXCIpO1xuICAgICAgICB0aGlzLl93b3JrZXIuZXJyb3IoZSk7XG4gICAgfVxufVxuXG5TdHJlYW1IZWxwZXIucHJvdG90eXBlID0ge1xuICAgIC8qKlxuICAgICAqIExpc3RlbiBhIFN0cmVhbUhlbHBlciwgYWNjdW11bGF0ZSBpdHMgY29udGVudCBhbmQgY29uY2F0ZW5hdGUgaXQgaW50byBhXG4gICAgICogY29tcGxldGUgYmxvY2suXG4gICAgICogQHBhcmFtIHtGdW5jdGlvbn0gdXBkYXRlQ2IgdGhlIHVwZGF0ZSBjYWxsYmFjay5cbiAgICAgKiBAcmV0dXJuIFByb21pc2UgdGhlIHByb21pc2UgZm9yIHRoZSBhY2N1bXVsYXRpb24uXG4gICAgICovXG4gICAgYWNjdW11bGF0ZSA6IGZ1bmN0aW9uICh1cGRhdGVDYikge1xuICAgICAgICByZXR1cm4gYWNjdW11bGF0ZSh0aGlzLCB1cGRhdGVDYik7XG4gICAgfSxcbiAgICAvKipcbiAgICAgKiBBZGQgYSBsaXN0ZW5lciBvbiBhbiBldmVudCB0cmlnZ2VyZWQgb24gYSBzdHJlYW0uXG4gICAgICogQHBhcmFtIHtTdHJpbmd9IGV2dCB0aGUgbmFtZSBvZiB0aGUgZXZlbnRcbiAgICAgKiBAcGFyYW0ge0Z1bmN0aW9ufSBmbiB0aGUgbGlzdGVuZXJcbiAgICAgKiBAcmV0dXJuIHtTdHJlYW1IZWxwZXJ9IHRoZSBjdXJyZW50IGhlbHBlci5cbiAgICAgKi9cbiAgICBvbiA6IGZ1bmN0aW9uIChldnQsIGZuKSB7XG4gICAgICAgIHZhciBzZWxmID0gdGhpcztcblxuICAgICAgICBpZihldnQgPT09IFwiZGF0YVwiKSB7XG4gICAgICAgICAgICB0aGlzLl93b3JrZXIub24oZXZ0LCBmdW5jdGlvbiAoY2h1bmspIHtcbiAgICAgICAgICAgICAgICBmbi5jYWxsKHNlbGYsIGNodW5rLmRhdGEsIGNodW5rLm1ldGEpO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICB0aGlzLl93b3JrZXIub24oZXZ0LCBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICAgICAgdXRpbHMuZGVsYXkoZm4sIGFyZ3VtZW50cywgc2VsZik7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9LFxuICAgIC8qKlxuICAgICAqIFJlc3VtZSB0aGUgZmxvdyBvZiBjaHVua3MuXG4gICAgICogQHJldHVybiB7U3RyZWFtSGVscGVyfSB0aGUgY3VycmVudCBoZWxwZXIuXG4gICAgICovXG4gICAgcmVzdW1lIDogZnVuY3Rpb24gKCkge1xuICAgICAgICB1dGlscy5kZWxheSh0aGlzLl93b3JrZXIucmVzdW1lLCBbXSwgdGhpcy5fd29ya2VyKTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfSxcbiAgICAvKipcbiAgICAgKiBQYXVzZSB0aGUgZmxvdyBvZiBjaHVua3MuXG4gICAgICogQHJldHVybiB7U3RyZWFtSGVscGVyfSB0aGUgY3VycmVudCBoZWxwZXIuXG4gICAgICovXG4gICAgcGF1c2UgOiBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHRoaXMuX3dvcmtlci5wYXVzZSgpO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9LFxuICAgIC8qKlxuICAgICAqIFJldHVybiBhIG5vZGVqcyBzdHJlYW0gZm9yIHRoaXMgaGVscGVyLlxuICAgICAqIEBwYXJhbSB7RnVuY3Rpb259IHVwZGF0ZUNiIHRoZSB1cGRhdGUgY2FsbGJhY2suXG4gICAgICogQHJldHVybiB7Tm9kZWpzU3RyZWFtT3V0cHV0QWRhcHRlcn0gdGhlIG5vZGVqcyBzdHJlYW0uXG4gICAgICovXG4gICAgdG9Ob2RlanNTdHJlYW0gOiBmdW5jdGlvbiAodXBkYXRlQ2IpIHtcbiAgICAgICAgdXRpbHMuY2hlY2tTdXBwb3J0KFwibm9kZXN0cmVhbVwiKTtcbiAgICAgICAgaWYgKHRoaXMuX291dHB1dFR5cGUgIT09IFwibm9kZWJ1ZmZlclwiKSB7XG4gICAgICAgICAgICAvLyBhbiBvYmplY3Qgc3RyZWFtIGNvbnRhaW5pbmcgYmxvYi9hcnJheWJ1ZmZlci91aW50OGFycmF5L3N0cmluZ1xuICAgICAgICAgICAgLy8gaXMgc3RyYW5nZSBhbmQgSSBkb24ndCBrbm93IGlmIGl0IHdvdWxkIGJlIHVzZWZ1bC5cbiAgICAgICAgICAgIC8vIEkgeW91IGZpbmQgdGhpcyBjb21tZW50IGFuZCBoYXZlIGEgZ29vZCB1c2VjYXNlLCBwbGVhc2Ugb3BlbiBhXG4gICAgICAgICAgICAvLyBidWcgcmVwb3J0ICFcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcih0aGlzLl9vdXRwdXRUeXBlICsgXCIgaXMgbm90IHN1cHBvcnRlZCBieSB0aGlzIG1ldGhvZFwiKTtcbiAgICAgICAgfVxuXG4gICAgICAgIHJldHVybiBuZXcgTm9kZWpzU3RyZWFtT3V0cHV0QWRhcHRlcih0aGlzLCB7XG4gICAgICAgICAgICBvYmplY3RNb2RlIDogdGhpcy5fb3V0cHV0VHlwZSAhPT0gXCJub2RlYnVmZmVyXCJcbiAgICAgICAgfSwgdXBkYXRlQ2IpO1xuICAgIH1cbn07XG5cblxubW9kdWxlLmV4cG9ydHMgPSBTdHJlYW1IZWxwZXI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/StreamHelper.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/support.js":
/*!****************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/support.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nexports.base64 = true;\nexports.array = true;\nexports.string = true;\nexports.arraybuffer = typeof ArrayBuffer !== \"undefined\" && typeof Uint8Array !== \"undefined\";\nexports.nodebuffer = typeof Buffer !== \"undefined\";\n// contains true if JSZip can read/generate Uint8Array, false otherwise.\nexports.uint8array = typeof Uint8Array !== \"undefined\";\n\nif (typeof ArrayBuffer === \"undefined\") {\n    exports.blob = false;\n}\nelse {\n    var buffer = new ArrayBuffer(0);\n    try {\n        exports.blob = new Blob([buffer], {\n            type: \"application/zip\"\n        }).size === 0;\n    }\n    catch (e) {\n        try {\n            var Builder = self.BlobBuilder || self.WebKitBlobBuilder || self.MozBlobBuilder || self.MSBlobBuilder;\n            var builder = new Builder();\n            builder.append(buffer);\n            exports.blob = builder.getBlob(\"application/zip\").size === 0;\n        }\n        catch (e) {\n            exports.blob = false;\n        }\n    }\n}\n\ntry {\n    exports.nodestream = !!(__webpack_require__(/*! readable-stream */ \"(ssr)/../node_modules/.pnpm/readable-stream@2.3.8/node_modules/readable-stream/readable.js\").Readable);\n} catch(e) {\n    exports.nodestream = false;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/support.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utf8.js":
/*!*************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utf8.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\nvar support = __webpack_require__(/*! ./support */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/support.js\");\nvar nodejsUtils = __webpack_require__(/*! ./nodejsUtils */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejsUtils.js\");\nvar GenericWorker = __webpack_require__(/*! ./stream/GenericWorker */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js\");\n\n/**\n * The following functions come from pako, from pako/lib/utils/strings\n * released under the MIT license, see pako https://github.com/nodeca/pako/\n */\n\n// Table with utf8 lengths (calculated by first byte of sequence)\n// Note, that 5 & 6-byte values and some 4-byte values can not be represented in JS,\n// because max possible codepoint is 0x10ffff\nvar _utf8len = new Array(256);\nfor (var i=0; i<256; i++) {\n    _utf8len[i] = (i >= 252 ? 6 : i >= 248 ? 5 : i >= 240 ? 4 : i >= 224 ? 3 : i >= 192 ? 2 : 1);\n}\n_utf8len[254]=_utf8len[254]=1; // Invalid sequence start\n\n// convert string to array (typed, when possible)\nvar string2buf = function (str) {\n    var buf, c, c2, m_pos, i, str_len = str.length, buf_len = 0;\n\n    // count binary size\n    for (m_pos = 0; m_pos < str_len; m_pos++) {\n        c = str.charCodeAt(m_pos);\n        if ((c & 0xfc00) === 0xd800 && (m_pos+1 < str_len)) {\n            c2 = str.charCodeAt(m_pos+1);\n            if ((c2 & 0xfc00) === 0xdc00) {\n                c = 0x10000 + ((c - 0xd800) << 10) + (c2 - 0xdc00);\n                m_pos++;\n            }\n        }\n        buf_len += c < 0x80 ? 1 : c < 0x800 ? 2 : c < 0x10000 ? 3 : 4;\n    }\n\n    // allocate buffer\n    if (support.uint8array) {\n        buf = new Uint8Array(buf_len);\n    } else {\n        buf = new Array(buf_len);\n    }\n\n    // convert\n    for (i=0, m_pos = 0; i < buf_len; m_pos++) {\n        c = str.charCodeAt(m_pos);\n        if ((c & 0xfc00) === 0xd800 && (m_pos+1 < str_len)) {\n            c2 = str.charCodeAt(m_pos+1);\n            if ((c2 & 0xfc00) === 0xdc00) {\n                c = 0x10000 + ((c - 0xd800) << 10) + (c2 - 0xdc00);\n                m_pos++;\n            }\n        }\n        if (c < 0x80) {\n            /* one byte */\n            buf[i++] = c;\n        } else if (c < 0x800) {\n            /* two bytes */\n            buf[i++] = 0xC0 | (c >>> 6);\n            buf[i++] = 0x80 | (c & 0x3f);\n        } else if (c < 0x10000) {\n            /* three bytes */\n            buf[i++] = 0xE0 | (c >>> 12);\n            buf[i++] = 0x80 | (c >>> 6 & 0x3f);\n            buf[i++] = 0x80 | (c & 0x3f);\n        } else {\n            /* four bytes */\n            buf[i++] = 0xf0 | (c >>> 18);\n            buf[i++] = 0x80 | (c >>> 12 & 0x3f);\n            buf[i++] = 0x80 | (c >>> 6 & 0x3f);\n            buf[i++] = 0x80 | (c & 0x3f);\n        }\n    }\n\n    return buf;\n};\n\n// Calculate max possible position in utf8 buffer,\n// that will not break sequence. If that's not possible\n// - (very small limits) return max size as is.\n//\n// buf[] - utf8 bytes array\n// max   - length limit (mandatory);\nvar utf8border = function(buf, max) {\n    var pos;\n\n    max = max || buf.length;\n    if (max > buf.length) { max = buf.length; }\n\n    // go back from last position, until start of sequence found\n    pos = max-1;\n    while (pos >= 0 && (buf[pos] & 0xC0) === 0x80) { pos--; }\n\n    // Fuckup - very small and broken sequence,\n    // return max, because we should return something anyway.\n    if (pos < 0) { return max; }\n\n    // If we came to start of buffer - that means vuffer is too small,\n    // return max too.\n    if (pos === 0) { return max; }\n\n    return (pos + _utf8len[buf[pos]] > max) ? pos : max;\n};\n\n// convert array to string\nvar buf2string = function (buf) {\n    var i, out, c, c_len;\n    var len = buf.length;\n\n    // Reserve max possible length (2 words per char)\n    // NB: by unknown reasons, Array is significantly faster for\n    //     String.fromCharCode.apply than Uint16Array.\n    var utf16buf = new Array(len*2);\n\n    for (out=0, i=0; i<len;) {\n        c = buf[i++];\n        // quick process ascii\n        if (c < 0x80) { utf16buf[out++] = c; continue; }\n\n        c_len = _utf8len[c];\n        // skip 5 & 6 byte codes\n        if (c_len > 4) { utf16buf[out++] = 0xfffd; i += c_len-1; continue; }\n\n        // apply mask on first byte\n        c &= c_len === 2 ? 0x1f : c_len === 3 ? 0x0f : 0x07;\n        // join the rest\n        while (c_len > 1 && i < len) {\n            c = (c << 6) | (buf[i++] & 0x3f);\n            c_len--;\n        }\n\n        // terminated by end of string?\n        if (c_len > 1) { utf16buf[out++] = 0xfffd; continue; }\n\n        if (c < 0x10000) {\n            utf16buf[out++] = c;\n        } else {\n            c -= 0x10000;\n            utf16buf[out++] = 0xd800 | ((c >> 10) & 0x3ff);\n            utf16buf[out++] = 0xdc00 | (c & 0x3ff);\n        }\n    }\n\n    // shrinkBuf(utf16buf, out)\n    if (utf16buf.length !== out) {\n        if(utf16buf.subarray) {\n            utf16buf = utf16buf.subarray(0, out);\n        } else {\n            utf16buf.length = out;\n        }\n    }\n\n    // return String.fromCharCode.apply(null, utf16buf);\n    return utils.applyFromCharCode(utf16buf);\n};\n\n\n// That's all for the pako functions.\n\n\n/**\n * Transform a javascript string into an array (typed if possible) of bytes,\n * UTF-8 encoded.\n * @param {String} str the string to encode\n * @return {Array|Uint8Array|Buffer} the UTF-8 encoded string.\n */\nexports.utf8encode = function utf8encode(str) {\n    if (support.nodebuffer) {\n        return nodejsUtils.newBufferFrom(str, \"utf-8\");\n    }\n\n    return string2buf(str);\n};\n\n\n/**\n * Transform a bytes array (or a representation) representing an UTF-8 encoded\n * string into a javascript string.\n * @param {Array|Uint8Array|Buffer} buf the data de decode\n * @return {String} the decoded string.\n */\nexports.utf8decode = function utf8decode(buf) {\n    if (support.nodebuffer) {\n        return utils.transformTo(\"nodebuffer\", buf).toString(\"utf-8\");\n    }\n\n    buf = utils.transformTo(support.uint8array ? \"uint8array\" : \"array\", buf);\n\n    return buf2string(buf);\n};\n\n/**\n * A worker to decode utf8 encoded binary chunks into string chunks.\n * @constructor\n */\nfunction Utf8DecodeWorker() {\n    GenericWorker.call(this, \"utf-8 decode\");\n    // the last bytes if a chunk didn't end with a complete codepoint.\n    this.leftOver = null;\n}\nutils.inherits(Utf8DecodeWorker, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nUtf8DecodeWorker.prototype.processChunk = function (chunk) {\n\n    var data = utils.transformTo(support.uint8array ? \"uint8array\" : \"array\", chunk.data);\n\n    // 1st step, re-use what's left of the previous chunk\n    if (this.leftOver && this.leftOver.length) {\n        if(support.uint8array) {\n            var previousData = data;\n            data = new Uint8Array(previousData.length + this.leftOver.length);\n            data.set(this.leftOver, 0);\n            data.set(previousData, this.leftOver.length);\n        } else {\n            data = this.leftOver.concat(data);\n        }\n        this.leftOver = null;\n    }\n\n    var nextBoundary = utf8border(data);\n    var usableData = data;\n    if (nextBoundary !== data.length) {\n        if (support.uint8array) {\n            usableData = data.subarray(0, nextBoundary);\n            this.leftOver = data.subarray(nextBoundary, data.length);\n        } else {\n            usableData = data.slice(0, nextBoundary);\n            this.leftOver = data.slice(nextBoundary, data.length);\n        }\n    }\n\n    this.push({\n        data : exports.utf8decode(usableData),\n        meta : chunk.meta\n    });\n};\n\n/**\n * @see GenericWorker.flush\n */\nUtf8DecodeWorker.prototype.flush = function () {\n    if(this.leftOver && this.leftOver.length) {\n        this.push({\n            data : exports.utf8decode(this.leftOver),\n            meta : {}\n        });\n        this.leftOver = null;\n    }\n};\nexports.Utf8DecodeWorker = Utf8DecodeWorker;\n\n/**\n * A worker to endcode string chunks into utf8 encoded binary chunks.\n * @constructor\n */\nfunction Utf8EncodeWorker() {\n    GenericWorker.call(this, \"utf-8 encode\");\n}\nutils.inherits(Utf8EncodeWorker, GenericWorker);\n\n/**\n * @see GenericWorker.processChunk\n */\nUtf8EncodeWorker.prototype.processChunk = function (chunk) {\n    this.push({\n        data : exports.utf8encode(chunk.data),\n        meta : chunk.meta\n    });\n};\nexports.Utf8EncodeWorker = Utf8EncodeWorker;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utf8.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js":
/*!**************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar support = __webpack_require__(/*! ./support */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/support.js\");\nvar base64 = __webpack_require__(/*! ./base64 */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/base64.js\");\nvar nodejsUtils = __webpack_require__(/*! ./nodejsUtils */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/nodejsUtils.js\");\nvar external = __webpack_require__(/*! ./external */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/external.js\");\n__webpack_require__(/*! setimmediate */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/setimmediate/setImmediate.js\");\n\n\n/**\n * Convert a string that pass as a \"binary string\": it should represent a byte\n * array but may have > 255 char codes. Be sure to take only the first byte\n * and returns the byte array.\n * @param {String} str the string to transform.\n * @return {Array|Uint8Array} the string in a binary format.\n */\nfunction string2binary(str) {\n    var result = null;\n    if (support.uint8array) {\n        result = new Uint8Array(str.length);\n    } else {\n        result = new Array(str.length);\n    }\n    return stringToArrayLike(str, result);\n}\n\n/**\n * Create a new blob with the given content and the given type.\n * @param {String|ArrayBuffer} part the content to put in the blob. DO NOT use\n * an Uint8Array because the stock browser of android 4 won't accept it (it\n * will be silently converted to a string, \"[object Uint8Array]\").\n *\n * Use only ONE part to build the blob to avoid a memory leak in IE11 / Edge:\n * when a large amount of Array is used to create the Blob, the amount of\n * memory consumed is nearly 100 times the original data amount.\n *\n * @param {String} type the mime type of the blob.\n * @return {Blob} the created blob.\n */\nexports.newBlob = function(part, type) {\n    exports.checkSupport(\"blob\");\n\n    try {\n        // Blob constructor\n        return new Blob([part], {\n            type: type\n        });\n    }\n    catch (e) {\n\n        try {\n            // deprecated, browser only, old way\n            var Builder = self.BlobBuilder || self.WebKitBlobBuilder || self.MozBlobBuilder || self.MSBlobBuilder;\n            var builder = new Builder();\n            builder.append(part);\n            return builder.getBlob(type);\n        }\n        catch (e) {\n\n            // well, fuck ?!\n            throw new Error(\"Bug : can't construct the Blob.\");\n        }\n    }\n\n\n};\n/**\n * The identity function.\n * @param {Object} input the input.\n * @return {Object} the same input.\n */\nfunction identity(input) {\n    return input;\n}\n\n/**\n * Fill in an array with a string.\n * @param {String} str the string to use.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} array the array to fill in (will be mutated).\n * @return {Array|ArrayBuffer|Uint8Array|Buffer} the updated array.\n */\nfunction stringToArrayLike(str, array) {\n    for (var i = 0; i < str.length; ++i) {\n        array[i] = str.charCodeAt(i) & 0xFF;\n    }\n    return array;\n}\n\n/**\n * An helper for the function arrayLikeToString.\n * This contains static information and functions that\n * can be optimized by the browser JIT compiler.\n */\nvar arrayToStringHelper = {\n    /**\n     * Transform an array of int into a string, chunk by chunk.\n     * See the performances notes on arrayLikeToString.\n     * @param {Array|ArrayBuffer|Uint8Array|Buffer} array the array to transform.\n     * @param {String} type the type of the array.\n     * @param {Integer} chunk the chunk size.\n     * @return {String} the resulting string.\n     * @throws Error if the chunk is too big for the stack.\n     */\n    stringifyByChunk: function(array, type, chunk) {\n        var result = [], k = 0, len = array.length;\n        // shortcut\n        if (len <= chunk) {\n            return String.fromCharCode.apply(null, array);\n        }\n        while (k < len) {\n            if (type === \"array\" || type === \"nodebuffer\") {\n                result.push(String.fromCharCode.apply(null, array.slice(k, Math.min(k + chunk, len))));\n            }\n            else {\n                result.push(String.fromCharCode.apply(null, array.subarray(k, Math.min(k + chunk, len))));\n            }\n            k += chunk;\n        }\n        return result.join(\"\");\n    },\n    /**\n     * Call String.fromCharCode on every item in the array.\n     * This is the naive implementation, which generate A LOT of intermediate string.\n     * This should be used when everything else fail.\n     * @param {Array|ArrayBuffer|Uint8Array|Buffer} array the array to transform.\n     * @return {String} the result.\n     */\n    stringifyByChar: function(array){\n        var resultStr = \"\";\n        for(var i = 0; i < array.length; i++) {\n            resultStr += String.fromCharCode(array[i]);\n        }\n        return resultStr;\n    },\n    applyCanBeUsed : {\n        /**\n         * true if the browser accepts to use String.fromCharCode on Uint8Array\n         */\n        uint8array : (function () {\n            try {\n                return support.uint8array && String.fromCharCode.apply(null, new Uint8Array(1)).length === 1;\n            } catch (e) {\n                return false;\n            }\n        })(),\n        /**\n         * true if the browser accepts to use String.fromCharCode on nodejs Buffer.\n         */\n        nodebuffer : (function () {\n            try {\n                return support.nodebuffer && String.fromCharCode.apply(null, nodejsUtils.allocBuffer(1)).length === 1;\n            } catch (e) {\n                return false;\n            }\n        })()\n    }\n};\n\n/**\n * Transform an array-like object to a string.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} array the array to transform.\n * @return {String} the result.\n */\nfunction arrayLikeToString(array) {\n    // Performances notes :\n    // --------------------\n    // String.fromCharCode.apply(null, array) is the fastest, see\n    // see http://jsperf.com/converting-a-uint8array-to-a-string/2\n    // but the stack is limited (and we can get huge arrays !).\n    //\n    // result += String.fromCharCode(array[i]); generate too many strings !\n    //\n    // This code is inspired by http://jsperf.com/arraybuffer-to-string-apply-performance/2\n    // TODO : we now have workers that split the work. Do we still need that ?\n    var chunk = 65536,\n        type = exports.getTypeOf(array),\n        canUseApply = true;\n    if (type === \"uint8array\") {\n        canUseApply = arrayToStringHelper.applyCanBeUsed.uint8array;\n    } else if (type === \"nodebuffer\") {\n        canUseApply = arrayToStringHelper.applyCanBeUsed.nodebuffer;\n    }\n\n    if (canUseApply) {\n        while (chunk > 1) {\n            try {\n                return arrayToStringHelper.stringifyByChunk(array, type, chunk);\n            } catch (e) {\n                chunk = Math.floor(chunk / 2);\n            }\n        }\n    }\n\n    // no apply or chunk error : slow and painful algorithm\n    // default browser on android 4.*\n    return arrayToStringHelper.stringifyByChar(array);\n}\n\nexports.applyFromCharCode = arrayLikeToString;\n\n\n/**\n * Copy the data from an array-like to an other array-like.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} arrayFrom the origin array.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} arrayTo the destination array which will be mutated.\n * @return {Array|ArrayBuffer|Uint8Array|Buffer} the updated destination array.\n */\nfunction arrayLikeToArrayLike(arrayFrom, arrayTo) {\n    for (var i = 0; i < arrayFrom.length; i++) {\n        arrayTo[i] = arrayFrom[i];\n    }\n    return arrayTo;\n}\n\n// a matrix containing functions to transform everything into everything.\nvar transform = {};\n\n// string to ?\ntransform[\"string\"] = {\n    \"string\": identity,\n    \"array\": function(input) {\n        return stringToArrayLike(input, new Array(input.length));\n    },\n    \"arraybuffer\": function(input) {\n        return transform[\"string\"][\"uint8array\"](input).buffer;\n    },\n    \"uint8array\": function(input) {\n        return stringToArrayLike(input, new Uint8Array(input.length));\n    },\n    \"nodebuffer\": function(input) {\n        return stringToArrayLike(input, nodejsUtils.allocBuffer(input.length));\n    }\n};\n\n// array to ?\ntransform[\"array\"] = {\n    \"string\": arrayLikeToString,\n    \"array\": identity,\n    \"arraybuffer\": function(input) {\n        return (new Uint8Array(input)).buffer;\n    },\n    \"uint8array\": function(input) {\n        return new Uint8Array(input);\n    },\n    \"nodebuffer\": function(input) {\n        return nodejsUtils.newBufferFrom(input);\n    }\n};\n\n// arraybuffer to ?\ntransform[\"arraybuffer\"] = {\n    \"string\": function(input) {\n        return arrayLikeToString(new Uint8Array(input));\n    },\n    \"array\": function(input) {\n        return arrayLikeToArrayLike(new Uint8Array(input), new Array(input.byteLength));\n    },\n    \"arraybuffer\": identity,\n    \"uint8array\": function(input) {\n        return new Uint8Array(input);\n    },\n    \"nodebuffer\": function(input) {\n        return nodejsUtils.newBufferFrom(new Uint8Array(input));\n    }\n};\n\n// uint8array to ?\ntransform[\"uint8array\"] = {\n    \"string\": arrayLikeToString,\n    \"array\": function(input) {\n        return arrayLikeToArrayLike(input, new Array(input.length));\n    },\n    \"arraybuffer\": function(input) {\n        return input.buffer;\n    },\n    \"uint8array\": identity,\n    \"nodebuffer\": function(input) {\n        return nodejsUtils.newBufferFrom(input);\n    }\n};\n\n// nodebuffer to ?\ntransform[\"nodebuffer\"] = {\n    \"string\": arrayLikeToString,\n    \"array\": function(input) {\n        return arrayLikeToArrayLike(input, new Array(input.length));\n    },\n    \"arraybuffer\": function(input) {\n        return transform[\"nodebuffer\"][\"uint8array\"](input).buffer;\n    },\n    \"uint8array\": function(input) {\n        return arrayLikeToArrayLike(input, new Uint8Array(input.length));\n    },\n    \"nodebuffer\": identity\n};\n\n/**\n * Transform an input into any type.\n * The supported output type are : string, array, uint8array, arraybuffer, nodebuffer.\n * If no output type is specified, the unmodified input will be returned.\n * @param {String} outputType the output type.\n * @param {String|Array|ArrayBuffer|Uint8Array|Buffer} input the input to convert.\n * @throws {Error} an Error if the browser doesn't support the requested output type.\n */\nexports.transformTo = function(outputType, input) {\n    if (!input) {\n        // undefined, null, etc\n        // an empty string won't harm.\n        input = \"\";\n    }\n    if (!outputType) {\n        return input;\n    }\n    exports.checkSupport(outputType);\n    var inputType = exports.getTypeOf(input);\n    var result = transform[inputType][outputType](input);\n    return result;\n};\n\n/**\n * Resolve all relative path components, \".\" and \"..\", in a path. If these relative components\n * traverse above the root then the resulting path will only contain the final path component.\n *\n * All empty components, e.g. \"//\", are removed.\n * @param {string} path A path with / or \\ separators\n * @returns {string} The path with all relative path components resolved.\n */\nexports.resolve = function(path) {\n    var parts = path.split(\"/\");\n    var result = [];\n    for (var index = 0; index < parts.length; index++) {\n        var part = parts[index];\n        // Allow the first and last component to be empty for trailing slashes.\n        if (part === \".\" || (part === \"\" && index !== 0 && index !== parts.length - 1)) {\n            continue;\n        } else if (part === \"..\") {\n            result.pop();\n        } else {\n            result.push(part);\n        }\n    }\n    return result.join(\"/\");\n};\n\n/**\n * Return the type of the input.\n * The type will be in a format valid for JSZip.utils.transformTo : string, array, uint8array, arraybuffer.\n * @param {Object} input the input to identify.\n * @return {String} the (lowercase) type of the input.\n */\nexports.getTypeOf = function(input) {\n    if (typeof input === \"string\") {\n        return \"string\";\n    }\n    if (Object.prototype.toString.call(input) === \"[object Array]\") {\n        return \"array\";\n    }\n    if (support.nodebuffer && nodejsUtils.isBuffer(input)) {\n        return \"nodebuffer\";\n    }\n    if (support.uint8array && input instanceof Uint8Array) {\n        return \"uint8array\";\n    }\n    if (support.arraybuffer && input instanceof ArrayBuffer) {\n        return \"arraybuffer\";\n    }\n};\n\n/**\n * Throw an exception if the type is not supported.\n * @param {String} type the type to check.\n * @throws {Error} an Error if the browser doesn't support the requested type.\n */\nexports.checkSupport = function(type) {\n    var supported = support[type.toLowerCase()];\n    if (!supported) {\n        throw new Error(type + \" is not supported by this platform\");\n    }\n};\n\nexports.MAX_VALUE_16BITS = 65535;\nexports.MAX_VALUE_32BITS = -1; // well, \"\\xFF\\xFF\\xFF\\xFF\\xFF\\xFF\\xFF\\xFF\" is parsed as -1\n\n/**\n * Prettify a string read as binary.\n * @param {string} str the string to prettify.\n * @return {string} a pretty string.\n */\nexports.pretty = function(str) {\n    var res = \"\",\n        code, i;\n    for (i = 0; i < (str || \"\").length; i++) {\n        code = str.charCodeAt(i);\n        res += \"\\\\x\" + (code < 16 ? \"0\" : \"\") + code.toString(16).toUpperCase();\n    }\n    return res;\n};\n\n/**\n * Defer the call of a function.\n * @param {Function} callback the function to call asynchronously.\n * @param {Array} args the arguments to give to the callback.\n */\nexports.delay = function(callback, args, self) {\n    setImmediate(function () {\n        callback.apply(self || null, args || []);\n    });\n};\n\n/**\n * Extends a prototype with an other, without calling a constructor with\n * side effects. Inspired by nodejs' `utils.inherits`\n * @param {Function} ctor the constructor to augment\n * @param {Function} superCtor the parent constructor to use\n */\nexports.inherits = function (ctor, superCtor) {\n    var Obj = function() {};\n    Obj.prototype = superCtor.prototype;\n    ctor.prototype = new Obj();\n};\n\n/**\n * Merge the objects passed as parameters into a new one.\n * @private\n * @param {...Object} var_args All objects to merge.\n * @return {Object} a new object with the data of the others.\n */\nexports.extend = function() {\n    var result = {}, i, attr;\n    for (i = 0; i < arguments.length; i++) { // arguments is not enumerable in some browsers\n        for (attr in arguments[i]) {\n            if (Object.prototype.hasOwnProperty.call(arguments[i], attr) && typeof result[attr] === \"undefined\") {\n                result[attr] = arguments[i][attr];\n            }\n        }\n    }\n    return result;\n};\n\n/**\n * Transform arbitrary content into a Promise.\n * @param {String} name a name for the content being processed.\n * @param {Object} inputData the content to process.\n * @param {Boolean} isBinary true if the content is not an unicode string\n * @param {Boolean} isOptimizedBinaryString true if the string content only has one byte per character.\n * @param {Boolean} isBase64 true if the string content is encoded with base64.\n * @return {Promise} a promise in a format usable by JSZip.\n */\nexports.prepareContent = function(name, inputData, isBinary, isOptimizedBinaryString, isBase64) {\n\n    // if inputData is already a promise, this flatten it.\n    var promise = external.Promise.resolve(inputData).then(function(data) {\n\n\n        var isBlob = support.blob && (data instanceof Blob || [\"[object File]\", \"[object Blob]\"].indexOf(Object.prototype.toString.call(data)) !== -1);\n\n        if (isBlob && typeof FileReader !== \"undefined\") {\n            return new external.Promise(function (resolve, reject) {\n                var reader = new FileReader();\n\n                reader.onload = function(e) {\n                    resolve(e.target.result);\n                };\n                reader.onerror = function(e) {\n                    reject(e.target.error);\n                };\n                reader.readAsArrayBuffer(data);\n            });\n        } else {\n            return data;\n        }\n    });\n\n    return promise.then(function(data) {\n        var dataType = exports.getTypeOf(data);\n\n        if (!dataType) {\n            return external.Promise.reject(\n                new Error(\"Can't read the data of '\" + name + \"'. Is it \" +\n                          \"in a supported JavaScript type (String, Blob, ArrayBuffer, etc) ?\")\n            );\n        }\n        // special case : it's way easier to work with Uint8Array than with ArrayBuffer\n        if (dataType === \"arraybuffer\") {\n            data = exports.transformTo(\"uint8array\", data);\n        } else if (dataType === \"string\") {\n            if (isBase64) {\n                data = base64.decode(data);\n            }\n            else if (isBinary) {\n                // optimizedBinaryString === true means that the file has already been filtered with a 0xFF mask\n                if (isOptimizedBinaryString !== true) {\n                    // this is a string, not in a base64 format.\n                    // Be sure that this is a correct \"binary string\"\n                    data = string2binary(data);\n                }\n            }\n        }\n        return data;\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/zipEntries.js":
/*!*******************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/zipEntries.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar readerFor = __webpack_require__(/*! ./reader/readerFor */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/readerFor.js\");\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\nvar sig = __webpack_require__(/*! ./signature */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/signature.js\");\nvar ZipEntry = __webpack_require__(/*! ./zipEntry */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/zipEntry.js\");\nvar support = __webpack_require__(/*! ./support */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/support.js\");\n//  class ZipEntries {{{\n/**\n * All the entries in the zip file.\n * @constructor\n * @param {Object} loadOptions Options for loading the stream.\n */\nfunction ZipEntries(loadOptions) {\n    this.files = [];\n    this.loadOptions = loadOptions;\n}\nZipEntries.prototype = {\n    /**\n     * Check that the reader is on the specified signature.\n     * @param {string} expectedSignature the expected signature.\n     * @throws {Error} if it is an other signature.\n     */\n    checkSignature: function(expectedSignature) {\n        if (!this.reader.readAndCheckSignature(expectedSignature)) {\n            this.reader.index -= 4;\n            var signature = this.reader.readString(4);\n            throw new Error(\"Corrupted zip or bug: unexpected signature \" + \"(\" + utils.pretty(signature) + \", expected \" + utils.pretty(expectedSignature) + \")\");\n        }\n    },\n    /**\n     * Check if the given signature is at the given index.\n     * @param {number} askedIndex the index to check.\n     * @param {string} expectedSignature the signature to expect.\n     * @return {boolean} true if the signature is here, false otherwise.\n     */\n    isSignature: function(askedIndex, expectedSignature) {\n        var currentIndex = this.reader.index;\n        this.reader.setIndex(askedIndex);\n        var signature = this.reader.readString(4);\n        var result = signature === expectedSignature;\n        this.reader.setIndex(currentIndex);\n        return result;\n    },\n    /**\n     * Read the end of the central directory.\n     */\n    readBlockEndOfCentral: function() {\n        this.diskNumber = this.reader.readInt(2);\n        this.diskWithCentralDirStart = this.reader.readInt(2);\n        this.centralDirRecordsOnThisDisk = this.reader.readInt(2);\n        this.centralDirRecords = this.reader.readInt(2);\n        this.centralDirSize = this.reader.readInt(4);\n        this.centralDirOffset = this.reader.readInt(4);\n\n        this.zipCommentLength = this.reader.readInt(2);\n        // warning : the encoding depends of the system locale\n        // On a linux machine with LANG=en_US.utf8, this field is utf8 encoded.\n        // On a windows machine, this field is encoded with the localized windows code page.\n        var zipComment = this.reader.readData(this.zipCommentLength);\n        var decodeParamType = support.uint8array ? \"uint8array\" : \"array\";\n        // To get consistent behavior with the generation part, we will assume that\n        // this is utf8 encoded unless specified otherwise.\n        var decodeContent = utils.transformTo(decodeParamType, zipComment);\n        this.zipComment = this.loadOptions.decodeFileName(decodeContent);\n    },\n    /**\n     * Read the end of the Zip 64 central directory.\n     * Not merged with the method readEndOfCentral :\n     * The end of central can coexist with its Zip64 brother,\n     * I don't want to read the wrong number of bytes !\n     */\n    readBlockZip64EndOfCentral: function() {\n        this.zip64EndOfCentralSize = this.reader.readInt(8);\n        this.reader.skip(4);\n        // this.versionMadeBy = this.reader.readString(2);\n        // this.versionNeeded = this.reader.readInt(2);\n        this.diskNumber = this.reader.readInt(4);\n        this.diskWithCentralDirStart = this.reader.readInt(4);\n        this.centralDirRecordsOnThisDisk = this.reader.readInt(8);\n        this.centralDirRecords = this.reader.readInt(8);\n        this.centralDirSize = this.reader.readInt(8);\n        this.centralDirOffset = this.reader.readInt(8);\n\n        this.zip64ExtensibleData = {};\n        var extraDataSize = this.zip64EndOfCentralSize - 44,\n            index = 0,\n            extraFieldId,\n            extraFieldLength,\n            extraFieldValue;\n        while (index < extraDataSize) {\n            extraFieldId = this.reader.readInt(2);\n            extraFieldLength = this.reader.readInt(4);\n            extraFieldValue = this.reader.readData(extraFieldLength);\n            this.zip64ExtensibleData[extraFieldId] = {\n                id: extraFieldId,\n                length: extraFieldLength,\n                value: extraFieldValue\n            };\n        }\n    },\n    /**\n     * Read the end of the Zip 64 central directory locator.\n     */\n    readBlockZip64EndOfCentralLocator: function() {\n        this.diskWithZip64CentralDirStart = this.reader.readInt(4);\n        this.relativeOffsetEndOfZip64CentralDir = this.reader.readInt(8);\n        this.disksCount = this.reader.readInt(4);\n        if (this.disksCount > 1) {\n            throw new Error(\"Multi-volumes zip are not supported\");\n        }\n    },\n    /**\n     * Read the local files, based on the offset read in the central part.\n     */\n    readLocalFiles: function() {\n        var i, file;\n        for (i = 0; i < this.files.length; i++) {\n            file = this.files[i];\n            this.reader.setIndex(file.localHeaderOffset);\n            this.checkSignature(sig.LOCAL_FILE_HEADER);\n            file.readLocalPart(this.reader);\n            file.handleUTF8();\n            file.processAttributes();\n        }\n    },\n    /**\n     * Read the central directory.\n     */\n    readCentralDir: function() {\n        var file;\n\n        this.reader.setIndex(this.centralDirOffset);\n        while (this.reader.readAndCheckSignature(sig.CENTRAL_FILE_HEADER)) {\n            file = new ZipEntry({\n                zip64: this.zip64\n            }, this.loadOptions);\n            file.readCentralPart(this.reader);\n            this.files.push(file);\n        }\n\n        if (this.centralDirRecords !== this.files.length) {\n            if (this.centralDirRecords !== 0 && this.files.length === 0) {\n                // We expected some records but couldn't find ANY.\n                // This is really suspicious, as if something went wrong.\n                throw new Error(\"Corrupted zip or bug: expected \" + this.centralDirRecords + \" records in central dir, got \" + this.files.length);\n            } else {\n                // We found some records but not all.\n                // Something is wrong but we got something for the user: no error here.\n                // console.warn(\"expected\", this.centralDirRecords, \"records in central dir, got\", this.files.length);\n            }\n        }\n    },\n    /**\n     * Read the end of central directory.\n     */\n    readEndOfCentral: function() {\n        var offset = this.reader.lastIndexOfSignature(sig.CENTRAL_DIRECTORY_END);\n        if (offset < 0) {\n            // Check if the content is a truncated zip or complete garbage.\n            // A \"LOCAL_FILE_HEADER\" is not required at the beginning (auto\n            // extractible zip for example) but it can give a good hint.\n            // If an ajax request was used without responseType, we will also\n            // get unreadable data.\n            var isGarbage = !this.isSignature(0, sig.LOCAL_FILE_HEADER);\n\n            if (isGarbage) {\n                throw new Error(\"Can't find end of central directory : is this a zip file ? \" +\n                                \"If it is, see https://stuk.github.io/jszip/documentation/howto/read_zip.html\");\n            } else {\n                throw new Error(\"Corrupted zip: can't find end of central directory\");\n            }\n\n        }\n        this.reader.setIndex(offset);\n        var endOfCentralDirOffset = offset;\n        this.checkSignature(sig.CENTRAL_DIRECTORY_END);\n        this.readBlockEndOfCentral();\n\n\n        /* extract from the zip spec :\n            4)  If one of the fields in the end of central directory\n                record is too small to hold required data, the field\n                should be set to -1 (0xFFFF or 0xFFFFFFFF) and the\n                ZIP64 format record should be created.\n            5)  The end of central directory record and the\n                Zip64 end of central directory locator record must\n                reside on the same disk when splitting or spanning\n                an archive.\n         */\n        if (this.diskNumber === utils.MAX_VALUE_16BITS || this.diskWithCentralDirStart === utils.MAX_VALUE_16BITS || this.centralDirRecordsOnThisDisk === utils.MAX_VALUE_16BITS || this.centralDirRecords === utils.MAX_VALUE_16BITS || this.centralDirSize === utils.MAX_VALUE_32BITS || this.centralDirOffset === utils.MAX_VALUE_32BITS) {\n            this.zip64 = true;\n\n            /*\n            Warning : the zip64 extension is supported, but ONLY if the 64bits integer read from\n            the zip file can fit into a 32bits integer. This cannot be solved : JavaScript represents\n            all numbers as 64-bit double precision IEEE 754 floating point numbers.\n            So, we have 53bits for integers and bitwise operations treat everything as 32bits.\n            see https://developer.mozilla.org/en-US/docs/JavaScript/Reference/Operators/Bitwise_Operators\n            and http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-262.pdf section 8.5\n            */\n\n            // should look for a zip64 EOCD locator\n            offset = this.reader.lastIndexOfSignature(sig.ZIP64_CENTRAL_DIRECTORY_LOCATOR);\n            if (offset < 0) {\n                throw new Error(\"Corrupted zip: can't find the ZIP64 end of central directory locator\");\n            }\n            this.reader.setIndex(offset);\n            this.checkSignature(sig.ZIP64_CENTRAL_DIRECTORY_LOCATOR);\n            this.readBlockZip64EndOfCentralLocator();\n\n            // now the zip64 EOCD record\n            if (!this.isSignature(this.relativeOffsetEndOfZip64CentralDir, sig.ZIP64_CENTRAL_DIRECTORY_END)) {\n                // console.warn(\"ZIP64 end of central directory not where expected.\");\n                this.relativeOffsetEndOfZip64CentralDir = this.reader.lastIndexOfSignature(sig.ZIP64_CENTRAL_DIRECTORY_END);\n                if (this.relativeOffsetEndOfZip64CentralDir < 0) {\n                    throw new Error(\"Corrupted zip: can't find the ZIP64 end of central directory\");\n                }\n            }\n            this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir);\n            this.checkSignature(sig.ZIP64_CENTRAL_DIRECTORY_END);\n            this.readBlockZip64EndOfCentral();\n        }\n\n        var expectedEndOfCentralDirOffset = this.centralDirOffset + this.centralDirSize;\n        if (this.zip64) {\n            expectedEndOfCentralDirOffset += 20; // end of central dir 64 locator\n            expectedEndOfCentralDirOffset += 12 /* should not include the leading 12 bytes */ + this.zip64EndOfCentralSize;\n        }\n\n        var extraBytes = endOfCentralDirOffset - expectedEndOfCentralDirOffset;\n\n        if (extraBytes > 0) {\n            // console.warn(extraBytes, \"extra bytes at beginning or within zipfile\");\n            if (this.isSignature(endOfCentralDirOffset, sig.CENTRAL_FILE_HEADER)) {\n                // The offsets seem wrong, but we have something at the specified offset.\n                // So… we keep it.\n            } else {\n                // the offset is wrong, update the \"zero\" of the reader\n                // this happens if data has been prepended (crx files for example)\n                this.reader.zero = extraBytes;\n            }\n        } else if (extraBytes < 0) {\n            throw new Error(\"Corrupted zip: missing \" + Math.abs(extraBytes) + \" bytes.\");\n        }\n    },\n    prepareReader: function(data) {\n        this.reader = readerFor(data);\n    },\n    /**\n     * Read a zip file and create ZipEntries.\n     * @param {String|ArrayBuffer|Uint8Array|Buffer} data the binary string representing a zip file.\n     */\n    load: function(data) {\n        this.prepareReader(data);\n        this.readEndOfCentral();\n        this.readCentralDir();\n        this.readLocalFiles();\n    }\n};\n// }}} end of ZipEntries\nmodule.exports = ZipEntries;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/zipEntries.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/zipEntry.js":
/*!*****************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/zipEntry.js ***!
  \*****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar readerFor = __webpack_require__(/*! ./reader/readerFor */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/reader/readerFor.js\");\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utils.js\");\nvar CompressedObject = __webpack_require__(/*! ./compressedObject */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/compressedObject.js\");\nvar crc32fn = __webpack_require__(/*! ./crc32 */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/crc32.js\");\nvar utf8 = __webpack_require__(/*! ./utf8 */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utf8.js\");\nvar compressions = __webpack_require__(/*! ./compressions */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/compressions.js\");\nvar support = __webpack_require__(/*! ./support */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/support.js\");\n\nvar MADE_BY_DOS = 0x00;\nvar MADE_BY_UNIX = 0x03;\n\n/**\n * Find a compression registered in JSZip.\n * @param {string} compressionMethod the method magic to find.\n * @return {Object|null} the JSZip compression object, null if none found.\n */\nvar findCompression = function(compressionMethod) {\n    for (var method in compressions) {\n        if (!Object.prototype.hasOwnProperty.call(compressions, method)) {\n            continue;\n        }\n        if (compressions[method].magic === compressionMethod) {\n            return compressions[method];\n        }\n    }\n    return null;\n};\n\n// class ZipEntry {{{\n/**\n * An entry in the zip file.\n * @constructor\n * @param {Object} options Options of the current file.\n * @param {Object} loadOptions Options for loading the stream.\n */\nfunction ZipEntry(options, loadOptions) {\n    this.options = options;\n    this.loadOptions = loadOptions;\n}\nZipEntry.prototype = {\n    /**\n     * say if the file is encrypted.\n     * @return {boolean} true if the file is encrypted, false otherwise.\n     */\n    isEncrypted: function() {\n        // bit 1 is set\n        return (this.bitFlag & 0x0001) === 0x0001;\n    },\n    /**\n     * say if the file has utf-8 filename/comment.\n     * @return {boolean} true if the filename/comment is in utf-8, false otherwise.\n     */\n    useUTF8: function() {\n        // bit 11 is set\n        return (this.bitFlag & 0x0800) === 0x0800;\n    },\n    /**\n     * Read the local part of a zip file and add the info in this object.\n     * @param {DataReader} reader the reader to use.\n     */\n    readLocalPart: function(reader) {\n        var compression, localExtraFieldsLength;\n\n        // we already know everything from the central dir !\n        // If the central dir data are false, we are doomed.\n        // On the bright side, the local part is scary  : zip64, data descriptors, both, etc.\n        // The less data we get here, the more reliable this should be.\n        // Let's skip the whole header and dash to the data !\n        reader.skip(22);\n        // in some zip created on windows, the filename stored in the central dir contains \\ instead of /.\n        // Strangely, the filename here is OK.\n        // I would love to treat these zip files as corrupted (see http://www.info-zip.org/FAQ.html#backslashes\n        // or APPNOTE#********, \"All slashes MUST be forward slashes '/'\") but there are a lot of bad zip generators...\n        // Search \"unzip mismatching \"local\" filename continuing with \"central\" filename version\" on\n        // the internet.\n        //\n        // I think I see the logic here : the central directory is used to display\n        // content and the local directory is used to extract the files. Mixing / and \\\n        // may be used to display \\ to windows users and use / when extracting the files.\n        // Unfortunately, this lead also to some issues : http://seclists.org/fulldisclosure/2009/Sep/394\n        this.fileNameLength = reader.readInt(2);\n        localExtraFieldsLength = reader.readInt(2); // can't be sure this will be the same as the central dir\n        // the fileName is stored as binary data, the handleUTF8 method will take care of the encoding.\n        this.fileName = reader.readData(this.fileNameLength);\n        reader.skip(localExtraFieldsLength);\n\n        if (this.compressedSize === -1 || this.uncompressedSize === -1) {\n            throw new Error(\"Bug or corrupted zip : didn't get enough information from the central directory \" + \"(compressedSize === -1 || uncompressedSize === -1)\");\n        }\n\n        compression = findCompression(this.compressionMethod);\n        if (compression === null) { // no compression found\n            throw new Error(\"Corrupted zip : compression \" + utils.pretty(this.compressionMethod) + \" unknown (inner file : \" + utils.transformTo(\"string\", this.fileName) + \")\");\n        }\n        this.decompressed = new CompressedObject(this.compressedSize, this.uncompressedSize, this.crc32, compression, reader.readData(this.compressedSize));\n    },\n\n    /**\n     * Read the central part of a zip file and add the info in this object.\n     * @param {DataReader} reader the reader to use.\n     */\n    readCentralPart: function(reader) {\n        this.versionMadeBy = reader.readInt(2);\n        reader.skip(2);\n        // this.versionNeeded = reader.readInt(2);\n        this.bitFlag = reader.readInt(2);\n        this.compressionMethod = reader.readString(2);\n        this.date = reader.readDate();\n        this.crc32 = reader.readInt(4);\n        this.compressedSize = reader.readInt(4);\n        this.uncompressedSize = reader.readInt(4);\n        var fileNameLength = reader.readInt(2);\n        this.extraFieldsLength = reader.readInt(2);\n        this.fileCommentLength = reader.readInt(2);\n        this.diskNumberStart = reader.readInt(2);\n        this.internalFileAttributes = reader.readInt(2);\n        this.externalFileAttributes = reader.readInt(4);\n        this.localHeaderOffset = reader.readInt(4);\n\n        if (this.isEncrypted()) {\n            throw new Error(\"Encrypted zip are not supported\");\n        }\n\n        // will be read in the local part, see the comments there\n        reader.skip(fileNameLength);\n        this.readExtraFields(reader);\n        this.parseZIP64ExtraField(reader);\n        this.fileComment = reader.readData(this.fileCommentLength);\n    },\n\n    /**\n     * Parse the external file attributes and get the unix/dos permissions.\n     */\n    processAttributes: function () {\n        this.unixPermissions = null;\n        this.dosPermissions = null;\n        var madeBy = this.versionMadeBy >> 8;\n\n        // Check if we have the DOS directory flag set.\n        // We look for it in the DOS and UNIX permissions\n        // but some unknown platform could set it as a compatibility flag.\n        this.dir = this.externalFileAttributes & 0x0010 ? true : false;\n\n        if(madeBy === MADE_BY_DOS) {\n            // first 6 bits (0 to 5)\n            this.dosPermissions = this.externalFileAttributes & 0x3F;\n        }\n\n        if(madeBy === MADE_BY_UNIX) {\n            this.unixPermissions = (this.externalFileAttributes >> 16) & 0xFFFF;\n            // the octal permissions are in (this.unixPermissions & 0x01FF).toString(8);\n        }\n\n        // fail safe : if the name ends with a / it probably means a folder\n        if (!this.dir && this.fileNameStr.slice(-1) === \"/\") {\n            this.dir = true;\n        }\n    },\n\n    /**\n     * Parse the ZIP64 extra field and merge the info in the current ZipEntry.\n     * @param {DataReader} reader the reader to use.\n     */\n    parseZIP64ExtraField: function() {\n        if (!this.extraFields[0x0001]) {\n            return;\n        }\n\n        // should be something, preparing the extra reader\n        var extraReader = readerFor(this.extraFields[0x0001].value);\n\n        // I really hope that these 64bits integer can fit in 32 bits integer, because js\n        // won't let us have more.\n        if (this.uncompressedSize === utils.MAX_VALUE_32BITS) {\n            this.uncompressedSize = extraReader.readInt(8);\n        }\n        if (this.compressedSize === utils.MAX_VALUE_32BITS) {\n            this.compressedSize = extraReader.readInt(8);\n        }\n        if (this.localHeaderOffset === utils.MAX_VALUE_32BITS) {\n            this.localHeaderOffset = extraReader.readInt(8);\n        }\n        if (this.diskNumberStart === utils.MAX_VALUE_32BITS) {\n            this.diskNumberStart = extraReader.readInt(4);\n        }\n    },\n    /**\n     * Read the central part of a zip file and add the info in this object.\n     * @param {DataReader} reader the reader to use.\n     */\n    readExtraFields: function(reader) {\n        var end = reader.index + this.extraFieldsLength,\n            extraFieldId,\n            extraFieldLength,\n            extraFieldValue;\n\n        if (!this.extraFields) {\n            this.extraFields = {};\n        }\n\n        while (reader.index + 4 < end) {\n            extraFieldId = reader.readInt(2);\n            extraFieldLength = reader.readInt(2);\n            extraFieldValue = reader.readData(extraFieldLength);\n\n            this.extraFields[extraFieldId] = {\n                id: extraFieldId,\n                length: extraFieldLength,\n                value: extraFieldValue\n            };\n        }\n\n        reader.setIndex(end);\n    },\n    /**\n     * Apply an UTF8 transformation if needed.\n     */\n    handleUTF8: function() {\n        var decodeParamType = support.uint8array ? \"uint8array\" : \"array\";\n        if (this.useUTF8()) {\n            this.fileNameStr = utf8.utf8decode(this.fileName);\n            this.fileCommentStr = utf8.utf8decode(this.fileComment);\n        } else {\n            var upath = this.findExtraFieldUnicodePath();\n            if (upath !== null) {\n                this.fileNameStr = upath;\n            } else {\n                // ASCII text or unsupported code page\n                var fileNameByteArray =  utils.transformTo(decodeParamType, this.fileName);\n                this.fileNameStr = this.loadOptions.decodeFileName(fileNameByteArray);\n            }\n\n            var ucomment = this.findExtraFieldUnicodeComment();\n            if (ucomment !== null) {\n                this.fileCommentStr = ucomment;\n            } else {\n                // ASCII text or unsupported code page\n                var commentByteArray =  utils.transformTo(decodeParamType, this.fileComment);\n                this.fileCommentStr = this.loadOptions.decodeFileName(commentByteArray);\n            }\n        }\n    },\n\n    /**\n     * Find the unicode path declared in the extra field, if any.\n     * @return {String} the unicode path, null otherwise.\n     */\n    findExtraFieldUnicodePath: function() {\n        var upathField = this.extraFields[0x7075];\n        if (upathField) {\n            var extraReader = readerFor(upathField.value);\n\n            // wrong version\n            if (extraReader.readInt(1) !== 1) {\n                return null;\n            }\n\n            // the crc of the filename changed, this field is out of date.\n            if (crc32fn(this.fileName) !== extraReader.readInt(4)) {\n                return null;\n            }\n\n            return utf8.utf8decode(extraReader.readData(upathField.length - 5));\n        }\n        return null;\n    },\n\n    /**\n     * Find the unicode comment declared in the extra field, if any.\n     * @return {String} the unicode comment, null otherwise.\n     */\n    findExtraFieldUnicodeComment: function() {\n        var ucommentField = this.extraFields[0x6375];\n        if (ucommentField) {\n            var extraReader = readerFor(ucommentField.value);\n\n            // wrong version\n            if (extraReader.readInt(1) !== 1) {\n                return null;\n            }\n\n            // the crc of the comment changed, this field is out of date.\n            if (crc32fn(this.fileComment) !== extraReader.readInt(4)) {\n                return null;\n            }\n\n            return utf8.utf8decode(extraReader.readData(ucommentField.length - 5));\n        }\n        return null;\n    }\n};\nmodule.exports = ZipEntry;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/zipEntry.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/zipObject.js":
/*!******************************************************************************!*\
  !*** ../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/zipObject.js ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar StreamHelper = __webpack_require__(/*! ./stream/StreamHelper */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/StreamHelper.js\");\nvar DataWorker = __webpack_require__(/*! ./stream/DataWorker */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/DataWorker.js\");\nvar utf8 = __webpack_require__(/*! ./utf8 */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/utf8.js\");\nvar CompressedObject = __webpack_require__(/*! ./compressedObject */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/compressedObject.js\");\nvar GenericWorker = __webpack_require__(/*! ./stream/GenericWorker */ \"(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/stream/GenericWorker.js\");\n\n/**\n * A simple object representing a file in the zip file.\n * @constructor\n * @param {string} name the name of the file\n * @param {String|ArrayBuffer|Uint8Array|Buffer} data the data\n * @param {Object} options the options of the file\n */\nvar ZipObject = function(name, data, options) {\n    this.name = name;\n    this.dir = options.dir;\n    this.date = options.date;\n    this.comment = options.comment;\n    this.unixPermissions = options.unixPermissions;\n    this.dosPermissions = options.dosPermissions;\n\n    this._data = data;\n    this._dataBinary = options.binary;\n    // keep only the compression\n    this.options = {\n        compression : options.compression,\n        compressionOptions : options.compressionOptions\n    };\n};\n\nZipObject.prototype = {\n    /**\n     * Create an internal stream for the content of this object.\n     * @param {String} type the type of each chunk.\n     * @return StreamHelper the stream.\n     */\n    internalStream: function (type) {\n        var result = null, outputType = \"string\";\n        try {\n            if (!type) {\n                throw new Error(\"No output type specified.\");\n            }\n            outputType = type.toLowerCase();\n            var askUnicodeString = outputType === \"string\" || outputType === \"text\";\n            if (outputType === \"binarystring\" || outputType === \"text\") {\n                outputType = \"string\";\n            }\n            result = this._decompressWorker();\n\n            var isUnicodeString = !this._dataBinary;\n\n            if (isUnicodeString && !askUnicodeString) {\n                result = result.pipe(new utf8.Utf8EncodeWorker());\n            }\n            if (!isUnicodeString && askUnicodeString) {\n                result = result.pipe(new utf8.Utf8DecodeWorker());\n            }\n        } catch (e) {\n            result = new GenericWorker(\"error\");\n            result.error(e);\n        }\n\n        return new StreamHelper(result, outputType, \"\");\n    },\n\n    /**\n     * Prepare the content in the asked type.\n     * @param {String} type the type of the result.\n     * @param {Function} onUpdate a function to call on each internal update.\n     * @return Promise the promise of the result.\n     */\n    async: function (type, onUpdate) {\n        return this.internalStream(type).accumulate(onUpdate);\n    },\n\n    /**\n     * Prepare the content as a nodejs stream.\n     * @param {String} type the type of each chunk.\n     * @param {Function} onUpdate a function to call on each internal update.\n     * @return Stream the stream.\n     */\n    nodeStream: function (type, onUpdate) {\n        return this.internalStream(type || \"nodebuffer\").toNodejsStream(onUpdate);\n    },\n\n    /**\n     * Return a worker for the compressed content.\n     * @private\n     * @param {Object} compression the compression object to use.\n     * @param {Object} compressionOptions the options to use when compressing.\n     * @return Worker the worker.\n     */\n    _compressWorker: function (compression, compressionOptions) {\n        if (\n            this._data instanceof CompressedObject &&\n            this._data.compression.magic === compression.magic\n        ) {\n            return this._data.getCompressedWorker();\n        } else {\n            var result = this._decompressWorker();\n            if(!this._dataBinary) {\n                result = result.pipe(new utf8.Utf8EncodeWorker());\n            }\n            return CompressedObject.createWorkerFrom(result, compression, compressionOptions);\n        }\n    },\n    /**\n     * Return a worker for the decompressed content.\n     * @private\n     * @return Worker the worker.\n     */\n    _decompressWorker : function () {\n        if (this._data instanceof CompressedObject) {\n            return this._data.getContentWorker();\n        } else if (this._data instanceof GenericWorker) {\n            return this._data;\n        } else {\n            return new DataWorker(this._data);\n        }\n    }\n};\n\nvar removedMethods = [\"asText\", \"asBinary\", \"asNodeBuffer\", \"asUint8Array\", \"asArrayBuffer\"];\nvar removedFn = function () {\n    throw new Error(\"This method has been removed in JSZip 3.0, please check the upgrade guide.\");\n};\n\nfor(var i = 0; i < removedMethods.length; i++) {\n    ZipObject.prototype[removedMethods[i]] = removedFn;\n}\nmodule.exports = ZipObject;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/jszip@3.10.1/node_modules/jszip/lib/zipObject.js\n");

/***/ })

};
;