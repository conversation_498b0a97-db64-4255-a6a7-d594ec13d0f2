(()=>{var e={};e.id=615,e.ids=[615],e.modules={2641:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,85770,23)),Promise.resolve().then(t.t.bind(t,88204,23)),Promise.resolve().then(t.t.bind(t,82576,23)),Promise.resolve().then(t.t.bind(t,59507,23)),Promise.resolve().then(t.t.bind(t,61283,23)),Promise.resolve().then(t.t.bind(t,75147,23)),Promise.resolve().then(t.t.bind(t,83163,23)),Promise.resolve().then(t.t.bind(t,99773,23))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4761:()=>{},6388:()=>{},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25290:()=>{},25794:()=>{},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},59650:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s,metadata:()=>o});var n=t(95479);t(25290);let o={title:"v0 App",description:"Created with v0",generator:"v0.dev"};function s({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{children:e})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68313:()=>{},91514:()=>{},92913:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,60140,23)),Promise.resolve().then(t.t.bind(t,18946,23)),Promise.resolve().then(t.t.bind(t,74178,23)),Promise.resolve().then(t.t.bind(t,6229,23)),Promise.resolve().then(t.t.bind(t,31281,23)),Promise.resolve().then(t.t.bind(t,93833,23)),Promise.resolve().then(t.t.bind(t,97857,23)),Promise.resolve().then(t.t.bind(t,4947,23))},97369:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>p,routeModule:()=>h,tree:()=>l});var n=t(7025),o=t(18198),s=t(82576),i=t.n(s),d=t(45239),a={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>d[e]);t.d(r,a);let l={children:["",{children:["(dashboard)",{children:["feature-update",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,6388,23)),"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\(dashboard)\\feature-update\\page.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,4540,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,53117,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,6874,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,59650)),"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,4540,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,53117,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,6874,23)),"next/dist/client/components/unauthorized-error"]}]}.children,p=["D:\\pycode\\support_chart2\\hotel-dashboard\\app\\(dashboard)\\feature-update\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/(dashboard)/feature-update/page",pathname:"/feature-update",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[557,273],()=>t(97369));module.exports=n})();