(()=>{var e={};e.id=908,e.ids=[908],e.modules={1739:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(51129).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\surface-data-query\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\(dashboard)\\surface-data-query\\page.tsx","default")},2641:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85770,23)),Promise.resolve().then(r.t.bind(r,88204,23)),Promise.resolve().then(r.t.bind(r,82576,23)),Promise.resolve().then(r.t.bind(r,59507,23)),Promise.resolve().then(r.t.bind(r,61283,23)),Promise.resolve().then(r.t.bind(r,75147,23)),Promise.resolve().then(r.t.bind(r,83163,23)),Promise.resolve().then(r.t.bind(r,99773,23))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8629:(e,t,r)=>{"use strict";r.d(t,{dj:()=>p,oR:()=>c});var n=r(13072);let o=0,s=new Map,i=e=>{if(s.has(e))return;let t=setTimeout(()=>{s.delete(e),l({type:"REMOVE_TOAST",toastId:e})},1e6);s.set(e,t)},a=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?i(r):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},d=[],u={toasts:[]};function l(e){u=a(u,e),d.forEach(e=>{e(u)})}function c({...e}){let t=(o=(o+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>l({type:"DISMISS_TOAST",toastId:t});return l({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>l({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function p(){let[e,t]=n.useState(u);return n.useEffect(()=>(d.push(t),()=>{let e=d.indexOf(t);e>-1&&d.splice(e,1)}),[e]),{...e,toast:c,dismiss:e=>l({type:"DISMISS_TOAST",toastId:e})}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25290:()=>{},25794:()=>{},27751:(e,t,r)=>{Promise.resolve().then(r.bind(r,1739))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29771:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>l,routeModule:()=>p,tree:()=>u});var n=r(7025),o=r(18198),s=r(82576),i=r.n(s),a=r(45239),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let u={children:["",{children:["(dashboard)",{children:["surface-data-query",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1739)),"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\(dashboard)\\surface-data-query\\page.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,4540,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,53117,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,6874,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,59650)),"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,4540,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,53117,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,6874,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["D:\\pycode\\support_chart2\\hotel-dashboard\\app\\(dashboard)\\surface-data-query\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/(dashboard)/surface-data-query/page",pathname:"/surface-data-query",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},33873:e=>{"use strict";e.exports=require("path")},43030:(e,t,r)=>{"use strict";r.d(t,{J:()=>u});var n=r(45781),o=r(13072),s=r(11687),i=r(87990),a=r(77401);let d=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),u=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)(s.b,{ref:r,className:(0,a.cn)(d(),e),...t}));u.displayName=s.b.displayName},44238:(e,t,r)=>{"use strict";r.d(t,{$:()=>u});var n=r(45781),o=r(13072),s=r(83759),i=r(87990),a=r(77401);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=o.forwardRef(({className:e,variant:t,size:r,asChild:o=!1,...i},u)=>{let l=o?s.DX:"button";return(0,n.jsx)(l,{className:(0,a.cn)(d({variant:t,size:r,className:e})),ref:u,...i})});u.displayName="Button"},58015:(e,t,r)=>{Promise.resolve().then(r.bind(r,6801))},59650:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s,metadata:()=>o});var n=r(95479);r(25290);let o={title:"v0 App",description:"Created with v0",generator:"v0.dev"};function s({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{children:e})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77401:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s});var n=r(42366),o=r(73927);function s(...e){return(0,o.QP)((0,n.$)(e))}},91514:()=>{},92913:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,60140,23)),Promise.resolve().then(r.t.bind(r,18946,23)),Promise.resolve().then(r.t.bind(r,74178,23)),Promise.resolve().then(r.t.bind(r,6229,23)),Promise.resolve().then(r.t.bind(r,31281,23)),Promise.resolve().then(r.t.bind(r,93833,23)),Promise.resolve().then(r.t.bind(r,97857,23)),Promise.resolve().then(r.t.bind(r,4947,23))},98418:(e,t,r)=>{"use strict";r.d(t,{C:()=>i});var n=r(13072),o=r(25182),s=r(39068),i=e=>{let{present:t,children:r}=e,i=function(e){var t,r;let[o,i]=n.useState(),d=n.useRef({}),u=n.useRef(e),l=n.useRef("none"),[c,p]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=a(d.current);l.current="mounted"===c?e:"none"},[c]),(0,s.N)(()=>{let t=d.current,r=u.current;if(r!==e){let n=l.current,o=a(t);e?p("MOUNT"):"none"===o||t?.display==="none"?p("UNMOUNT"):r&&n!==o?p("ANIMATION_OUT"):p("UNMOUNT"),u.current=e}},[e,p]),(0,s.N)(()=>{if(o){let e;let t=o.ownerDocument.defaultView??window,r=r=>{let n=a(d.current).includes(r.animationName);if(r.target===o&&n&&(p("ANIMATION_END"),!u.current)){let r=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=r)})}},n=e=>{e.target===o&&(l.current=a(d.current))};return o.addEventListener("animationstart",n),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",n),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}p("ANIMATION_END")},[o,p]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:n.useCallback(e=>{e&&(d.current=getComputedStyle(e)),i(e)},[])}}(t),d="function"==typeof r?r({present:i.isPresent}):n.Children.only(r),u=(0,o.s)(i.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(d));return"function"==typeof r||i.isPresent?n.cloneElement(d,{ref:u}):null};function a(e){return e?.animationName||"none"}i.displayName="Presence"}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[557,273,403,823,748,801],()=>r(29771));module.exports=n})();