# Decision Log

This file records architectural and implementation decisions using a list format.
2025-06-10 15:10:54 - Log of updates made.

*
---
---
### Decision (Bug Fix)
[2025-06-17 13:39:00] - [Bug Fix: 通过将绘图函数移入useEffect彻底解决SSR构建失败问题]

**Rationale:**
*   **问题:** 即便在将 `dpr` 作为参数传递后，`next build` 命令仍然失败。
*   **根本原因:** 问题的根源在于，任何在组件顶层作用域内定义的、包含了浏览器特有API（如 `document.createElement`）的辅助函数，都可能在Next.js的服务器端构建过程中被分析和评估，从而导致构建失败，即使这些函数只在客户端的 `useEffect` 中被调用。
*   **最终解决方案:** 将所有包含浏览器特有API的绘图辅助函数（`getShapeCenter`, `drawMtfPattern`, `drawShape`）的定义完全移入到 `Canvas.tsx` 的 `useEffect` hook 内部。

**Implementation Details:**
*   **文件:** [`hotel-dashboard/components/drawing-board/Canvas.tsx`](hotel-dashboard/components/drawing-board/Canvas.tsx)
*   **更改:**
    1.  **函数迁移:** `getShapeCenter`, `drawMtfPattern`, 和 `drawShape` 三个函数的完整定义被从组件的顶层作用域剪切并粘贴到了 `useEffect` hook 的内部。
    2.  **作用域:** 这确保了这些函数及其对 `document.createElement` 的调用只在客户端的 `useEffect` hook 的上下文中存在和执行，完全避免了服务器端对它们的任何接触。
    3.  **依赖复制:** 由于 `findShapeAt` 函数（由事件处理器调用，不能移入 `useEffect`）依赖 `getShapeCenter` 的逻辑，因此将计算中心点的逻辑复制到了 `findShapeAt` 内部，以解除其对现在位于 `useEffect` 内部的函数的依赖。
    4.  这个重构是解决此类SSR问题的最稳健的方法，它保证了服务器端构建环境的纯净性。
---
### Decision (Feature Enhancement)
[2025-06-17 13:21:00] - [Enhancement: 完善二值化逻辑以处理白色背景]

**Rationale:**
*   **用户反馈:** 用户要求更新二值化逻辑，使灰度值低于200的像素变为黑色，高于或等于200的像素变为白色。
*   **问题:** 之前的实现只处理了转黑色的情况，未处理其他像素，这可能导致背景或浅色区域保留原始的、未经处理的颜色。
*   **解决方案:** 在二值化函数中添加 `else` 分支，将所有不符合“转黑”条件的像素强制转换为纯白色 `(255, 255, 255)`。

**Implementation Details:**
*   **文件:** [`hotel-dashboard/components/drawing-board/DrawingBoard.tsx`](hotel-dashboard/components/drawing-board/DrawingBoard.tsx)
*   **更改:**
    *   在 `exportCanvas` 函数内的 `applyBinarizationToContext` 辅助函数中，修改了像素处理循环：
        ```javascript
        if (grayscale < 200) {
          // ... set to black
        } else {
          data[i] = 255;   // R
          data[i + 1] = 255; // G
          data[i + 2] = 255; // B
        }
        ```
---
### Decision (Bug Fix & Feature)
[2025-06-17 13:17:00] - [Fix: 将二值化逻辑移至导出功能以解决无效问题]

**Rationale:**
*   **问题:** 用户反馈之前实现的“二值化”开关对导出的图片不起作用。
*   **根本原因:** `exportCanvas` 函数在一个临时的、内存中的画布上重新绘制所有图形，完全绕过了在屏幕上对可见画布进行的二值化后处理。
*   **解决方案:** 将二值化逻辑直接集成到 `exportCanvas` 函数中。这样可以确保在生成最终的导出图片之前，对临时画布上的像素进行处理。同时，重新引入UI开关，使其专门控制*导出时*是否应用二值化。

**Implementation Details:**
*   **文件:**
    *   [`hotel-dashboard/components/drawing-board/DrawingBoard.tsx`](hotel-dashboard/components/drawing-board/DrawingBoard.tsx)
    *   [`hotel-dashboard/components/drawing-board/Toolbar.tsx`](hotel-dashboard/components/drawing-board/Toolbar.tsx)
*   **撤销与重构:**
    1.  **撤销 `Canvas.tsx` 的更改:** 移除了 `Canvas.tsx` 中的 `binarizationEnabled` prop 和 `applyBinarization` 函数，使其恢复到只负责绘图的单一职责。
    2.  **状态管理 (`DrawingBoard.tsx`):**
        *   重新引入了 `const [binarizationEnabled, setBinarizationEnabled] = useState(true);` 状态，默认为 `true` 以满足用户需求。
        *   将状态和设置器传递给 `Toolbar`。
    3.  **导出逻辑 (`DrawingBoard.tsx`):**
        *   在 `exportCanvas` 函数内部，创建了一个 `applyBinarizationToContext` 辅助函数。
        *   在所有图形都绘制到临时画布上之后，增加了一个条件块：`if (binarizationEnabled) { applyBinarizationToContext(ctx); }`。
    4.  **UI 开关 (`Toolbar.tsx`):**
        *   重新引入了 `Switch` 组件，标签更新为“导出时二值化”，以明确其功能范围。
---
### Decision (Bug Fix)
[2025-06-17 12:12:00] - [Bug Fix: 通过处理设备像素比(DPR)彻底解决Canvas绘图模糊问题]

**Rationale:**
*   **问题:** 即便在禁用了图像平滑并对坐标取整后，绘制的图形（圆形和MTF图案）在高DPI屏幕上仍然存在灰色和模糊的边缘。
*   **根本原因:** 浏览器在高DPI（高像素密度）屏幕上会拉伸Canvas的位图，导致模糊。根本的解决方法是让Canvas的后台缓冲区拥有与设备物理像素匹配的分辨率。
*   **修复目标:** 在所有屏幕（特别是高DPI屏幕）上实现像素级的清晰锐利渲染。

**Implementation Details:**
*   **文件:** [`hotel-dashboard/components/drawing-board/Canvas.tsx`](hotel-dashboard/components/drawing-board/Canvas.tsx)
*   **核心更改 (DPR Scaling):**
    1.  **主画布缩放 (在 `useEffect` 中):**
        *   获取 `const dpr = window.devicePixelRatio || 1;`。
        *   设置画布的物理（内存）尺寸：`canvas.width = width * dpr;` 和 `canvas.height = height * dpr;`。
        *   使用CSS设置画布的显示（逻辑）尺寸：`canvas.style.width = \`\${width}px\`;` 和 `canvas.style.height = \`\${height}px\`;`。
        *   缩放整个2D绘图上下文：`ctx.scale(dpr, dpr);`。这样，所有后续的绘图命令（如 `fillRect`, `arc`）都将使用逻辑坐标，但会被渲染到更高分辨率的缓冲区上。
    2.  **临时画布缩放 (在 `drawMtfPattern` 中):**
        *   对用于预渲染MTF图案的临时画布也应用了相同的DPR缩放逻辑，以确保图案本身是高分辨率的，避免将低分辨率图像绘制到高分辨率主画布上。
    3.  **圆形绘制调整:**
        *   由于整个上下文已被缩放，之前为解决亚像素问题而添加的 `Math.round()` 已不再必要，因此被移除，以避免潜在的精度损失。
        *   保留了 `ctx.imageSmoothingEnabled = false;` 作为确保最锐利边缘的最佳实践。
---
---
### Decision (Bug Fix)
[2025-06-13 18:05:00] - [Bug Fix: 修复MTF图案渲染超出边界的问题]

**Rationale:**
*   **问题:** 当前绘制的 MTF 图案的线条可能会超出其指定的尺寸边界（例如，一个 200x200 的图案可能会画到 200x200 区域之外）。
*   **修复目标:** 使用 Canvas 的剪切（clipping）功能来严格约束绘制区域，确保所有线条都在指定的边界框内。

**Implementation Details:**
*   **文件:** [`hotel-dashboard/components/drawing-board/Canvas.tsx`](hotel-dashboard/components/drawing-board/Canvas.tsx)
*   **更改:**
    1.  在 `drawMtfPattern` 函数的开头，调用 `ctx.save()` 保存当前画布状态。
    2.  使用 `ctx.beginPath()` 和 `ctx.rect(x, y, size, size)` 创建一个与图案边界完全匹配的矩形路径。
    3.  调用 `ctx.clip()` 将后续的所有绘制操作限制在该矩形区域内。
    4.  在函数的所有绘制操作完成后，调用 `ctx.restore()` 来移除剪切区域，恢复画布的原始状态。
---
### Decision (Bug Fix)
[2025-06-13 18:02:00] - [Bug Fix: 修复MTF图案渲染循环停止条件以完全填充象限]

**Rationale:**
*   **问题:** 当前 MTF 图案的渲染不正确，问题出在 `for` 循环的停止条件上，导致线条没有完全填满各自的象限，与最新的 Python 参考代码不符。
*   **修复目标:** 严格按照最新的 Python 参考代码更新 `drawMtfPattern` 函数中所有四个象限的 `for` 循环的停止条件，并移除多余的中心线绘制逻辑。

**Implementation Details:**
*   **文件:** [`hotel-dashboard/components/drawing-board/Canvas.tsx`](hotel-dashboard/components/drawing-board/Canvas.tsx)
*   **更改:**
    1.  **移除中心十字线:** 删除了在 `drawMtfPattern` 函数中单独绘制中心十字线的 `fillRect` 调用。
    2.  **更新循环停止条件:**
        *   左上角和右上角的循环条件从 `> rX` 或 `> rY` 更新为 `> -lineWidth`。
        *   左下角和右下角的循环条件从 `< rY + rSize` 或 `< rX + rSize` 更新为 `< rY + rSize + lineWidth` 或 `< rX + rSize + lineWidth`。
    3.  这些更改确保了线条能够一直绘制到或稍微超出图案的边界，从而完全填充象限并自然形成中心十字，与 Python 参考代码的行为保持一致。
---
### Decision (Bug Fix)
[2025-06-13 17:56:00] - [Bug Fix: 修复MTF图案渲染逻辑以匹配Python参考]

**Rationale:**
*   **问题:** 当前绘制的 MTF 图案与用户提供的标准 Python 参考代码和图像不完全一致。具体差异在于中心十字线的形成方式以及四个象限中线条的精确绘制范围。
*   **修复目标:** 严格按照 Python 参考代码重写 `drawMtfPattern` 函数，以确保渲染结果的像素级一致性。

**Implementation Details:**
*   **文件:** [`hotel-dashboard/components/drawing-board/Canvas.tsx`](hotel-dashboard/components/drawing-board/Canvas.tsx)
*   **更改:**
    1.  **移除显式中心十字线:** 删除了单独绘制贯穿整个图案的水平和垂直中心线的代码。
    2.  **遵循Python逻辑:** 修改了所有四个象限的绘制逻辑，使其线条从图案的边缘一直绘制到中心线，从而自然地形成中心十字，这与Python参考代码的行为一致。
    3.  **修正循环和坐标:** 仔细检查并修正了所有四个象限的 `for` 循环的起始值、结束条件和步长，以及 `fillRect` 的坐标和尺寸参数，使其与Python的 `range()` 和 `draw.rectangle()` 逻辑精确对应。特别是修正了左下角图案的起始 `y` 坐标。
    4.  **确保整数坐标:** 在最终的 `fillRect` 调用中继续使用 `Math.round()`，以避免亚像素渲染问题。
### Decision (Bug Fix)
[2025-06-13 17:48:00] - [Bug Fix: 修复MTF图案渲染中的亚像素模糊问题]

**Rationale:**
*   **问题:** 即使禁用了 `imageSmoothingEnabled`，绘制的MTF图案的竖线边缘仍然有灰色的模糊像素。
*   **根本原因:** 传递给 `ctx.fillRect()` 的坐标和尺寸是浮点数，导致了亚像素渲染，从而产生模糊效果。

**Implementation Details:**
*   **文件:** [`hotel-dashboard/components/drawing-board/Canvas.tsx`](hotel-dashboard/components/drawing-board/Canvas.tsx)
*   **更改:**
    *   在 `drawMtfPattern` 函数中，对所有传递给 `ctx.fillRect()` 的参数（x, y, width, height）都使用了 `Math.round()` 进行处理。
    *   这确保了所有的绘制操作都与像素网格对齐，从而消除了亚像素渲染导致的模糊，生成了清晰的线条。
### Decision (Bug Fix)
[2025-06-13 17:40:00] - [Bug Fix: 修复MTF图案生成中的定位与抗锯齿问题]

**Rationale:**
*   **定位错误:** 生成的MTF图案中，左下角区域的第一条黑线距离中心的水平线有3个像素的间距，而预期的间距应该是2像素。
*   **边缘模糊:** 放大查看MTF图案时，黑白线条的边缘存在灰色的模糊像素，这是由于抗锯齿（image smoothing）导致的。

**Implementation Details:**
*   **文件:** [`hotel-dashboard/components/drawing-board/Canvas.tsx`](hotel-dashboard/components/drawing-board/Canvas.tsx)
*   **更改:**
    *   **定位修复:** 在 `drawMtfPattern` 函数中，将绘制左下角水平线的 `for` 循环的起始值从 `center_y + lineWidth + 2` 修正为 `center_y + lineWidth / 2 + 2`。这确保了第一条黑线的上边缘与中心水平线的下边缘之间有精确的2像素间距。
    *   **抗锯齿禁用:** 在 `drawMtfPattern` 函数的开头添加了 `ctx.imageSmoothingEnabled = false;`，以禁用抗锯齿，从而生成像素分明、边缘清晰的线条。

---
### Decision (Bug Fix)
[2025-06-13 17:34:00] - [Bug Fix: "画图板"导出的图片中MTF图案显示不正确]

**Rationale:**
*   **问题:** 当画布上绘制了新的“MTF 图案”后，导出的图片仍然显示为旧的纯色方块，而不是正确的 MTF 图案。
*   **根本原因:** 在 `hotel-dashboard/components/drawing-board/DrawingBoard.tsx` 的 `exportCanvas` 函数中，用于在临时画布上重新绘制图形的逻辑没有更新。它仍然在使用旧的 `ctx.rect()` 来绘制方形，而不是调用与屏幕显示逻辑一致的 `drawMtfPattern` 函数。

**Implementation Details:**
*   **文件:** [`hotel-dashboard/components/drawing-board/DrawingBoard.tsx`](hotel-dashboard/components/drawing-board/DrawingBoard.tsx)
*   **更改:**
    *   将 `Canvas.tsx` 中的 `drawMtfPattern` 函数的实现复制到了 `exportCanvas` 函数的本地作用域中。
    *   修改了 `exportCanvas` 中遍历 `shapes` 的循环，当 `shape.type` 为 `'square'` 时，调用这个本地的 `drawMtfPattern` 函数来绘制图形，而不是原来的 `ctx.rect()`。
    *   这确保了导出时使用的绘图逻辑与屏幕上显示的绘图逻辑完全一致。
---
### Decision (Bug Fix)
[2025-06-13 17:18:44] - [Bug Fix: "画图板"导出图片背景色不正确]

**Rationale:**
*   **问题:** 当用户使用“替换颜色”功能更改了画布的背景色后，导出的图片仍然是白色背景，没有反映出新的背景颜色。
*   **根本原因:** 在 `hotel-dashboard/components/drawing-board/DrawingBoard.tsx` 的 `exportCanvas` 函数中，用于填充临时画布背景的颜色被硬编码为 `'#FFFFFF'`，而不是使用当前的 `backgroundColor` 状态。

**Implementation Details:**
*   **文件:** [`hotel-dashboard/components/drawing-board/DrawingBoard.tsx`](hotel-dashboard/components/drawing-board/DrawingBoard.tsx)
*   **更改:**
    *   在 `exportCanvas` 函数中，将 `ctx.fillStyle = '#FFFFFF';` 修改为 `ctx.fillStyle = backgroundColor;`，以确保导出时使用正确的背景颜色。
---
### Decision (Bug Fix)
[2025-06-13 16:24:00] - [Bug Fix: "画图板"功能运行时错误修复]

**Rationale:**
*   **运行时错误:** 在 `hotel-dashboard/components/drawing-board/DrawingBoard.tsx` 中，代码仍然尝试向 `Toolbar` 和 `Canvas` 组件传递 `backgroundColor` 和 `setBackgroundColor` props，但其对应的 `useState` 已在之前的代码清理中被移除，导致 `ReferenceError: backgroundColor is not defined`。
*   **代码一致性:** 确保组件的 props 与其父组件的状态保持一致。

**Implementation Details:**
*   **文件:** [`hotel-dashboard/components/drawing-board/DrawingBoard.tsx`](hotel-dashboard/components/drawing-board/DrawingBoard.tsx)
*   **更改:**
    *   从传递给 `Toolbar` 组件的 props 中移除了 `backgroundColor={backgroundColor}` 和 `setBackgroundColor={setBackgroundColor}`。
    *   从传递给 `Canvas` 组件的 props 中移除了 `backgroundColor={backgroundColor}`。
---
### Decision (Code Simplification)
[2025-06-13 16:17:00] - [Simplification: "画图板"功能简化]

**Rationale:**
*   **用户需求:** 根据用户请求，对“画图板”功能进行最终简化，移除背景颜色选择功能，以简化UI和状态管理。
*   **代码简化:** 移除不再需要的状态和 props，可以使代码更清晰、更易于维护。

**Implementation Details:**
*   **移除背景颜色选择器:**
    *   **文件:** [`hotel-dashboard/components/drawing-board/Toolbar.tsx`](hotel-dashboard/components/drawing-board/Toolbar.tsx)
    *   **更改:** 完全移除了“画布背景”颜色选择器 input 及其关联的 `Label`。同时，从 `ToolbarProps` 接口和组件的 props 解构中删除了 `backgroundColor` 和 `setBackgroundColor`。
*   **移除背景颜色状态:**
    *   **文件:** [`hotel-dashboard/components/drawing-board/DrawingBoard.tsx`](hotel-dashboard/components/drawing-board/DrawingBoard.tsx)
    *   **更改:** 移除了 `backgroundColor` 的 `useState`。在 `exportCanvas` 函数中，背景色被硬编码为 `'#FFFFFF'`。传递给 `Toolbar` 和 `Canvas` 的相关 props 也被移除。
*   **固定画布背景色:**
    *   **文件:** [`hotel-dashboard/components/drawing-board/Canvas.tsx`](hotel-dashboard/components/drawing-board/Canvas.tsx)
    *   **更改:**
        1.  从 `CanvasProps` 接口和组件的 props 解构中移除了 `backgroundColor`。
        2.  在 `useEffect` hook 中，画布的 `fillStyle` 被硬编码为 `'#FFFFFF'`。
        3.  `drawGrid` 函数不再接收 `backgroundColor` 参数，其网格线颜色 `strokeStyle` 被固定为 `'rgba(0, 0, 0, 0.5)'`，不再需要动态计算。
---
### Decision (Feature Enhancement)
[2025-06-13 16:06:00] - [Feature Enhancement: "画图板"高级功能增强]

**Rationale:**
*   **用户需求:** 增强“画图板”功能，包括全局颜色替换、动态网格线颜色和单元格尺寸显示，以提高其专业性和可用性。
*   **像素级操作:** 为了实现全局颜色替换（包括背景），需要从状态驱动的绘图转向直接的像素操作。
*   **UI/UX 优化:** 动态网格线和单元格尺寸显示为用户提供了更好的视觉反馈和精确性。

**Implementation Details:**
*   **全局颜色替换:**
    *   **文件:** [`hotel-dashboard/components/drawing-board/DrawingBoard.tsx`](hotel-dashboard/components/drawing-board/DrawingBoard.tsx)
    *   **更改:** `replaceColor` 函数被重写。现在它使用 `canvas.getContext('2d').getImageData()` 读取整个画布的像素。通过遍历像素数据，将匹配的旧颜色（RGB）替换为新颜色，然后使用 `putImageData()` 将结果写回画布。同时保留了对 `shapes` 状态的更新，以确保后续重绘的持久性。
*   **动态网格线颜色:**
    *   **文件:** [`hotel-dashboard/components/drawing-board/Canvas.tsx`](hotel-dashboard/components/drawing-board/Canvas.tsx)
    *   **更改:**
        1.  添加了 `backgroundColor` 状态，并将其从 `DrawingBoard` 传递到 `Canvas` 和 `Toolbar`。
        2.  `drawGrid` 函数现在包含一个 `isColorDark` 的辅助函数，用于计算背景色的亮度。
        3.  根据背景色是深色还是浅色，网格线的 `strokeStyle` 被动态设置为半透明的白色或黑色。
        4.  画布的背景填充现在也使用 `backgroundColor` 状态。
*   **显示单元格尺寸:**
    *   **文件:** [`hotel-dashboard/components/drawing-board/Toolbar.tsx`](hotel-dashboard/components/drawing-board/Toolbar.tsx)
    *   **更改:**
        1.  `Toolbar` 现在从 `DrawingBoard`接收 `canvasSize` prop。
        2.  使用 `React.useEffect` hook 监听 `canvasSize` 和 `grid` 的变化。
        3.  当这些值变化时，计算出每个单元格的精确宽度和高度。
        4.  结果以 `宽: xxx.xx px, 高: yyy.yy px` 的格式显示在工具栏中。

---
### Decision (Bug Fix)
[2025-06-13 15:22:00] - [Bug Fix: "画图板"功能修复]

**Rationale:**
*   **运行时错误:** `Canvas.tsx` 组件在处理画布交互时，错误地引用了未定义的 `canvasRef`，导致 `ReferenceError`。
*   **UI 布局问题:** `Toolbar` 组件在页面上显示不正确，过于靠右，原因是其父容器在页面布局中被不必要地限制了宽度并居中显示。

**Implementation Details:**
*   **`ReferenceError` 修复:**
    *   **文件:** [`hotel-dashboard/components/drawing-board/Canvas.tsx`](hotel-dashboard/components/drawing-board/Canvas.tsx)
    *   **更改:** 在 `handleCanvasInteraction` 函数中，将对 `canvasRef.current` 的引用更正为 `internalCanvasRef.current`，与组件内部定义的 ref 保持一致。
*   **工具栏布局修复:**
    *   **文件:** [`hotel-dashboard/app/(dashboard)/drawing-board/page.tsx`](hotel-dashboard/app/(dashboard)/drawing-board/page.tsx)
    *   **更改:** 移除了包裹 `<DrawingBoard />` 组件的多余 `div` 元素 (`<div class="w-[90%] h-[90%] mx-auto my-auto">`)。这使得 `DrawingBoard` 组件可以扩展到其父容器 (`<div class="flex-grow w-full h-full">`) 的全部宽度，从而解决了工具栏的对齐问题。
---
### Decision (Architecture)
[2025-06-13 14:53:00] - [Architecture: "画图"功能实现]

**Rationale:**
*   **用户需求:** 实现一个全新的“画图”功能，允许用户创建自定义尺寸的画板，绘制网格，并在网格单元中放置和对齐图形，同时支持颜色操作。
*   **模块化设计:** 将功能拆分为独立的React组件，以提高可维护性和代码复用性。`DrawingBoard.tsx` 作为协调器，`Canvas.tsx` 负责渲染，`Toolbar.tsx` 提供用户交互控件。
*   **技术选型:** 使用标准的HTML5 Canvas API进行2D绘图，因为它性能良好且浏览器支持广泛。利用React的状态管理（`useState`）来驱动画布的重新渲染。

**Implementation Details:**
*   **新页面:** 在 `hotel-dashboard/app/(dashboard)/drawing-board/page.tsx` 创建新页面。
*   **组件结构:**
    *   `hotel-dashboard/components/drawing-board/DrawingBoard.tsx`: 包含所有状态管理和核心逻辑，将 props 传递给子组件。
    *   `hotel-dashboard/components/drawing-board/Canvas.tsx`: 使用 `useRef` 访问 canvas 元素，并在 `useEffect` hook 中根据 props (尺寸, 网格, 图形) 的变化来重绘。
    *   `hotel-dashboard/components/drawing-board/Toolbar.tsx`: 包含用于修改画布尺寸、网格、图形颜色、直径等设置的输入控件。
*   **类型定义:** 在 `hotel-dashboard/types/drawing-board.ts` 中定义了共享的类型，如 `Shape`, `Alignment`, `ShapeType`。
*   **数据流:**
    1.  用户在 `Toolbar` 中修改设置。
    2.  `Toolbar` 通过回调函数更新 `DrawingBoard` 中的状态。
    3.  `DrawingBoard` 将更新后的状态作为 props 传递给 `Canvas`。
    4.  `Canvas` 的 `useEffect` hook 检测到 props 变化，触发画布重绘。
    5.  用户点击 `Canvas`，触发 `addShape` 回调，更新 `DrawingBoard` 中的 `shapes` 状态，导致重绘。
*   **集成:** 在 `hotel-dashboard/dashboard.tsx` 的侧边栏中添加了指向新页面的链接。
---
### Decision (Architecture)
[2025-06-12 14:06:00] - [Architecture: C# Wrapper for DLL Invocation]

**Rationale:**
*   **Dependency Isolation:** The core logic is encapsulated within a provided DLL. Creating a C# console application wrapper allows Node.js to interact with it through a clean command-line interface, without requiring native Node.js addons or complex inter-process communication (IPC) setups. This isolates the .NET dependencies from the Node.js environment.
*   **Simplified Interoperability:** Invoking a console application from Node.js (via `child_process`) is a well-understood and straightforward pattern for interoperability between different technology stacks. It avoids the complexities and potential brittleness of direct bindings (e.g., using `edge-js`).
*   **Maintainability:** The C# wrapper can be developed, tested, and maintained independently of the Next.js application.

**Implementation Details:**
*   **C# Project:** A new C# console application project (`WrapperApp`) is created in the `csharp-wrapper/` directory.
*   **Interface:** The wrapper accepts command-line arguments (`sn`, `featureCode`).
*   **Node.js API:** A new API route (`/api/feature/update`) in the Next.js application is responsible for receiving requests from the frontend and executing the C# wrapper executable.
*   **Data Flow:** Frontend Form -> Next.js API Route -> C# Wrapper -> DLL.
---
### Decision (Debug)
[2025-06-12 13:11:00] - [Bug Fix Strategy: 修复预览API中ZIP处理的TypeError]

**Rationale:**
在 `app/api/surface-data/preview/route.ts` 的 `handleZipDecompression` 函数中，使用 `new StreamZip.async({ buffer: fileBuffer })` 直接从内存缓冲区初始化 `node-stream-zip` 存在类型不兼容问题，并可能在某些环境下导致 `TypeError: The "path" argument must be of type string...` 的运行时错误。正确的做法是为库提供一个文件路径。

**Details:**
*   **Affected File:** [`hotel-dashboard/app/api/surface-data/preview/route.ts`](hotel-dashboard/app/api/surface-data/preview/route.ts)
*   **Fix Implemented:**
    1.  **临时文件写入:** 将从FTP下载的 `fileBuffer` 写入到一个在临时目录 (`os.tmpdir()`) 中生成的唯一命名的ZIP文件（使用 `uuid`）。
    2.  **路径初始化:** 使用这个临时文件的路径来初始化 `node-stream-zip`，即 `new StreamZip.async({ file: tmpZipPath })`。
    3.  **依赖添加:** 在 `hotel-dashboard` 项目中添加 `uuid` 及其类型定义 `@types/uuid`。
    4.  **清理临时文件:** 在 `finally` 块中添加了健壮的清理逻辑，确保无论解压成功与否，都会尝试删除临时文件 (`fs.unlink`)，避免临时文件堆积。
*   **Expected Impact:** 解决了 `TypeError` 运行时错误，使ZIP文件预览功能更加稳定和可靠。
---
### Decision (Performance Optimization)
[2025-06-12 12:16:00] - 面形数据预览API性能优化策略

**Rationale:**
*   **响应时间:** 原始的预览API在处理大型点云文件（尤其是压缩文件）时，可能会因完整解析和渲染所有数据点而导致响应时间过长，甚至超时。
*   **资源消耗:** 在服务器上处理数百万个点会消耗大量CPU和内存，影响服务器的整体性能和可扩展性。
*   **I/O瓶颈:** 对于ZIP文件，将缓冲区写入临时文件再进行解压会引入不必要的磁盘I/O，成为性能瓶颈。

**Implementation Details:**
*   **数据采样:**
   *   **Affected File:** [`hotel-dashboard/utils/pointCloudUtils.ts`](hotel-dashboard/utils/pointCloudUtils.ts)
   *   **Change:** 修改 `parsePointCloudData` 函数，增加一个可选的 `options` 参数，允许指定 `maxPoints`。如果输入数据的点数超过 `maxPoints`，则进行均匀采样，只解析和返回一部分数据点。
   *   **API Integration:** 在 `/api/surface-data/preview` 中调用 `parsePointCloudData` 时，设置 `maxPoints` 为 `150,000`，确保用于预览的点数在一个合理的范围内。
*   **内存中解压:**
   *   **Affected File:** [`hotel-dashboard/app/api/surface-data/preview/route.ts`](hotel-dashboard/app/api/surface-data/preview/route.ts)
   *   **Change:** 重构了ZIP文件处理逻辑。不再将FTP下载的 `Buffer` 写入临时文件，而是直接使用 `node-stream-zip` 从内存 `Buffer` 中创建实例并解压。
   *   **Impact:** 消除了磁盘I/O，减少了延迟，并简化了代码（无需处理临时文件的创建和删除）。
*   **代码结构优化:**
   *   **Affected File:** [`hotel-dashboard/app/api/surface-data/preview/route.ts`](hotel-dashboard/app/api/surface-data/preview/route.ts)
   *   **Change:** 将API路由处理函数分解为更小的、单一职责的辅助函数（如 `getPointCloudData`, `generatePreviewImage`, `handleZipDecompression`），提高了代码的可读性和可维护性。

---
### Decision (UI/UX Enhancement)
[2025-06-12 12:16:00] - 为点云预览引入交互式缩放和平移功能

**Rationale:**
*   **用户体验:** 静态的预览图对于观察点云细节（如局部异常或特征）作用有限。提供缩放和平移功能可以极大地提升用户体验和预览的实用性。
*   **技术选型:** `react-zoom-pan-pinch` 是一个轻量级、功能强大且易于集成的库，专门用于为React组件添加平移和缩放功能，非常适合此场景。

**Implementation Details:**
*   **Affected File:** [`hotel-dashboard/components/surface-data-query/PointCloudPreviewModal.tsx`](hotel-dashboard/components/surface-data-query/PointCloudPreviewModal.tsx)
*   **Dependency:** 添加了 `react-zoom-pan-pinch` 库。
*   **Integration:**
   *   将原有的 `<img>` 标签包裹在 `TransformWrapper` 和 `TransformComponent` 组件中。
   *   `TransformWrapper` 负责处理所有的鼠标和触摸事件，以实现平移和缩放。
   *   添加了一个浮动的控件面板，包含“放大”、“缩小”和“重置视图”的按钮，这些按钮通过 `TransformWrapper` 提供的 `utils` 函数来控制视图。
   *   使用 `useRef` 来获取 `TransformWrapper` 的实例，以便在模态框关闭或重新加载数据时可以调用 `resetTransform()` 方法，确保UI状态的一致性。
*   **TypeScript支持:**
   *   由于 `react-zoom-pan-pinch` 可能缺少官方的类型声明，创建了自定义的声明文件 (`hotel-dashboard/types/react-zoom-pan-pinch.d.ts`) 来提供必要的类型定义，解决了TypeScript编译错误。

---
### Decision (Architecture Design)
[2025-06-12 11:35:41] - 面形数据预览功能技术架构设计

**Rationale:**
*   **用户体验提升:** 在下载完整数据前提供快速的点云数据俯视图预览。
*   **性能优化:** 后端处理解压、解析和渲染，前端轻量化展示。
*   **集成与复用:** 利用现有FTP客户端和UI组件。
*   **算法移植:** 将C#中的点云可视化逻辑移植到Web环境。

**Implementation Details:**
*   **组件划分:**
    *   **前端:** [`PointCloudPreviewModal.tsx`](hotel-dashboard/components/surface-data-query/PointCloudPreviewModal.tsx) (新增), [`ResultsDisplay.tsx`](hotel-dashboard/components/surface-data-query/ResultsDisplay.tsx) (修改), [`pointCloudUtils.ts`](hotel-dashboard/lib/pointCloudUtils.ts) (新增)。
    *   **API层:** 新增 `/api/surface-data/preview` 端点 (Next.js API Route)。
    *   **数据处理层 (后端):** 包含解压缩、XYZ点云解析、俯视图渲染算法（移植自[`Form1.cs`](Form1.cs)）。
*   **数据流:** 用户点击预览 -> 前端请求API -> 后端下载、解压、解析、渲染 -> 后端返回图像数据 -> 前端Canvas显示。
*   **API设计 (`/api/surface-data/preview`):**
    *   **请求 (POST):** `{ "filePath": "string" }`
    *   **响应 (成功):** `{ "imageData": "base64_string" | "pixel_array", "imageType": "string", "width": number, "height": number, "originalBounds": object }`
    *   **响应 (失败):** `{ "error": "string" }`
*   **关键技术决策:**
    *   **后端渲染俯视图:** 避免大文件传输，利用服务器计算能力。
    *   **解压缩:** 根据文件类型选择Node.js `zlib`或`unzipper`/`pako`。
    *   **渲染算法移植:** 将[`Form1.cs`](Form1.cs)中的2D投影和画布映射逻辑用TypeScript/JavaScript实现，可使用`node-canvas`或手动操作像素数组。
*   **Memory Bank 更新:**
    *   [`productContext.md`](memory-bank/productContext.md): 添加新功能描述。
    *   [`decisionLog.md`](memory-bank/decisionLog.md): 记录此架构决策。
    *   相关开发和用户文档将进行更新。
## Decision

*   [2025-06-10 15:11:26] - 确定将 `web_tool` 日志分析功能集成到 `hotel-dashboard` 的系统架构。采用基于 React 组件（`LogFileUpload`, `LogDisplayArea`, `LogChartView`）和 Web Worker (`logParser.worker.ts`) 的设计。

**Rationale:**
*   组件化设计易于管理和维护。
*   Web Worker 用于日志解析，避免阻塞 UI 主线程，提升用户体验。
*   利用 `hotel-dashboard` 现有的 Next.js 和 shadcn/ui 基础。

**Implementation Details:**
*   **目录结构:**
    *   `components/log-analysis/` 存放新的 React 组件。
    *   `app/(dashboard)/log-analysis/page.tsx` 作为日志分析主页面。
    *   `workers/logParser.worker.ts` (或 `public/workers/`) 存放 Web Worker。
*   **数据流:** 用户上传 -> `LogFileUpload` -> `logParser.worker` -> `LogDisplayArea` -> `LogChartView`。
*   **集成点:** 修改 `components/sidebar.tsx` 添加导航链接；`app/(dashboard)/layout.tsx` 承载整体布局。
*   **状态管理:** 初步考虑使用 React Context API，可根据后续开发调整。
---
### Decision (Debug)
[2025-06-11 17:39:00] - [Bug Fix Strategy: 深度修复Recharts图表尺寸问题]

**Rationale:**
尽管之前设置了minWidth/minHeight，Recharts图表尺寸警告仍然存在。根据Memory Bank中的历史调试信息，问题的根本原因是Y轴domain计算导致的。当domain为`[0, 0]`时，Recharts无法正确计算图表的内部尺寸，即使外部容器有尺寸。

**Details:**
*   **Root Cause Analysis:** 
    *   当没有数据时，`glueDomain`和`collimationDomain`都被设置为`[0, 0]`
    *   `timeDomain`也被设置为`[0, 0]`
    *   这些无效的domain导致Recharts内部计算出0尺寸的图表区域
*   **Comprehensive Fix (`LogChartView.tsx`):**
    1.  **Domain默认值修复:**
        *   `glueDomain`: 从`[0, 0]`改为`[0, 1000]` (胶厚的合理默认范围)
        *   `collimationDomain`: 从`[0, 0]`改为`[0, 0.1]` (准直差的合理默认范围)  
        *   `timeDomain`: 从`[0, 0]`改为`[Date.now() - 3600000, Date.now()]` (默认1小时时间范围)
    2.  **Y轴类型明确化:**
        *   为两个Y轴都添加了`type="number"`属性，确保Recharts正确处理数值域
*   **Expected Impact:**
    *   解决"width(0) and height(0) of chart should be greater than 0"警告
    *   确保即使在没有数据时图表也能正确渲染占位符
    *   提高图表渲染的稳定性和可预测性

---
---
### Decision (Debug)
[2025-06-11 17:53:00] - [Bug Fix Strategy: 修复图表显示和宽度问题]

**Rationale:**
用户反馈发现了真正的问题：1) 点击"显示"不会显示图表，只有点击"导出图片"才显示，2) 图表宽度没有填满容器。这表明问题不在Recharts尺寸警告，而在于Card的显示逻辑和图表的响应式布局。

**Details:**
*   **Root Cause Analysis:**
    *   Card的className逻辑问题：使用了错误的显示/隐藏逻辑
    *   图表宽度问题：使用固定宽度800px而不是响应式宽度
*   **Fixes Implemented:**
    1.  **Card显示逻辑修复:**
        *   恢复正确的className: `selectedBlockIds.includes(chunk.block_id) ? 'block' : 'hidden'`
        *   移除了错误的内联style显示逻辑
    2.  **图表宽度修复:**
        *   恢复ResponsiveContainer以实现100%宽度填充
        *   移除固定的width={800}，改为响应式布局
        *   保持height={400}的容器高度约束
*   **Expected Result:**
    *   选中数据块时图表立即显示
    *   图表宽度完全填满容器
    *   保持所有之前修复的有效性（domain默认值、编码检测等）
### Decision (Code)
[2025-06-10 15:59:06] - 日志分析图表 (`LogChartView.tsx`) 数据处理与可视化策略

**Rationale:**
确保图表能够准确、清晰地展示来自一个或多个日志数据块的胶厚、准直数据及相关事件，同时保证时间轴的正确性和可读性。

**Details:**
*   **时间戳统一处理:**
   *   所有从日志解析出的字符串时间戳 (e.g., "YYYY-MM-DD HH:MM:SS,mmm") 被转换为自 Epoch 以来的毫秒级数字时间戳 (`new Date(timestampString).getTime()`)。这适用于 `recharts` 的数值型X轴，便于排序、定义域计算和格式化。
*   **数据准备与转换:**
   *   利用 `logParser.worker.ts` 中的 `formatBlockDataForFrontend` 函数对每个 `ProcessedBlock` 进行初步格式化，生成包含 `data_points` (时间戳、胶厚、准直) 和 `valve_open_events` 的结构。
   *   来自多个选定 `ProcessedBlock` 的 `data_points` 被合并，并为每个数据系列（如 `glue_thickness_block_X`, `collimation_diff_block_X`）创建唯一的 `dataKey`，以便在同一图表上区分显示。
   *   所有合并后的数据点 (`allChartDataPoints`) 最终按时间戳排序。
*   **图表库与组件:**
   *   使用 `recharts` 库。采用 `<ComposedChart>` 作为基础，主要使用 `<Line>` 组件展示胶厚和准直数据。`<ComposedChart>` 提供未来扩展其他图表类型（如柱状图）的灵活性。
   *   X轴 (`<XAxis>`) 设置为 `type="number"`，使用转换后的数字时间戳，并提供 `tickFormatter` 将数字时间戳转换为可读的时间字符串。
   *   Y轴 (`<YAxis>`) 分别对应胶厚和准直数据，使用不同的 `yAxisId`。
*   **事件可视化:**
   *   `valve_open_events`（放气阀打开事件）通过 `<ReferenceLine>` 组件在图表上标记其发生的时间点，并附带标签。
*   **多数据块处理:**
   *   图表设计为可以同时显示来自多个数据块的数据。每个数据块的系列使用不同的颜色（通过 `COLORS` 数组循环获取）和图例名称加以区分。
   *   `connectNulls={true}` 用于 `<Line>` 组件，以处理数据点可能不连续的情况。

---
### Decision (Debug)
[2025-06-10 16:06:26] - [Bug Fix Strategy: SSR `self is not defined` Error in Web Worker]

**Rationale:**
The `ReferenceError: self is not defined` occurs because Web Worker code (`self.onmessage`) is executed in a Node.js environment (SSR) where `self` is not a defined global. The fix ensures Worker instantiation and its lifecycle management occur only on the client-side.

**Details:**
*   **Affected Component:** [`hotel-dashboard/components/log-analysis/LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx)
*   **Fix Implemented:**
    *   The Web Worker instance (`logParser.worker.ts`) is now created and managed within a `useEffect` hook in `LogFileUpload.tsx`.
    *   A condition `typeof window !== 'undefined'` is used inside `useEffect` to ensure the Worker is only instantiated in the browser environment.
    *   The Worker instance is stored in a `useRef` hook (`workerRef`) to persist it across re-renders and allow access in event handlers.
    *   The `useEffect` hook also includes a cleanup function to terminate the Worker when the component unmounts.
    *   The `handleUpload` function now uses `workerRef.current` to post messages to the Worker.

---
### Decision (Debug)
[2025-06-10 16:12:30] - [Bug Fix Strategy: Log Parser Worker Fails to Extract Data Blocks Due to Encoding Issues]

**Rationale:**
The Web Worker ([`hotel-dashboard/workers/logParser.worker.ts`](hotel-dashboard/workers/logParser.worker.ts)) was unable to parse data blocks from uploaded log files because the file content was being read with an incorrect encoding (defaulting to UTF-8 via `selectedFile.text()`). If the log files are encoded in GBK or other non-UTF-8 formats, the string passed to the worker would be garbled, causing regex matches to fail. The previous project (`web_tool`) successfully handled this by using `jschardet` for encoding detection and `TextDecoder` for decoding.

**Details:**
*   **Affected Component:** [`hotel-dashboard/components/log-analysis/LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx)
*   **Fix Implemented:**
    *   Installed the `jschardet` library in the `hotel-dashboard` project.
    *   Modified the `handleUpload` function in `LogFileUpload.tsx`:
        *   File is read as an `ArrayBuffer` using `selectedFile.arrayBuffer()`.
        *   `jschardet.detect()` is used on the `ArrayBuffer` (via a `Uint8Array`) to determine the file's encoding.
        *   A `TextDecoder` instance is created using the detected encoding (with fallbacks to 'gbk' and then 'utf-8' if detection is uncertain or the primary detected encoding fails, mirroring the logic from the `web_tool` project).
        *   The `ArrayBuffer` is decoded into a string using the `TextDecoder`.
        *   This correctly decoded string is then sent to the Web Worker for parsing.

---
### Decision (Debug)
[2025-06-10 16:21:00] - [Bug Fix Strategy: Web Worker重复实例化及`self is not defined`错误]

**Rationale:**
控制台反复输出 "[Worker] logParser.worker.ts script loaded..." 表明 Web Worker 被多次实例化。同时，出现 `self is not defined` 错误，指示 Worker 脚本在服务器端被执行。`jschardet.detect` 也存在 TypeScript 类型问题。

**Details:**
*   **Affected Files:**
    *   [`hotel-dashboard/components/log-analysis/LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx)
    *   [`hotel-dashboard/workers/logParser.worker.ts`](hotel-dashboard/workers/logParser.worker.ts)
*   **Fixes Implemented:**
    1.  **Worker 重复实例化 (`LogFileUpload.tsx`):**
        *   `useEffect` Hook (用于 Worker 初始化和清理) 的依赖项数组从 `[onDataProcessed, onError]` 更改为 `[]`。这确保 Worker 仅在组件挂载时创建，在卸载时销毁。
        *   为确保 `onmessage` 和 `onerror` 事件处理器能调用最新的 `onDataProcessed` 和 `onError` prop 函数，这些 prop 通过 `useRef` 存储，并在 `useEffect` 中更新这些 ref。事件处理器则调用 ref 的 `.current` 版本。
        *   在 `useEffect` 的清理函数中，明确将 Worker 的 `onmessage` 和 `onerror` 设置为 `null`，然后再调用 `terminate()`。
    2.  **`self is not defined` 错误 (`logParser.worker.ts`):**
        *   在 `logParser.worker.ts` 文件顶部添加了一个检查 `if (typeof self === 'undefined' || typeof self.postMessage !== 'function' || (typeof window !== 'undefined' && self === window))`。
        *   如果条件为真（即不在有效的 Worker 环境中），则会打印警告，并且 Worker 的主要逻辑（包括 `self.onmessage` 的设置）被包裹在一个 `else` 块中，从而不会执行。所有顶层 `export` 语句保持在顶层，以便模块在非 Worker 环境中仍可导入（例如用于类型共享）。
    3.  **`jschardet.detect` 类型错误 (`LogFileUpload.tsx`):**
        *   对 `jschardet.detect(uInt8ArrayForDetection)` 的调用修改为 `jschardet.detect(uInt8ArrayForDetection as any)`，以抑制 TypeScript 关于 `Uint8Array` 与 `Buffer` 不兼容的错误。
        *   置信度阈值检查从 `jschardet.Constants.MINIMUM_THRESHOLD` 改为直接使用数值 `0.2`，因为 `Constants` 对象未按预期导出。

---
### Decision (Debug)
[2025-06-10 16:33:00] - [Bug Fix Strategy: `Uncaught ReferenceError: window is not defined` in `logParser.worker.ts`]

**Rationale:**
The error `Uncaught ReferenceError: window is not defined` at line 48 of [`hotel-dashboard/workers/logParser.worker.ts`](hotel-dashboard/workers/logParser.worker.ts) was caused by code (specifically `[...new Set(...)]`) that, while not directly calling `window`, might have an implicit dependency or behavior in some JavaScript engines within the Web Worker environment that leads to attempts to access `window`. Additionally, the existing Worker environment check was not sufficiently robust.

**Details:**
*   **Affected File:** [`hotel-dashboard/workers/logParser.worker.ts`](hotel-dashboard/workers/logParser.worker.ts)
*   **Fixes Implemented:**
    1.  **Timestamp Deduplication (Line 48 area):**
        *   Replaced the use of `[...new Set([...glueValues.map(item => item.timestamp), ...collimValues.map(item => item.timestamp)])]` with a manual approach using an object to create a set of unique timestamps:
            ```typescript
            const timestampSet: { [key: string]: boolean } = {};
            for (const item of glueValues) {
                if (item.timestamp) timestampSet[item.timestamp] = true;
            }
            for (const item of collimValues) {
                if (item.timestamp) timestampSet[item.timestamp] = true;
            }
            const allTimestamps = Object.keys(timestampSet);
            allTimestamps.sort();
            ```
        *   This avoids potential `window` access by `Set` or spread syntax in certain Worker contexts.
    2.  **Worker Environment Check (Line 72 area):**
        *   Strengthened the condition for executing worker-specific logic by checking against `DedicatedWorkerGlobalScope`:
            ```typescript
            if (
                typeof DedicatedWorkerGlobalScope === "undefined" ||
                !(self instanceof DedicatedWorkerGlobalScope) ||
                typeof self.postMessage !== 'function'
            ) {
            // ... console.warn and do not run worker logic
            } else {
            // ... run worker logic
            }
            ```
        *   This ensures that the core worker logic only runs if the script is truly executing within a dedicated worker's global scope.
## Rationale

*

## Implementation Details

*
---
### Decision (Debug)
[2025-06-10 17:17:00] - [Bug Fix Strategy: Timestamp Parsing in Chart View Produces NaN]

**Rationale:**
Console logs after user selected a data block showed that `new Date(dp.timestamp).getTime()` in `LogChartView.tsx` was resulting in `NaN` for the `time` property of chart data points. This occurred despite the timestamp strings (e.g., "2025-06-06 08:45:17,890") appearing generally parsable. The use of a comma as a millisecond separator, and a space as the date-time separator, while often handled by `new Date()`, is not as universally robust as the ISO 8601 standard (which uses a period for milliseconds and 'T' as the date-time separator). This inconsistency likely led to parsing failures.

**Details:**
*   **Affected File:** [`hotel-dashboard/components/log-analysis/LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx)
*   **Fix Implemented:**
    1.  A new helper function `parseTimestamp(ts: string | null | undefined): number` was introduced in `LogChartView.tsx`.
    2.  This function first checks if the input timestamp string `ts` is null or undefined, returning `NaN` if so.
    3.  It then standardizes the timestamp string by:
        *   Replacing the comma (`,`) millisecond separator with a period (`.`).
        *   Replacing the space (" ") separator between date and time with a 'T'.
        (e.g., "2025-06-06 08:45:17,890" becomes "2025-06-06T08:45:17.890").
    4.  The standardized string is then passed to `new Date()`, and `.getTime()` is called on the result.
    5.  Added a `console.warn` within `parseTimestamp` if `getTime()` still results in `NaN` after standardization, to aid future debugging.
    6.  All internal uses of `new Date(dp.timestamp).getTime()` and `new Date(event.timestamp).getTime()` within `LogChartView.tsx` were replaced with calls to `parseTimestamp(dp.timestamp)` and `parseTimestamp(event.timestamp)` respectively.
    7.  Updated chart rendering logic (e.g., `tickFormatter`, `labelFormatter`, `ReferenceLine` rendering) to also handle potential `NaN` time values gracefully, preventing crashes and providing visual feedback (e.g., "Invalid Time").
    8.  Adjusted `timeDomain` calculation to be more robust against `NaN` values to prevent chart errors.
---
### Decision (Debug)
[2025-06-10 18:01:00] - [Bug Fix Strategy: Isolate Chart Rendering with Minimal Config]

**Rationale:**
After previous fixes (timestamp parsing, robust decoding), chart lines and points still failed to render with real data, and also with initial fake data attempts. Only `ReferenceLine` was visible. DOM inspection showed Y-axes were not rendering. Forcing a fixed Y-axis domain with fake data made Y-axes appear, but still no lines/points. The latest test with an extremely simplified chart configuration (single Y-axis with fixed domain, single Line component with basic props and highly visible dots) using fake data *successfully rendered both dots and lines*.

This indicates the core Recharts rendering mechanism is functional. The problem likely lies in:
1.  The interaction of more complex configurations (e.g., dual Y-axes, `ComposedChart` vs `LineChart`, specific `<Line type="...">` props, `Tooltip`, `Legend`).
2.  How `domain={['auto', 'auto']}` for Y-axes behaves with the actual data (potentially all nulls for a series, or values that Recharts struggles to automatically scale).
3.  The combination of the above with real data characteristics.

**Strategy Adopted:**
The successful rendering with a minimal setup using fake data is a key baseline. The next step is to revert to using *real data* but maintain a *simplified chart configuration* (initially, a single Y-axis with `domain="auto"` and a single Line component) to see if real data can be rendered in this minimal setup. If successful, configurations will be added back incrementally.

**Details:**
*   **Observation:** Minimal chart with fake data, fixed Y-axis domain, and prominent dots renders correctly.
*   **Hypothesis:** The issue is not with the fundamental SVG rendering or container sizes, but with specific Recharts configurations or their interaction with data properties when using `auto` domains or multiple series/axes.
*   **Next Step:** Modify `LogChartView.tsx` to use real data (`useTestData = false`), but keep the chart configuration minimal (e.g., single Y-axis with `domain="auto"`, one `<Line>`).
---
### Decision (Debug)
[2025-06-10 18:03:00] - [Bug Fix Strategy: Incremental Restoration of Chart Configuration]

**Rationale:**
Testing ريال data with a highly simplified chart configuration (single `LineChart`, single Y-axis with `domain="auto"`, single `<Line>` component) resulted in successful rendering of data points and lines. This confirms that the data processing, timestamp parsing, and basic Recharts rendering are functional. The issue of the chart appearing blank must therefore stem from the more complex configurations present in the original `LogChartView.tsx` code.

**Strategy Adopted:**
Incrementally reintroduce components and configurations that were present in the original, more complex chart implementation. After each addition or small group of additions, the chart will be tested with real data to pinpoint which specific part of the configuration causes the rendering to fail.

**Initial Restoration Steps:**
1.  Change chart type from `LineChart` back to `ComposedChart`.
2.  Restore the original `Tooltip` and `Legend` components and their configurations.
3.  Restore the `type="monotone"` prop to the `<Line>` component.

Subsequent steps will involve reintroducing the second Y-axis and the second `<Line>` component for collimation data, and finally the `ReferenceLine` for events, as these are the most complex parts.
---
### Decision (Debug)
[2025-06-10 18:16:00] - [Bug Fix Strategy: Test Dual Y-Axes with Fixed Domains Using Fake Data]

**Rationale:**
After successfully rendering a single data series (glue thickness) with `domain="auto"` on its Y-axis, reintroducing the second Y-axis and second data series (collimation difference), both also with `domain="auto"`, caused the chart to fail rendering correctly again (blank or only one series partially visible). This strongly suggests an issue with how Recharts calculates or handles `domain="auto"` when multiple Y-axes are present, especially if one data series might have characteristics (e.g., all nulls, very small range, or extreme values not properly handled by auto-scaling in a dual-axis context) that interfere with domain calculation for itself or the other axis.

**Strategy Adopted:**
To isolate whether the `domain="auto"` calculation is the culprit in a dual-axis setup, the next step is to:
1.  Revert to using the internally generated fake data (which is known to be well-structured for both series).
2.  Configure *both* Y-axes (`yAxisId="left"` for glue thickness and `yAxisId="right"` for collimation difference) with *fixed, explicit domains* that are known to encompass the range of the fake data.
3.  Render both `<Line>` components.

If both series render correctly under this setup, it will confirm that the issue lies with `domain="auto"` in a multi-axis scenario with the specific characteristics of the real (or even fake, if auto-domain fails there too) data. If it still fails, the problem is more fundamental to the dual-axis/dual-line rendering in Recharts.

**Details:**
*   **Affected File:** [`hotel-dashboard/components/log-analysis/LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx)
*   **Modification:**
    *   Set `useTestData = true` to use `generateTestData()`.
    *   Set `domain={[900, 1200]}` (or similar appropriate fixed range based on fake data) for the left Y-axis.
    *   Set `domain={[0, 0.1]}` (or similar appropriate fixed range based on fake data) for the right Y-axis.
    *   Ensure both `<Line>` components are included in the render.
---
### Decision (Debug)
[2025-06-10 18:20:00] - [Bug Fix Strategy: Incremental Reintroduction from Minimal Working Chart]

**Rationale:**
The test with an extremely simplified chart configuration using hardcoded data was successful: a single line with data points rendered correctly. This confirms that the basic Recharts `LineChart`, `XAxis`, `YAxis`, and `Line` components can function within the project environment. The failure to render lines/points in previous, more complex configurations (even with valid fake data and fixed Y-axis domains) must be due to elements introduced beyond this minimal setup.

**Strategy Adopted:**
Starting from the known-good minimal hardcoded example, incrementally reintroduce the features and data processing steps that were part of the original, non-working `LogChartView` implementation. This will help pinpoint exactly which addition causes the rendering to fail.

**First Incremental Step:**
1.  Reintroduce the `LogChartViewProps` (including `dataChunks` and `useTestData`).
2.  Set `useTestData = true` to use the `generateTestData()` function.
3.  Restore the data processing pipeline: `generateTestData()` -> `formatBlockDataForFrontend()` -> creation of `allChartDataPoints` -> `finalChartData`.
4.  Maintain a `LineChart` with a single X-axis and a single Y-axis (no `yAxisId`, default).
5.  The single `<Line>` component will use the dynamically generated `dataKey` (e.g., `glue_thickness_test_block_1`) derived from the `finalChartData`.
6.  The Y-axis will use a fixed domain appropriate for the "glue" series of the fake data (e.g., `[900, 1200]`).
7.  Keep auxiliary components (`Tooltip`, `Legend`, `CartesianGrid`) minimal or as they were in the working hardcoded version.

This step aims to verify if the data processing pipeline and dynamic `dataKey` usage are compatible with the basic `LineChart` rendering.
---
### Decision (Debug)
[2025-06-11 08:56:00] - [Bug Fix Strategy: Test `ComposedChart` with Single Series]

**Rationale:**
The previous test confirmed that a `LineChart` rendering a single series (glue thickness) with dynamically processed fake data and a dynamic `dataKey` works correctly. The version that failed before this (showing axes but no lines/dots) used `ComposedChart` and attempted to render two series with two Y-axes, both with fixed domains. To isolate the cause of failure, the next step is to determine if `ComposedChart` itself behaves differently from `LineChart` even when rendering only a single series.

**Strategy Adopted:**
Modify the last working configuration (single series `LineChart` with fake data and fixed Y-axis domain) by changing only the chart container from `LineChart` to `ComposedChart`. All other aspects (single X-axis, single Y-axis, single `<Line>` component, data processing, `dataKey`) will remain the same.

**Details:**
*   **Affected File:** [`hotel-dashboard/components/log-analysis/LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx)
*   **Modification:**
    *   Change `<LineChart ...>` to `<ComposedChart ...>`.
    *   Ensure all other configurations from the last successful test (single series, fake data, fixed Y-axis domain for that series, dynamic `dataKey`) are maintained.
    *   The `ComposedChart` will contain one `XAxis`, one `YAxis`, and one `Line` component.
---
### Decision (Debug)
[2025-06-11 09:01:00] - [Bug Fix Strategy: Test Dual Series in `ComposedChart` with Fixed Domains]

**Rationale:**
The previous test successfully rendered a single series (glue thickness) using `ComposedChart` with fake data, a dynamic `dataKey`, and a fixed Y-axis domain. This confirms that `ComposedChart` itself, along with `Tooltip`, `Legend`, and `type="monotone"` for the line, are not the primary cause of the rendering failure when multiple series are involved. The issue is now strongly suspected to be related to the introduction of a second Y-axis and a second `<Line>` component, particularly how their Y-axis domains are handled.

**Strategy Adopted:**
Maintain the `ComposedChart` setup from the last successful test. Reintroduce the second Y-axis (for collimation data) and the second `<Line>` component (for collimation data). Crucially, *both* Y-axes will be configured with fixed, explicit domains known to be appropriate for their respective series in the fake data. This will test if the dual-axis, dual-line structure itself is problematic even when `domain="auto"` is not a factor.

**Details:**
*   **Affected File:** [`hotel-dashboard/components/log-analysis/LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx)
*   **Modification:**
    *   Continue using `useTestData = true`.
    *   The `ComposedChart` will contain one `XAxis`.
    *   Add back the second `YAxis` (`yAxisId="right"`, `orientation="right"`) with its `<RechartsLabel>`.
    *   Set a fixed `domain` for the left Y-axis (e.g., `[900, 1200]` for glue thickness from fake data).
    *   Set a fixed `domain` for the right Y-axis (e.g., `[0, 0.1]` for collimation diff from fake data).
    *   Add back the second `<Line>` component, associating it with `yAxisId="right"` and using the dynamic `dataKey` for collimation data (e.g., `collimation_diff_test_block_1`).
    *   Restore `Tooltip` formatter logic for both series.
---
### Decision (Debug)
[2025-06-11 09:04:00] - [Bug Identified: Y-Axis `domain="auto"` Calculation with Dual Axes]

**Rationale:**
Previous tests culminated in a successful rendering of a dual-axis, dual-line `ComposedChart` when using *fake data* and *fixed domains* for both Y-axes. This was a critical step, as prior attempts with `domain="auto"` for Y-axes (even with fake data in some configurations, or real data) failed to render lines/dots correctly, often resulting in only axes being visible or a completely blank chart area (aside from `ReferenceLine`). The successful test with fixed domains strongly indicates that the root cause of the chart rendering failure lies in Recharts' automatic calculation of Y-axis domains (`domain="auto"`) when multiple Y-axes are present and interacting with the characteristics of the input data (either real or the more complex fake data). One or both series' data might be leading to an unrenderable domain (e.g., all nulls, no variance, extreme values) that disrupts the rendering of other chart elements.

**Strategy Moving Forward:**
1.  **Switch to Real Data with Fixed Domains:** The immediate next step is to switch `LogChartView.tsx` to use real data (`useTestData = false`). However, instead of immediately reverting to `domain="auto"`, initially use fixed domains for both Y-axes. This requires determining appropriate fixed ranges for the "glue thickness" and "collimation difference" series from the real data. This can be done by:
    *   Temporarily logging min/max values of the processed real data series.
    *   Asking the user for typical value ranges if known.
    *   Setting slightly padded fixed domains based on this information.
    This will verify if real data can be rendered correctly when domain calculation is not left to "auto".

2.  **Incremental Reintroduction of `domain="auto"`:** If step 1 is successful (real data renders with fixed domains):
    *   Attempt setting `domain="auto"` for *one* Y-axis at a time, keeping the other fixed. This will help identify if a specific data series is problematic for auto-domain calculation.
    *   If both individual auto-domains work, attempt setting `domain="auto"` for *both* Y-axes simultaneously.

3.  **Implement Fallbacks for `domain="auto"` if Necessary:** If `domain="auto"` proves unreliable for certain real data scenarios (e.g., a series is all nulls), implement logic to detect such cases and provide a sensible default fixed domain, or hide the problematic series/axis with a user notification.

**Details for Next Code Change:**
*   **Affected File:** [`hotel-dashboard/components/log-analysis/LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx)
*   **Modification:**
    *   Set `useTestData = false`.
    *   Add temporary logging within the component to calculate and print the min/max values for `glue_thickness_block_X` and `collimation_diff_block_X` from the `finalChartData` when real data is processed.
    *   Based on these logged min/max values (or user input), set appropriate fixed `domain` props for both left and right Y-axes.
    *   Keep the dual-line, dual-Y-axis `ComposedChart` structure.
---
### Decision (Debug)
[2025-06-11 09:24:00] - [Bug Investigation: Lines/Dots Not Rendering Despite Correct Axes and Data with Real Data]

**Rationale:**
The previous test using real data and dynamically calculated fixed domains for both Y-axes successfully rendered the X and Y axes with correct domain ranges. However, the `<Line>` components (for both glue thickness and collimation difference) and their `dot`s still failed to render. This is highly perplexing as the data processing pipeline, timestamp parsing, `dataKey` matching, and Y-axis domain calculations appear correct according to console logs. The issue persists even when `connectNulls={true}` and dot visibility are enforced.

**Strategy Adopted to Further Isolate:**
The problem might be related to how Recharts handles the rendering of multiple lines associated with different Y-axes, or a subtle issue with a shared prop, or an interaction with `allowDataOverflow` (though less likely for causing complete non-rendering).
The next steps will be:
1.  **Remove `allowDataOverflow={true}`:** This property was added to Y-axes. While intended to allow data outside the domain, it's worth removing to see if it has an unintended side effect.
2.  **Test Single Line with Dual Axes Declared:** Modify the chart to declare both Y-axes (left and right, with their calculated fixed domains) but only include *one* `<Line>` component (e.g., for glue thickness, associated with the left Y-axis). This will test if the mere presence and configuration of the second Y-axis (even if no line is explicitly mapped to it yet) interferes with the rendering of the first line.
3.  If the single line renders, then add back the second line. If it fails at that point, the issue is highly specific to rendering two lines simultaneously with their respective Y-axes.

**Details for Next Code Change:**
*   **Affected File:** [`hotel-dashboard/components/log-analysis/LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx)
*   **Modification:**
    *   Continue using real data (`useTestData = false`).
    *   Remove `allowDataOverflow={true}` from both `<YAxis>` components.
    *   Initially, comment out the second `<Line>` component (for collimation difference).
    *   Keep both `<YAxis>` declarations.
---
### Decision (UI Architecture)
[2025-06-11 10:15:00] - 确认将“选择数据块进行分析”功能区与“日志文件上传”组件并排布局的方案。

**Rationale:**
*   **用户体验提升:** 将相关功能在视觉上并列，可以改善用户操作流程的连贯性，减少页面滚动或导航。
*   **响应式设计:** 采用 Flexbox (例如 Tailwind CSS 的 `flex flex-col md:flex-row gap-8`) 可以在不同屏幕尺寸上提供合适的布局，确保可用性。
*   **技术可行性:** 此方案是纯 UI 层面调整，不影响核心业务逻辑或数据流，风险低。
*   **可维护性:** 在父组件 [`hotel-dashboard/app/(dashboard)/log-analysis/page.tsx`](hotel-dashboard/app/(dashboard)/log-analysis/page.tsx) 中进行布局调整，职责清晰，易于未来维护。

**Implementation Details:**
*   **目标文件:** [`hotel-dashboard/app/(dashboard)/log-analysis/page.tsx`](hotel-dashboard/app/(dashboard)/log-analysis/page.tsx)
*   **方法:**
    *   创建一个新的 `div` 元素作为 [`LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx) 和 [`LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx) 组件的容器。
    *   对该 `div` 应用 Tailwind CSS 类，例如：`flex flex-col md:flex-row gap-8`，以实现所需的并排和响应式布局。
*   **影响评估:**
    *   对子组件 [`LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx) 和 [`LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx) 的内部逻辑无直接影响，但可能需要微调其根元素的样式以适应新的父容器。
    *   数据流和状态管理不受影响。

---
### Decision (UI Architecture - Correction based on User Feedback)
[2025-06-11 10:23:00] - 调整日志分析页面布局，将“日志文件上传”和“选择数据块进行分析”功能区并排等高显示，并将曲线图单独置于其下方。

**Rationale:**
*   **用户反馈:** 此更改是基于用户对先前布局的反馈，旨在优化视觉层次和操作流程。
*   **用户体验提升:** 将主要操作区域（文件上传、数据选择）集中在页面上半部分并排显示，可以提高空间利用率并减少垂直滚动。图表作为结果展示，单独置于下方符合常见仪表盘布局模式。
*   **组件职责调整:** 促进了更好的关注点分离，父页面 ([`page.tsx`](hotel-dashboard/app/(dashboard)/log-analysis/page.tsx)) 承担更多布局和数据协调职责。

**Implementation Details:**
*   **Affected Files:**
    *   [`hotel-dashboard/app/(dashboard)/log-analysis/page.tsx`](hotel-dashboard/app/(dashboard)/log-analysis/page.tsx): 实现两行布局。第一行使用 Flexbox (e.g., Tailwind CSS `flex flex-col md:flex-row gap-8 items-stretch`) 包裹 [`LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx) 和修改版的 [`LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx)。第二行渲染 [`LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx)。
    *   [`hotel-dashboard/components/log-analysis/LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx): 将不再直接渲染图表。需要提供回调函数给父组件以传递选中的数据块信息。
*   **Data Flow Changes:**
    *   [`page.tsx`](hotel-dashboard/app/(dashboard)/log-analysis/page.tsx) 将从 [`LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx) (通过新回调) 获取选中的数据块。
    *   [`page.tsx`](hotel-dashboard/app/(dashboard)/log-analysis/page.tsx) 负责准备图表数据并将其传递给 [`LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx)。
*   **Impact Assessment:**
    *   [`LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx) 的职责变得更单一（数据表格和选择）。
    *   [`LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx) 保持其渲染图表的职责，但数据源将由 [`page.tsx`](hotel-dashboard/app/(dashboard)/log-analysis/page.tsx) 提供。
    *   提高了 [`page.tsx`](hotel-dashboard/app/(dashboard)/log-analysis/page.tsx) 作为容器和协调者的角色。

---
### Decision (UI Layout Refinement)
[2025-06-11 10:33:00] - 采纳关于 [`LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx) 和 [`LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx) 固定高度及内部滚动的UI布局细化方案。

**Rationale:**
*   **用户反馈:** 进一步细化UI布局，满足用户对特定组件固定高度和滚动行为的要求。
*   **视觉一致性:** 确保“日志文件上传”和“选择数据块进行分析”组件具有统一的450px高度。
*   **内容可访问性:** 通过在 [`LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx) 内部实现滚动，确保在固定高度下所有内容均可访问。

**Implementation Details:**
*   **Affected Components:**
    *   [`LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx): 根 `Card` 元素应用 `h-[450px]`。
    *   [`LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx): 根 `Card` 元素应用 `h-[450px]`。其内部的数据块列表区域（如 `CardContent` 内的 `div`）应用 `overflow-y-auto` 并配合适当的 flex/height 布局以实现滚动。
*   **Parent Component Adjustment:**
    *   [`hotel-dashboard/app/(dashboard)/log-analysis/page.tsx`](hotel-dashboard/app/(dashboard)/log-analysis/page.tsx): 包裹这两个组件的父 `div` 的 Flexbox 对齐方式从 `items-stretch` 修改为 `items-start`。
*   **Impact Assessment:**
    *   此调整主要影响相关组件的视觉表现和内部布局，对核心数据流和业务逻辑无重大影响。
    *   需要确保滚动区域的高度计算正确，以避免布局问题。
---
### Decision (UI Layout/CSS Fix)
[2025-06-11 10:43:10] - 采纳针对 [`LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx) 内部滚动问题的 `min-h-0` Tailwind CSS 类修复方案。

**Rationale:**
*   **Flexbox Behavior:** 在 Flexbox 布局中，当一个 flex item (设置了 `flex-grow` 和 `overflow-y-auto`) 的内容超出其父容器分配的空间时，它可能不会出现预期的滚动条，而是会撑大父容器。这是因为 `flex-grow` 会尝试分配所有可用空间，但若没有最小高度限制，它可能会错误地计算其内容所需的实际高度。
*   **`min-h-0` 作用:** `min-h-0` (即 `min-height: 0px;`) 为 flex item 提供了一个明确的最小高度基线。这使得 `overflow-y-auto` 能够在其由 `flex-grow` 决定的、正确计算的分配空间内创建滚动条，而不是基于其无限内容的固有高度。
*   **Standard Solution:** 这是解决此类 Flexbox 内部滚动问题的标准且有效的方法，避免了复杂的 CSS hack 或 JavaScript介入。

**Implementation Details:**
*   **目标文件:** [`LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx)
*   **修改:** 在包含数据块列表并应用了 `flex-grow` 和 `overflow-y-auto` 的 `div` 元素上添加 `min-h-0` Tailwind CSS 类。
*   **伪代码参考:**
    ```pseudocode
    // ... (Card and CardHeader)
    CardContent (className: "space-y-4 flex-grow flex flex-col")
      // ... (Buttons)
      DIV (className: "flex-grow overflow-y-auto space-y-2 p-2 border rounded-md min-h-0") // CRITICAL CHANGE: Added min-h-0
        // ... (Loop for data chunks)
      END DIV
    END CardContent
    // ...
    ```
*   **影响评估:** 此更改是局部的，预计不会对组件的其他部分或整体应用布局产生负面影响。

---
### Decision (UI Layout/CSS Fix)
[2025-06-11 10:50:00] - 采纳在 [`LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx) 的 `CardContent` 元素上添加 `overflow-hidden` 的修复方案，以解决固定高度父组件下的内部滚动和内容溢出问题。

**Rationale:**
*   **问题背景:** 尽管 [`LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx) 的根 `Card` 元素具有固定高度 (`h-[450px]`)，并且其内部可滚动列表区域使用了 `min-h-0` 来确保正确滚动，但 `CardContent` (作为可滚动区域的父级，且设置了 `flex-grow flex flex-col`) 仍可能被其内容撑高，超出 `Card` 的固定高度。
*   **`overflow-hidden` 的作用:** 在 `CardContent` 上应用 `overflow-hidden` 会裁剪掉任何超出其计算边界的内容。由于 `CardContent` 是一个 flex item (`flex-grow`) 在一个固定高度的 flex 容器 (`Card`) 中，`overflow-hidden` 有助于强制 `CardContent` 遵守其分配的高度，防止它被内部内容（如过长的滚动列表）撑大。
*   **协同作用:** 此修复与之前在可滚动 `div` 上应用的 `min-h-0` 协同工作。`min-h-0` 帮助可滚动 `div` 在其父容器 (`CardContent`) 中正确计算其尺寸以实现滚动，而 `overflow-hidden` 在 `CardContent` 上确保 `CardContent` 本身不会超出其在 `Card` 内的分配空间。
*   **间距调整:** 同时建议移除按钮容器上的 `mb-4`，让 `CardContent` 的 `space-y-4` 统一管理其直接子元素的垂直间距，以保持一致性。

**Implementation Details:**
*   **目标文件:** [`LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx)
*   **修改:**
    *   为 `CardContent` 元素（已应用 `space-y-4 flex-grow flex flex-col`）添加 `overflow-hidden` Tailwind CSS 类。
    *   移除其直接子元素中按钮容器 `div` 上的 `mb-4` 类。
*   **伪代码参考:**
    ```pseudocode
    // ... (Card: h-[450px] flex flex-col)
      <CardHeader>...</CardHeader>
      <CardContent class="space-y-4 flex-grow flex flex-col overflow-hidden"> // CRITICAL CHANGE: Added overflow-hidden
        <div class="flex items-center space-x-2"> // Buttons - mb-4 removed
          // ...
        </div>
        <div class="flex-grow overflow-y-auto space-y-2 p-2 border rounded-md min-h-0"> // Scrollable List
          // ...
        </div>
      </CardContent>
    // ...
    ```
*   **影响评估:** 此更改是高度局部的 CSS 调整，旨在解决特定的 Flexbox 溢出问题，预计不会对其他组件或功能产生负面影响。
---
### Decision (Architecture & Recharts)
[2025-06-11 11:31:00] - Recharts 标签定位策略评估与建议

**Rationale:**
用户需要修改 Recharts 图表中标签的 X 和 Y 坐标，特别是在 [`hotel-dashboard/components/log-analysis/LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx) 中。`spec-pseudocode` 模式已总结了核心定位方法。本次评估旨在确认其准确性，并从架构角度提供建议，特别是关于 `content` 属性的运用。

**Details & Evaluation:**
*   **Core Recharts Label Positioning Strategies (Accurate):**
    *   **`<Label />` Component / Axis `label` Prop:** Fundamental approach.
    *   **Key Attributes:**
        *   `position`: (e.g., `'insideBottom'`, `'center'`) - For preset placements.
        *   `offset`: Numerical offset based on `position`.
        *   `x`, `y`: Absolute coordinates (context-dependent reference system).
        *   `dx`, `dy`: Relative fine-tuning adjustments.
        *   `content`: Enables custom React element/render function for full control.
    *   **Completeness Additions:**
        *   `LabelList` component is crucial for per-data-point labels.
        *   SVG `transform` attribute can be used within custom `content` for advanced positioning.

*   **Architectural Assessment:**
    *   **Standard Attributes (`position`, `offset`, `x`, `y`, `dx`, `dy`):**
        *   **Pros:** Simple, declarative, good performance for common cases.
        *   **Cons:** Limited flexibility for complex dynamic positioning; `x`/`y` reference can be non-intuitive.
    *   **`content` Attribute (Custom Rendering):**
        *   **Pros:** Maximum flexibility for complex layouts, styles, and interactions. Full control over positioning logic.
        *   **Cons:** Increased complexity (custom component maintenance); potential performance overhead if not optimized; shifts responsibility to custom code.

*   **Architectural Recommendations:**
    1.  **Prioritize Simplicity:** Use standard attributes for common needs.
    2.  **Custom Render on Demand:** Employ `content` for complex scenarios not covered by standard attributes.
    3.  **Encapsulate Custom Components:** If using `content`, design reusable, well-encapsulated label components.
    4.  **State Management:** Manage dynamic coordinates (from props/state) preferably outside the chart component or via custom hooks.
    5.  **Documentation:** Document complex/custom label patterns in [`LOG_ANALYSIS_DEVELOPER_DOCS.md`](LOG_ANALYSIS_DEVELOPER_DOCS.md) or [`systemPatterns.md`](memory-bank/systemPatterns.md).

**Implications/Details:**
*   This evaluation confirms the strategies provided by `spec-pseudocode` are sound.
*   The `content` prop offers powerful customization but should be used judiciously due to increased complexity.
*   Clear guidelines for choosing between standard props and custom rendering will improve maintainability and developer understanding within the [`hotel-dashboard`](hotel-dashboard) project.
*   The findings will inform updates to [`systemPatterns.md`](memory-bank/systemPatterns.md) regarding Recharts usage.
---
### Decision (Feature Enhancement & UI)
[2025-06-11 12:32:00] - 在 [`hotel-dashboard/components/log-analysis/LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx) 组件中添加日志文件统计信息显示和UI间距调整。

**Rationale:**
*   **用户需求:** 增强用户体验，在文件上传后提供关于日志文件的即时反馈（总行数、时间范围）。
*   **信息透明度:** 帮助用户快速了解已上传日志的基本情况。
*   **UI优化:** 调整间距以改善视觉效果和组件内元素布局。

**Implementation Details:**
*   **Affected Component:** [`hotel-dashboard/components/log-analysis/LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx)
*   **State Management:**
    *   引入新的 state `fileStats: { totalLines: number | null, firstLogTime: string | null, lastLogTime: string | null } | null`。
    *   在文件选择 (`handleFileChange`)、上传开始 (`handleUpload`) 或处理出错时重置 `fileStats` 为 `null`。
*   **Data Processing:**
    *   创建新函数 `calculateAndSetFileStats(blocks: ProcessedBlock[])`。
    *   此函数在 Web Worker 成功处理并返回 `ProcessedBlock[]` (在 `worker.onmessage` 中) 后被调用。
    *   `calculateAndSetFileStats` 遍历 `blocks`：
        *   累加 `block.lines_count` 计算总行数。
        *   解析并比较所有 `block.start_time` 找到最早时间。
        *   解析并比较所有 `block.end_time` 找到最晚时间。
        *   使用 `setFileStats` 更新状态。
        *   时间戳解析时需注意处理潜在的逗号毫秒分隔符 (e.g., `YYYY-MM-DD HH:MM:SS,mmm`)，转换为标准 Date 对象。
        *   最终显示的时间格式为 "YYYY-MM-DD HH:MM:SS"。
*   **UI Rendering:**
    *   调整 `CardContent` 的 `className` 为 `space-y-6 p-6` 以改善内部间距。
    *   在显示已选文件名下方，条件性渲染 `fileStats` 中的总行数、起始时间和结束时间。仅当 `fileStats` 有数据且不处于 `isProcessing` 状态时显示。
*   **Error Handling:**
    *   在 Worker 的 `onerror` 和 `onmessage` (当 `event.data.error` 为 true) 中，以及 `handleUpload` 的 `catch` 块中，调用 `setFileStats(null)` 以清除统计信息。
---
### Decision (Feature Enhancement & UI)
[2025-06-11 12:35:00] - 在 [`hotel-dashboard/components/log-analysis/LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx) 组件中实现日志文件统计信息显示和UI间距调整。

**Rationale:**
*   **用户需求:** 增强用户体验，在文件上传后提供关于日志文件的即时反馈（总行数、时间范围）。
*   **信息透明度:** 帮助用户快速了解已上传日志的基本情况。
*   **UI优化:** 调整间距以改善视觉效果和组件内元素布局。

**Details:**
*   **Affected Component:** [`hotel-dashboard/components/log-analysis/LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx)
*   **State Management:**
    *   引入新的 state `fileStats: { totalLines: number | null, firstLogTime: string | null, lastLogTime: string | null } | null`。
    *   在文件选择 (`handleFileChange`)、上传开始 (`handleUpload`) 或处理出错时重置 `fileStats` 为 `null`。
*   **Data Processing:**
    *   创建新函数 `calculateAndSetFileStats(blocks: ProcessedBlock[])`。
    *   此函数在 Web Worker 成功处理并返回 `ProcessedBlock[]` (在 `worker.onmessage` 中) 后被调用。
    *   `calculateAndSetFileStats` 遍历 `blocks`：
        *   累加 `block.lines_count` 计算总行数。
        *   解析并比较所有 `block.start_time` 找到最早时间。
        *   解析并比较所有 `block.end_time` 找到最晚时间。
        *   使用 `setFileStats` 更新状态。
        *   时间戳解析时需注意处理潜在的逗号毫秒分隔符 (e.g., `YYYY-MM-DD HH:MM:SS,mmm`)，转换为标准 Date 对象。
        *   最终显示的时间格式为 "YYYY-MM-DD HH:MM:SS"。
*   **UI Rendering:**
    *   调整 `CardContent` 的 `className` 为 `space-y-6 p-6` 以改善内部间距。
    *   文件选择区域的 `div` (`<div className="flex flex-col items-start gap-3">`) 调整间距为 `gap-3`。
    *   在显示已选文件名下方，条件性渲染 `fileStats` 中的总行数、起始时间和结束时间。仅当 `fileStats` 有数据且不处于 `isProcessing` 状态时显示。
*   **Error Handling:**
    *   在 Worker 的 `onerror` 和 `onmessage` (当 `event.data.error` 为 true) 中，以及 `handleUpload` 的 `catch` 块中，调用 `setFileStats(null)` 以清除统计信息。
- [2025-06-11 12:39:23] Integration Check: LogFileUpload.tsx component modifications and documentation updates verified. Confirmed conceptual consistency with the project. No obvious integration issues identified.
* [2025-06-11 13:12:00] - **集成检查完成:** 对 [`hotel-dashboard/components/log-analysis/LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx) 组件的自定义文件输入框UI修改及相关文档 ([`LOG_ANALYSIS_DEVELOPER_DOCS.md`](LOG_ANALYSIS_DEVELOPER_DOCS.md), [`LOG_ANALYSIS_USER_GUIDE.md`](LOG_ANALYSIS_USER_GUIDE.md)) 更新进行了集成检查。
    *   **确认:** 更改在概念上与项目的其余部分保持一致，遵循了 [`memory-bank/systemPatterns.md`](memory-bank/systemPatterns.md) 中定义的“自定义文件输入组件模式”。
    *   **结论:** 未发现明显的集成问题。
---
### Decision (UI/Component Design)
[2025-06-11 13:05:41] - 采纳针对 [`hotel-dashboard/components/log-analysis/LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx) 的自定义文件输入框样式和功能规范。

**Rationale:**
*   **用户体验提升:** 提供比浏览器默认文件输入框更美观、更符合整体应用风格的界面。
*   **功能集成:** 允许在自定义组件内部集成文件名显示和其他相关UI元素。
*   **设计一致性:** 确保文件上传功能在视觉上与 `shadcn/ui` 的其他组件和整体仪表盘设计语言保持一致。

**Implementation Details:**
*   **结构:** 使用 `<label>` 元素包裹自定义内容，并将其 `htmlFor` 属性指向一个视觉上隐藏的 (`sr-only`) `<input type="file">` 元素。
*   **布局 (Flexbox):**
    *   自定义 `<label>` 内部使用 Flexbox 实现两段式布局。
    *   左侧部分为一个 `<span>` 或 `<div>`，显示静态文本“上传文件”，并具有按钮式的背景和前景颜色 (例如 `bg-primary text-primary-foreground`)。
    *   右侧部分为一个 `<span>` 或 `<div>`，用于显示所选文件名或默认占位文本 (如 "未选择文件")。此部分将具有类似标准输入框的边框和样式。
*   **功能性:** 点击整个 `<label>` 区域（特别是右侧区域）将触发隐藏的 `<input type="file">`，从而打开文件选择对话框。
*   **状态管理:**
    *   组件内部维护一个状态 `selectedFileName` (string)，用于存储和显示当前选择的文件名。初始值为 "未选择文件"。
*   **事件处理:**
    *   隐藏的 `<input type="file">` 的 `onChange` 事件将由一个新的处理函数 `handleActualFileChange` 捕获。
    *   `handleActualFileChange` 负责：
        *   从事件对象中获取选中的 `File` 对象。
        *   更新 `selectedFile` 状态 (已存在的状态，用于存储实际 `File` 对象)。
        *   更新 `selectedFileName` 状态为 `file.name`。
        *   根据现有逻辑重置 `fileStats`。
        *   如果用户未选择文件，则重置相关状态。
*   **可访问性:** 通过正确使用 `<label>` 和 `htmlFor`，以及为隐藏输入框提供 `sr-only` 类，确保基本的可访问性。
---
### Decision (UI/Component Enhancement)
[2025-06-11 13:08:44] - 在 [`hotel-dashboard/components/log-analysis/LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx) 中实现自定义文件输入框样式。

**Rationale:**
*   根据用户提供的规范，改进文件输入框的用户体验和视觉一致性。
*   移除旧的 `<Input type="file">`，替换为由 `<label>` 和隐藏的 `<input type="file">` 组成的自定义结构。
*   应用 Tailwind CSS 实现两段式布局（上传按钮和文件名显示）。
*   引入新的状态 `selectedFileName` 和事件处理函数 `handleActualFileChange` 来管理自定义输入框的行为。

**Details:**
*   移除了旧的 `<Input id="log-file" ... />`。
*   添加了新的 `<label htmlFor="hidden-log-file">` 结构，包含两个 `<span>` 用于布局。
*   添加了隐藏的 `<input id="hidden-log-file" type="file" className="sr-only" ... />`。
*   `selectedFileName` state 用于显示文件名，默认为 "未选择文件"。
*   `handleActualFileChange` 函数负责更新 `selectedFile`、`selectedFileName` 和重置 `fileStats`。
*   相关 CSS 类已按规范应用。
---
### Decision (Feature Architecture)
[2025-06-11 13:17:00] - 采纳为 [`LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx) 和 [`LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx) 添加图片导出功能的架构方案。

**Rationale:**
*   **用户需求:** 提供将可视化图表和日志分析摘要导出为图片的功能，便于分享和报告。
*   **技术选型:** `dom-to-image-more` 库因其对 SVG 的良好支持和处理复杂 DOM 的能力而被选中。
*   **模块化设计:** 创建专门的辅助工具模块 `hotel-dashboard/lib/exportUtils.ts` 来封装导出逻辑，提高代码复用性和可维护性。
*   **用户体验:** 通过异步操作、加载状态指示和 `toast` 通知来确保流畅的用户体验。

**Implementation Details:**
*   **辅助工具模块 (`hotel-dashboard/lib/exportUtils.ts`):**
    *   `exportElementAsImage(element: HTMLElement, fileName: string, type: 'png' | 'jpeg' = 'png'): Promise<void>`:
        *   使用 `dom-to-image-more` (e.g., `domtoimage.toPng()`) 将指定 HTML 元素转换为图片数据 URL。
        *   调用 `downloadImage` 触发下载。
        *   包含 `try-catch` 错误处理和用户通知 (`toast`)。
    *   `downloadImage(dataUrl: string, fileName: string): void`:
        *   创建临时 `<a>` 元素，设置 `href` 和 `download` 属性，模拟点击以下载图片。
*   **组件 [`LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx):**
    *   **UI:** 添加 "导出图片" 按钮。
    *   **Ref:** 使用 `useRef` 获取图表容器 `div` 的引用。
    *   **State:** `isExporting` (boolean) 管理导出状态。
    *   **Handler (`handleExportChart`):** 调用 `exportElementAsImage` 导出图表容器，文件名如 `log-chart-[timestamp].png`。
*   **组件 [`LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx):**
    *   **UI:** 添加 "导出全部图片" 按钮。
    *   **Ref:** 使用 `useRef` 获取整个显示区域的 `div` 引用。
    *   **State:** `isExportingAll` (boolean) 管理导出状态。
    *   **Handler (`handleExportAllImages`):** 调用 `exportElementAsImage` 导出整个显示区域，文件名如 `log-analysis-summary-[timestamp].png`。
*   **错误处理与反馈:**
    *   导出过程中按钮应禁用并显示加载状态。
    *   导出成功或失败时通过 `toast` 通知用户。
*   **文件命名:** 导出的图片文件名包含组件名和时间戳，以避免冲突。
---
### Decision (Feature Implementation)
[2025-06-11 13:24:16] - 实现图片导出功能

**Rationale:**
根据用户需求，为 [`LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx) 和 [`LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx) 添加图片导出功能，便于分享和报告。采用 `dom-to-image-more` 库和模块化的 `exportUtils.ts` 辅助模块。详细规范见初始用户请求。

**Details:**
*   创建了 [`hotel-dashboard/lib/exportUtils.ts`](hotel-dashboard/lib/exportUtils.ts) 包含 `exportElementAsImage` 和 `downloadImage` 函数。
*   修改了 [`hotel-dashboard/components/log-analysis/LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx) 添加了导出单个图表的功能。
*   修改了 [`hotel-dashboard/components/log-analysis/LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx) 添加了导出整个显示区域的功能。
*   使用了 `useRef` 来引用需要导出的 DOM 元素。
*   使用了 `useState` 来管理导出状态 (e.g., `isExporting`, `isExportingAll`)。
*   通过 `toast` 提供用户反馈。
*   提醒用户安装 `dom-to-image-more` 依赖。
## [YYYY-MM-DD] - 图片导出功能集成检查

**决策:**
确认图片导出功能 ([`hotel-dashboard/lib/exportUtils.ts`](hotel-dashboard/lib/exportUtils.ts), [`hotel-dashboard/components/log-analysis/LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx), [`hotel-dashboard/components/log-analysis/LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx)) 已按预期实现，并且相关文档 ([`LOG_ANALYSIS_DEVELOPER_DOCS.md`](LOG_ANALYSIS_DEVELOPER_DOCS.md), [`LOG_ANALYSIS_USER_GUIDE.md`](LOG_ANALYSIS_USER_GUIDE.md)) 已相应更新。

**理由:**
- 代码审查表明，导出逻辑在 `exportUtils.ts` 中被正确封装，并在 `LogChartView.tsx` 和 `LogDisplayArea.tsx` 中被正确调用。
- 考虑了 PNG 导出的背景色问题。
- 使用了标准的 `toast` 通知。
- 开发者文档和用户指南准确描述了新功能及其用法。
- 新功能与现有项目架构和用户体验保持一致，未发现明显的集成冲突。

**状态:** 已完成集成检查。
---
### Decision (Bug Fix Strategy - Image Export)
[2025-06-11 13:47:10] - 采纳针对图片导出内容截断问题的修复策略。

**Rationale:**
用户反馈图片导出功能存在内容截断问题。核心原因是 `dom-to-image-more` 默认可能只捕获元素的视口可见部分。

**Details:**
*   **Affected File:** [`hotel-dashboard/lib/exportUtils.ts`](hotel-dashboard/lib/exportUtils.ts) (函数 `exportElementAsImage`)
*   **Core Fix:**
    *   在调用 `domtoimage.toPng` 或 `domtoimage.toJpeg` 时，向 `options` 对象添加 `width: element.scrollWidth` 和 `height: element.scrollHeight`。这将确保捕获元素的完整滚动内容区域。
*   **Additional Considerations & Recommendations:**
    *   **`options.style`:** 考虑使用 `options.style = { 'overflow': 'visible !important', ... }` 来覆盖可能导致截断的父级或元素自身的 `overflow` 样式。
    *   **`cacheBust: true`:** 建议在 `options` 中启用此选项，以避免使用缓存的图像，确保捕获最新状态。
    *   **`bgcolor`:** 继续使用如 `#ffffff` 的背景色，以确保导出图片背景的一致性。
    *   **Performance:** 对大型或复杂元素的导出进行性能测试，因为捕获完整尺寸可能消耗较多资源。
    *   **Error Handling:** 保持现有的 `try-catch` 和 `toast` 通知机制。
*   **Verification:** 此策略基于对 `dom-to-image-more` 行为的理解和常见的 DOM 捕获问题的解决方案。
---
### Decision (Code Fix & TypeScript)
[2025-06-11 13:49:30] - 解决 `exportUtils.ts` 中的 TypeScript 错误并修复导入问题。

**Rationale:**
在更新 `exportElementAsImage` 函数后，出现了几个 TypeScript 错误：
1.  `dom-to-image-more` 缺少类型声明。
2.  `toast` 函数的导入路径不正确。
3.  `exportElementAsImage` 被重复导出。

**Details:**
*   **Affected File:** [`hotel-dashboard/lib/exportUtils.ts`](hotel-dashboard/lib/exportUtils.ts)
*   **Fixes Implemented:**
    1.  **`dom-to-image-more` 类型声明:**
        *   创建了类型声明文件 [`hotel-dashboard/lib/dom-to-image-more.d.ts`](hotel-dashboard/lib/dom-to-image-more.d.ts)。
        *   在该文件中添加了 `declare module 'dom-to-image-more';`。
    2.  **`toast` 导入路径:**
        *   将 [`hotel-dashboard/lib/exportUtils.ts`](hotel-dashboard/lib/exportUtils.ts) 中的导入路径从 `import { toast } from 'hotel-dashboard/components/ui/use-toast';` 修改为 `import { toast } from '../components/ui/use-toast';` 以正确解析相对路径。
    3.  **重复导出:**
        *   移除了 [`hotel-dashboard/lib/exportUtils.ts`](hotel-dashboard/lib/exportUtils.ts) 文件末尾多余的 `export { exportElementAsImage };` 语句，因为该函数已通过 `export async function exportElementAsImage(...)` 导出。
## [YYYY-MM-DD HH:MM:SS] - 集成检查：图片导出内容截断修复

**决策:**
确认对 [`hotel-dashboard/lib/exportUtils.ts`](hotel-dashboard/lib/exportUtils.ts) 中 `exportElementAsImage` 函数的更新以及相关文档 ([`LOG_ANALYSIS_DEVELOPER_DOCS.md`](LOG_ANALYSIS_DEVELOPER_DOCS.md)) 的审查已完成。

**理由:**
- 代码实现（使用 `scrollWidth`, `scrollHeight`, `style: { 'overflow': 'visible !important' }`, `cacheBust: true`）与开发者文档中的描述一致。
- 修复方案在概念上与项目目标（准确导出完整内容）保持一致。
- 未发现由该修复方案直接引入的新的重大集成问题。潜在的性能影响和极端情况下的样式表现被认为是可接受的风险。

**状态:** 集成检查完成。
---
### Decision (Bug Fix Strategy - Image Export Enhancement)
[2025-06-11 14:11:00] - 采纳针对图片导出文件名错误 (`.png.png`) 和视觉不一致 (边框导致截断) 问题的修复策略。

**Rationale:**
用户反馈图片导出功能存在文件名错误和视觉不一致的问题。`spec-pseudocode` 模式已提供修复方案。此决策旨在确认并记录这些修复策略。

**Details:**
*   **Affected Files:**
    *   [`hotel-dashboard/components/log-analysis/LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx)
    *   [`hotel-dashboard/components/log-analysis/LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx)
    *   [`hotel-dashboard/lib/exportUtils.ts`](hotel-dashboard/lib/exportUtils.ts)
*   **Filename Fix (`.png.png`):**
    *   **In `LogChartView.tsx` & `LogDisplayArea.tsx`:** 调用 `exportElementAsImage` 时，传递不带 `.png` 后缀的文件名。
        *   例如: `await exportElementAsImage(..., \`some-name-\${Date.now()}\`);`
    *   **In `exportUtils.ts` (`exportElementAsImage` function):**
        *   修改函数签名，参数 `fileName` 更改为 `fileNameWithoutExtension: string`。
        *   内部拼接完整文件名: `const fullFileNameWithExtension = \`\${fileNameWithoutExtension}.\${type}\`;`
*   **Visual Inconsistency & Truncation Fix (in `exportUtils.ts` - `exportElementAsImage` options):**
    *   **Dimensions:** 使用 `width: element.scrollWidth` 和 `height: element.scrollHeight` 确保捕获完整内容。
    *   **Style Overrides:** 在 `domtoimage` 选项的 `style` 对象中添加/修改以下属性，以强制移除可能导致视觉问题的样式：
        *   `'overflow': 'visible !important'`
        *   `'border': 'none !important'`
        *   `'margin': '0 !important'`
        *   `'padding': '0 !important'`
        *   `'box-shadow': 'none !important'`
    *   **Other Options:** 保持 `bgcolor: '#ffffff'` 和 `cacheBust: true`。

**Risk Assessment:**
*   **Filename Fix:** 低风险。
*   **Visual Fix:**
    *   低到中风险。主要风险在于对超大DOM元素的性能影响，以及在极罕见情况下，强制移除样式可能导致的内部布局微小变化。
    *   总体上，预期收益（更准确的图像）远大于风险。

**Impact:**
*   提高了图片导出功能的健壮性和用户体验。
*   对现有架构影响较小，主要集中在 `exportUtils.ts` 和调用该工具的两个组件。
[2025-06-11 14:16:42] Decision: Integration check for image export fixes (filename, visual consistency) completed. Confirmed that the implemented fixes in `exportUtils.ts`, `LogChartView.tsx`, `LogDisplayArea.tsx` and `LOG_ANALYSIS_DEVELOPER_DOCS.md` are conceptually sound and do not introduce new integration issues.

---
### Decision (Code)
[2025-06-11 15:12:00] - 修复多图表导出错误：确保导出前渲染所有选定图表块

**Rationale:**
当用户尝试导出多个图表图片时，如果某个被选中的图表块当前未在页面上显示（因为用户通常一次只查看一个图表块），则会发生 "Block element not found for ID: [block_id]" 错误。这是因为导出函数尝试查找一个尚未渲染的 DOM 元素。修复方案的核心思想是在执行导出操作之前，确保 `LogChartView` 组件接收到并渲染所有被选中用于导出的图表块。

**Details:**
*   **Affected Files:**
    *   [`hotel-dashboard/app/(dashboard)/log-analysis/page.tsx`](hotel-dashboard/app/(dashboard)/log-analysis/page.tsx)
    *   [`hotel-dashboard/components/log-analysis/LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx)
*   **Changes in `page.tsx`:**
    *   **New State:**
        *   `isExportingMode: boolean` (初始 `false`)
        *   `blocksToRenderForExport: ProcessedBlock[]` (初始 `[]`)
        *   `exportTargetContainerRef: React.RefObject<HTMLDivElement>` (使用 `useRef`)
    *   **New Function `initiateExportProcess(exportIds: string[])`:**
        *   由 `LogDisplayArea` 调用，接收待导出块的 ID 数组。
        *   根据 `exportIds` 从 `processedDataChunks` 筛选出块，更新 `blocksToRenderForExport`。
        *   设置 `isExportingMode = true`。
    *   **`useEffect` Hook:**
        *   监听 `isExportingMode` 和 `blocksToRenderForExport`。
        *   当 `isExportingMode` 为 `true` 且 `blocksToRenderForExport` 有内容时：
            *   获取 `exportTargetContainerRef.current`。
            *   遍历 `blocksToRenderForExport`，对每个块ID调用 [`hotel-dashboard/lib/exportUtils.ts`](hotel-dashboard/lib/exportUtils.ts) 中的 `exportElementAsImage(exportTargetContainerRef.current, filenamePrefix, block.id)`。
            *   导出完成后（无论成功或失败），重置 `isExportingMode = false` 和 `blocksToRenderForExport = []`。
    *   **Props to `LogChartView`:**
        *   `dataChunks`: 当 `isExportingMode` 为 `true` 时，传递 `blocksToRenderForExport`；否则传递当前单选模式下的块。
        *   `selectedBlockIds`: 传递 `blocksToRenderForExport.map(b => b.id)` (导出模式) 或 `chartDataForView.map(b => b.id)` (普通模式)，确保 `LogChartView` 内部根据这些ID显示正确的图表。
    *   `LogChartView` 被包裹在一个带有 `ref={exportTargetContainerRef}` 的 `div` 中。
*   **Changes in `LogDisplayArea.tsx`:**
    *   **New Prop:** `onStartExport: (exportIds: string[]) => void;`
    *   **Modified `handleExportAllImages`:**
        *   移除直接调用 `exportElementAsImage` 的逻辑。
        *   改为调用从 `page.tsx` 传递过来的 `onStartExport` prop，并传入当前选中的 `exportBlockIds`。
*   **[`LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx) Verification:**
    *   确认该组件已使用 `selectedBlockIds` prop 来控制其内部渲染的图表卡片的可见性 (通过 `className={selectedBlockIds.includes(chunk.block_id) ? '' : 'hidden'}`)。
    *   确认每个图表卡片都有 `data-block-id={chunk.block_id}` 属性，以便 `exportElementAsImage` 可以通过选择器找到它们。
    *   因此，`LogChartView.tsx` 无需进一步修改。
---
### Decision (Debug)
[2025-06-11 16:38:00] - [Bug Fix Strategy: 修复图表显示和编码检测问题]

**Rationale:**
用户报告了多个控制台错误，包括Recharts图表尺寸警告、useEffect依赖数组变化错误和jschardet编码检测失败。这些问题影响了图表显示和文件上传功能的稳定性。

**Details:**
*   **Affected Files:**
    *   [`hotel-dashboard/components/log-analysis/LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx)
    *   [`hotel-dashboard/components/log-analysis/LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx)
    *   [`hotel-dashboard/components/log-analysis/LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx)
*   **Fixes Implemented:**
    1.  **Recharts图表尺寸问题 (`LogChartView.tsx`):**
        *   将 `ResponsiveContainer` 的 `minWidth` 和 `minHeight` 从 `0` 改为 `300`，确保图表有最小尺寸以正确渲染。
        *   添加了缺失的 `chartContainerRef` 到根容器div，修复导出功能。
    2.  **useEffect依赖数组问题 (`LogDisplayArea.tsx`):**
        *   在第58行的useEffect依赖数组中添加了 `selectedBlockId`，解决React警告的依赖数组大小变化问题。
    3.  **jschardet编码检测错误 (`LogFileUpload.tsx`):**
        *   将传递给 `jschardet.detect()` 的 `Uint8Array` 转换为 `Buffer`，使用 `Buffer.from(uInt8ArrayForDetection)` 替代 `uInt8ArrayForDetection as any`。
        *   这解决了 `aBuf.slice(...).split is not a function` 错误。
---
### Decision (Debug)
[2025-06-12 10:46:15] - [Bug Fix Strategy: 修复 dashboard.tsx 中的嵌套按钮HTML结构错误]

**Rationale:**
在 [`hotel-dashboard/dashboard.tsx`](hotel-dashboard/dashboard.tsx) 的侧边栏导航链接中，由于编辑错误，导致一个 `<button>` 元素（用于“面形数据查询”）被错误地嵌套在另一个 `<button>` 元素（用于“胶合日志分析”）之内。HTML规范不允许 `<button>` 作为另一个 `<button>` 的后代，这会导致React水合错误和页面渲染问题。修复旨在纠正此错误的HTML结构，确保每个按钮都是独立的顶级元素。

**Details:**
*   **Affected File:** [`hotel-dashboard/dashboard.tsx`](hotel-dashboard/dashboard.tsx)
*   **Root Cause:** 在“胶合日志分析”按钮的JSX代码块中，其文本内容被错误地放置在了另一个（“面形数据查询”）按钮元素之后，并且存在一个多余的 `<button` 开标签，导致了嵌套。
*   **Fix Implemented:**
    1.  将“胶合日志分析”按钮的文本内容（“胶合日志分析”）移至其对应的 `<FileText>` 图标之后，但在其父 `<button>` 的结束标签之前。
    2.  删除了导致嵌套的多余的 `<button` 开标签（原第1464行）。
    3.  调整了注释的位置，确保其不再中断按钮元素。
    4.  确保“胶合日志分析”按钮和“面形数据查询”按钮都是独立的兄弟 `<button>` 元素。
*   **Expected Impact:** 消除React水合错误，确保导航按钮正确渲染且功能不受影响。
---
### Decision (Debug)
[2025-06-12 10:55:11] - [Bug Fix Strategy: FTP目录访问错误导致面形数据查询无结果]

**Rationale:**
用户报告FTP目录访问错误 (`550 Directory not found`)，随后在API返回200 OK的情况下，面形数据查询功能仍无结果。根本原因是在 [`hotel-dashboard/.env.local`](hotel-dashboard/.env.local) 文件中，`FTP_BASE_PATH_PRISM` 配置项的值末尾包含一个多余的空格。这个空格导致FTP客户端在构建路径时生成了一个无效的目录名，使得FTP服务器无法找到正确的路径，从而在初始阶段报550错误，或在后续（空格被意外处理或忽略时）虽然连接成功但无法列出或匹配到任何文件。

**Details:**
*   **Affected File:** [`hotel-dashboard/.env.local`](hotel-dashboard/.env.local)
*   **Root Cause:** `FTP_BASE_PATH_PRISM` 环境变量的值为 `"/手动线/一线/大小棱镜面型数据/original_data "` (注意末尾的空格)。
*   **Fix Implemented (by user):** 用户手动编辑了 [`hotel-dashboard/.env.local`](hotel-dashboard/.env.local) 文件，移除了 `FTP_BASE_PATH_PRISM` 值末尾的多余空格。
    *   修正后的值为: `FTP_BASE_PATH_PRISM="/手动线/一线/大小棱镜面型数据/original_data"`
*   **Verification:** 用户确认在手动修正 `.env.local` 文件后，面形数据查询功能已能正常返回结果。
*   **Affected Components/Files:**
    *   [`hotel-dashboard/.env.local`](hotel-dashboard/.env.local) (配置源头)
    *   [`hotel-dashboard/app/api/ftp/search/route.ts`](hotel-dashboard/app/api/ftp/search/route.ts) (读取环境变量并将其用作基础路径)
    *   [`hotel-dashboard/lib/ftpClient.ts`](hotel-dashboard/lib/ftpClient.ts) (使用基础路径执行FTP操作)
---
### Decision (Debug)
[2025-06-12 11:03:00] - [Bug Fix Strategy: FTP Download Concurrency and API Usage]

**Rationale:**
The FTP download functionality was failing due to:
1.  Incorrect API method usage (`downloadToStream` which is not a standard `basic-ftp` client method).
2.  Concurrent access to a single shared FTP client instance, leading to "task still running" errors when multiple files were downloaded simultaneously.

The fix involves refactoring `hotel-dashboard/lib/ftpClient.ts` to:
1.  Use the correct `basic-ftp` method `client.downloadTo()` in conjunction with a `PassThrough` stream to buffer file contents.
2.  Instantiate a new `Client` for each FTP operation (list, search, download) and ensure it's closed after the operation, thus isolating operations and resolving concurrency conflicts.

**Details:**
*   **Affected file:** [`hotel-dashboard/lib/ftpClient.ts`](hotel-dashboard/lib/ftpClient.ts)
*   **Key changes:**
    *   Replaced global FTP client instance with per-operation client instances created by a new `createNewClientAndConnect()` function.
    *   Modified `downloadFileToBuffer` to use `ftpClient.downloadTo(passThroughStream, remotePath)` and read from `passThroughStream`.
    *   Updated `listDirectory` and `searchFiles` to use the per-operation client pattern.
    *   This resolves the `TypeError` and the concurrency errors, allowing multiple files to be downloaded in parallel via the API route, as each download will have its own FTP client session.
---
### Decision (Code)
[2025-06-12 11:40:21] - 创建点云预览模态框组件 PointCloudPreviewModal.tsx

**Rationale:**
为面形数据查询功能提供文件预览能力，允许用户在下载前查看点云数据的俯视图。该组件利用shadcn/ui的Dialog，并使用Canvas API进行渲染，参考了Form1.cs中的可视化逻辑。

**Details:**
*   **UI结构:** Dialog组件包含标题、关闭按钮、Canvas渲染区、加载指示器和错误状态显示。
*   **功能:** 接收SurfaceFile，模拟API调用获取点云数据，在Canvas上渲染俯视图。
*   **Canvas渲染:**
    *   基于点云边界计算缩放。
    *   使用`pointCloudUtils.ts`中的`getColorFromGradient`进行颜色映射。
    *   实现X-Y平面俯视图渲染，Z值映射为颜色。
    *   包含颜色图例。
*   **状态管理:** 使用`useState`管理`loading`, `error`, `pointCloudData`。
*   **参考文件:** [`hotel-dashboard/components/surface-data-query/PointCloudPreviewModal.tsx`](hotel-dashboard/components/surface-data-query/PointCloudPreviewModal.tsx), [`Form1.cs`](Form1.cs) (渲染逻辑参考), [`hotel-dashboard/utils/pointCloudUtils.ts`](hotel-dashboard/utils/pointCloudUtils.ts) (工具函数)
- **2025-06-12 13:01:41**: 部署面形数据预览功能 v0.2.0。使用 `npm run build` 构建项目，并将 `.next` 目录部署到生产服务器。
- **决策**: 采用 `pnpm rebuild canvas` 命令解决构建失败问题。
  - **原因**: 直接删除 `node_modules` 目录的操作被拒绝。`pnpm rebuild` 可以针对性地重新构建 `canvas` 包，以解决原生模块编译失败的问题，同时避免了完全重新安装的开销和风险。
  - **时间**: 2025-06-12 13:16:32
- **决策**: 修改 `next.config.mjs`，将 `canvas` 模块配置为 webpack 的 `externals`，以在构建过程中绕过它。
  - **原因**: 多次尝试通过重新安装和重新构建 `canvas` 依赖均告失败。问题根源很可能在于构建环境与 `canvas` 原生模块的兼容性。通过将其外部化，可以避免在 Next.js 构建时打包该模块，从而可能解决构建失败的问题。
  - **时间**: 2025-06-12 13:20:09
- **决策**: 在API路由中添加对`canvas`模块动态导入的try-catch错误处理。
  - **原因**: 即使在构建成功后，应用在开发模式下运行时仍然无法找到`canvas`的原生模块，导致运行时错误。这表明问题出在服务器的运行环境，而不是构建过程。由于无法直接修改用户的系统环境（如安装系统依赖），最佳策略是使功能可以优雅地失败，而不是让API崩溃。通过捕获导入错误，可以向前端返回一个明确的错误信息，提高了应用的健壮性。
  - **时间**: 2025-06-12 13:26:29
- 2025-06-12 13:37:40: `npm install --global windows-build-tools` failed due to the package being deprecated and unable to download Python 2.7 from a 404 URL. Decided to skip this step as modern Node.js versions may include build tools, and proceed directly to GTK2 installation as per `node-canvas` requirements.
- 2025-06-12 13:42:22: User decided to postpone the installation of `node-canvas` on Windows Server due to complexity. The surface data preview feature will remain disabled until this dependency is installed.
---
### Decision (Code)
[2025-06-13 15:14:31] - [Feature Enhancement: "画图板"功能增强]

**Rationale:**
*   **用户需求:** 增强现有“画图板”功能，增加删除图形、重置画布、导出图片和调整布局的功能，以提高其可用性和实用性。
*   **模块化增强:** 在现有组件（`DrawingBoard`, `Toolbar`, `Canvas`）的基础上进行扩展，保持了代码的模块化和可维护性。

**Implementation Details:**
*   **删除图形:**
    *   在 `DrawingBoard.tsx` 中添加 `deleteShape` 方法来过滤 `shapes` 状态。
    *   在 `Canvas.tsx` 中，将 `onClick` 事件改为 `onMouseDown`，并区分左键（添加）和右键（删除）。
    *   添加了 `findShapeAt` 辅助函数，通过坐标反向查找最上层的图形。
    *   通过 `event.preventDefault()` 禁用了右键菜单。
*   **重置画布:**
    *   在 `DrawingBoard.tsx` 中添加 `resetCanvas` 方法，将 `shapes` 状态设置为空数组。
    *   在 `Toolbar.tsx` 中添加了一个新的“Reset Canvas”按钮，并将其链接到 `resetCanvas` 方法。
*   **导出图片:**
    *   在 `Toolbar.tsx` 中添加了“Export as PNG”和“Export as JPEG”按钮。
    *   在 `DrawingBoard.tsx` 中实现了 `exportCanvas` 方法，该方法使用 `canvas.toDataURL()` 生成图片数据，并动态创建 `<a>` 标签来触发下载。
    *   为了从 `DrawingBoard.tsx` 访问 `canvas` 元素，`Canvas.tsx` 被重构为使用 `React.forwardRef`，并将 `canvas` 的 `ref` 传递给父组件。
*   **布局调整:**
    *   在 `app/(dashboard)/drawing-board/page.tsx` 中，将 `DrawingBoard` 组件包裹在一个新的 `div` 中。
    *   应用了 Tailwind CSS 类 (`w-[90%] h-[90%]`) 来限制画板尺寸，并确保其父容器 (`overflow-auto`) 在内容溢出时提供滚动条。
*   **文件修改:**
    *   [`hotel-dashboard/components/drawing-board/DrawingBoard.tsx`](hotel-dashboard/components/drawing-board/DrawingBoard.tsx)
    *   [`hotel-dashboard/components/drawing-board/Toolbar.tsx`](hotel-dashboard/components/drawing-board/Toolbar.tsx)
    *   [`hotel-dashboard/components/drawing-board/Canvas.tsx`](hotel-dashboard/components/drawing-board/Canvas.tsx)
    *   [`hotel-dashboard/app/(dashboard)/drawing-board/page.tsx`](hotel-dashboard/app/(dashboard)/drawing-board/page.tsx)
---
### Decision (Code)
[2025-06-13 15:47:00] - [Feature Enhancement: "画图板"功能最终调整]

**Rationale:**
*   **用户需求:** 对“画图板”功能进行最终调整，包括确保默认背景色为白色、导出图片时不包含网格、并调整整体高度以避免页面滚动。
*   **代码健壮性:** 提高导出功能的健壮性，使其不依赖于主画布的当前状态（例如网格可见性）。
*   **UI/UX 优化:** 解决页面级滚动条问题，提供更流畅的全屏体验。

**Implementation Details:**
*   **默认背景色:**
    *   **文件:** [`hotel-dashboard/components/drawing-board/Canvas.tsx`](hotel-dashboard/components/drawing-board/Canvas.tsx)
    *   **验证:** 确认 `useEffect` hook 中已存在 `ctx.fillStyle = '#FFFFFF'; ctx.fillRect(0, 0, width, height);` 的逻辑，该逻辑在每次重绘时都将背景设置为白色。此项已满足要求。
*   **导出内容调整 (无网格):**
    *   **文件:** [`hotel-dashboard/components/drawing-board/DrawingBoard.tsx`](hotel-dashboard/components/drawing-board/DrawingBoard.tsx)
    *   **更改:** 重构了 `exportCanvas` 函数。
        1.  创建一个临时的、内存中的 `<canvas>` 元素。
        2.  设置其尺寸与主画布相同。
        3.  获取其 2D 上下文。
        4.  在新画布上用白色填充背景。
        5.  **重新实现了在新画布上绘制所有 `shapes` 的逻辑，但跳过了绘制网格的步骤。**
        6.  使用这个临时画布的 `toDataURL()` 方法生成图片数据。
        7.  通过动态创建 `<a>` 标签触发下载。
*   **高度调整:**
    *   **文件:** [`hotel-dashboard/app/(dashboard)/drawing-board/page.tsx`](hotel-dashboard/app/(dashboard)/drawing-board/page.tsx)
    *   **更改:**
        1.  将根 `div` 的 `h-screen` 修改为 `h-full`，使其能更好地适应父容器的高度。
        2.  将包裹 `<DrawingBoard />` 的 `div` 的 `h-full` 修改为 `min-h-0`。这是一个标准的 Flexbox 修复，用于防止 flex item 在其内容过大时撑破父容器，确保 `flex-grow` 能在可用空间内正确工作，从而避免不必要的滚动条。
---
### Decision (Code)
[2025-06-13 17:29:35] - Refactored square drawing feature to generate MTF (Modulation Transfer Function) pattern instead of a solid color square.

**Rationale:**
This change was requested to provide a more advanced and specific visualization tool for optical analysis within the drawing board application. The MTF pattern is a standard tool for evaluating the performance of optical systems.

**Details:**
- Implemented a new function `drawMtfPattern(ctx, x, y, size, color)` in [`hotel-dashboard/components/drawing-board/Canvas.tsx`](hotel-dashboard/components/drawing-board/Canvas.tsx:109) to draw the pattern, based on the provided Python reference code.
- Modified the `drawShape` function in [`hotel-dashboard/components/drawing-board/Canvas.tsx`](hotel-dashboard/components/drawing-board/Canvas.tsx:138) to call `drawMtfPattern` when the shape type is 'square'.
- Updated the 'Diameter' input in [`hotel-dashboard/components/drawing-board/Toolbar.tsx`](hotel-dashboard/components/drawing-board/Toolbar.tsx:125) to enforce even numbers for the size of the square, as required for the pattern's symmetry.