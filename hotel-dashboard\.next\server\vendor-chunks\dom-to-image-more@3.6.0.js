/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/dom-to-image-more@3.6.0";
exports.ids = ["vendor-chunks/dom-to-image-more@3.6.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/dom-to-image-more@3.6.0/node_modules/dom-to-image-more/dist/dom-to-image-more.min.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/dom-to-image-more@3.6.0/node_modules/dom-to-image-more/dist/dom-to-image-more.min.js ***!
  \*****************************************************************************************************************/
/***/ (function(module) {

eval("/*! dom-to-image-more 08-05-2025 */\n(l=>{let f=(()=>{let e=0;return{escape:function(e){return e.replace(/([.*+?^${}()|[\\]/\\\\])/g,\"\\\\$1\")},isDataUrl:function(e){return-1!==e.search(/^(data:)/)},canvasToBlob:function(t){if(t.toBlob)return new Promise(function(e){t.toBlob(e)});return(r=>new Promise(function(e){var t=u(r.toDataURL().split(\",\")[1]),n=t.length,o=new Uint8Array(n);for(let e=0;e<n;e++)o[e]=t.charCodeAt(e);e(new Blob([o],{type:\"image/png\"}))}))(t)},resolveUrl:function(e,t){var n=document.implementation.createHTMLDocument(),o=n.createElement(\"base\"),r=(n.head.appendChild(o),n.createElement(\"a\"));return n.body.appendChild(r),o.href=t,r.href=e,r.href},getAndEncode:function(s){let e=d.impl.urlCache.find(function(e){return e.url===s});e||(e={url:s,promise:null},d.impl.urlCache.push(e));null===e.promise&&(d.impl.options.cacheBust&&(s+=(/\\?/.test(s)?\"&\":\"?\")+(new Date).getTime()),e.promise=new Promise(function(t){let e=d.impl.options.httpTimeout,r=new XMLHttpRequest;if(r.onreadystatechange=function(){if(4===r.readyState)if(300<=r.status)n?t(n):l(`cannot fetch resource: ${s}, status: `+r.status);else{let e=new FileReader;e.onloadend=function(){t(e.result)},e.readAsDataURL(r.response)}},r.ontimeout=function(){n?t(n):l(`timeout of ${e}ms occured while fetching resource: `+s)},r.responseType=\"blob\",r.timeout=e,0<d.impl.options.useCredentialsFilters.length&&(d.impl.options.useCredentials=0<d.impl.options.useCredentialsFilters.filter(e=>0<=s.search(e)).length),d.impl.options.useCredentials&&(r.withCredentials=!0),d.impl.options.corsImg&&0===s.indexOf(\"http\")&&-1===s.indexOf(window.location.origin)){var i=\"POST\"===(d.impl.options.corsImg.method||\"GET\").toUpperCase()?\"POST\":\"GET\";r.open(i,(d.impl.options.corsImg.url||\"\").replace(\"#{cors}\",s),!0);let t=!1,n=d.impl.options.corsImg.headers||{},o=(Object.keys(n).forEach(function(e){-1!==n[e].indexOf(\"application/json\")&&(t=!0),r.setRequestHeader(e,n[e])}),(e=>{try{return JSON.parse(JSON.stringify(e))}catch(e){l(\"corsImg.data is missing or invalid:\"+e.toString())}})(d.impl.options.corsImg.data||\"\"));Object.keys(o).forEach(function(e){\"string\"==typeof o[e]&&(o[e]=o[e].replace(\"#{cors}\",s))}),r.send(t?JSON.stringify(o):o)}else r.open(\"GET\",s,!0),r.send();let n;function l(e){console.error(e),t(\"\")}d.impl.options.imagePlaceholder&&(i=d.impl.options.imagePlaceholder.split(/,/))&&i[1]&&(n=i[1])}));return e.promise},uid:function(){return\"u\"+(\"0000\"+(Math.random()*Math.pow(36,4)<<0).toString(36)).slice(-4)+e++},delay:function(n){return function(t){return new Promise(function(e){setTimeout(function(){e(t)},n)})}},asArray:function(t){var n=[],o=t.length;for(let e=0;e<o;e++)n.push(t[e]);return n},escapeXhtml:function(e){return e.replace(/%/g,\"%25\").replace(/#/g,\"%23\").replace(/\\n/g,\"%0A\")},makeImage:function(r){return\"data:,\"!==r?new Promise(function(e,t){let n=document.createElementNS(\"http://www.w3.org/2000/svg\",\"svg\"),o=new Image;d.impl.options.useCredentials&&(o.crossOrigin=\"use-credentials\"),o.onload=function(){document.body.removeChild(n),window&&window.requestAnimationFrame?window.requestAnimationFrame(function(){e(o)}):e(o)},o.onerror=e=>{document.body.removeChild(n),t(e)},n.appendChild(o),o.src=r,document.body.appendChild(n)}):Promise.resolve()},width:function(e){var t=i(e,\"width\");if(!isNaN(t))return t;var t=i(e,\"border-left-width\"),n=i(e,\"border-right-width\");return e.scrollWidth+t+n},height:function(e){var t=i(e,\"height\");if(!isNaN(t))return t;var t=i(e,\"border-top-width\"),n=i(e,\"border-bottom-width\");return e.scrollHeight+t+n},getWindow:t,isElement:r,isElementHostForOpenShadowRoot:function(e){return r(e)&&null!==e.shadowRoot},isShadowRoot:n,isInShadowRoot:o,isHTMLElement:function(e){return e instanceof t(e).HTMLElement},isHTMLCanvasElement:function(e){return e instanceof t(e).HTMLCanvasElement},isHTMLInputElement:function(e){return e instanceof t(e).HTMLInputElement},isHTMLImageElement:function(e){return e instanceof t(e).HTMLImageElement},isHTMLLinkElement:function(e){return e instanceof t(e).HTMLLinkElement},isHTMLScriptElement:function(e){return e instanceof t(e).HTMLScriptElement},isHTMLStyleElement:function(e){return e instanceof t(e).HTMLStyleElement},isHTMLTextAreaElement:function(e){return e instanceof t(e).HTMLTextAreaElement},isShadowSlotElement:function(e){return o(e)&&e instanceof t(e).HTMLSlotElement},isSVGElement:function(e){return e instanceof t(e).SVGElement},isSVGRectElement:function(e){return e instanceof t(e).SVGRectElement},isDimensionMissing:function(e){return isNaN(e)||e<=0}};function t(e){e=e?e.ownerDocument:void 0;return(e?e.defaultView:void 0)||window||l}function n(e){return e instanceof t(e).ShadowRoot}function o(e){return null!=e&&void 0!==e.getRootNode&&n(e.getRootNode())}function r(e){return e instanceof t(e).Element}function i(t,n){if(t.nodeType===m){let e=h(t).getPropertyValue(n);if(\"px\"===e.slice(-2))return e=e.slice(0,-2),parseFloat(e)}return NaN}})(),r=(()=>{let o=/url\\(([\"']?)((?:\\\\?.)*?)\\1\\)/gm;return{inlineAll:function(t,o,r){if(!e(t))return Promise.resolve(t);return Promise.resolve(t).then(n).then(function(e){let n=Promise.resolve(t);return e.forEach(function(t){n=n.then(function(e){return i(e,t,o,r)})}),n})},shouldProcess:e,impl:{readUrls:n,inline:i,urlAsRegex:r}};function e(e){return-1!==e.search(o)}function n(e){for(var t,n=[];null!==(t=o.exec(e));)n.push(t[2]);return n.filter(function(e){return!f.isDataUrl(e)})}function r(e){return new RegExp(`url\\\\(([\"']?)(${f.escape(e)})\\\\1\\\\)`,\"gm\")}function i(n,o,t,e){return Promise.resolve(o).then(function(e){return t?f.resolveUrl(e,t):e}).then(e||f.getAndEncode).then(function(e){var t=r(o);return n.replace(t,`url($1${e}$1)`)})}})(),e={resolveAll:function(){return t().then(function(e){return Promise.all(e.map(function(e){return e.resolve()}))}).then(function(e){return e.join(\"\\n\")})},impl:{readAll:t}};function t(){return Promise.resolve(f.asArray(document.styleSheets)).then(function(e){let n=[];return e.forEach(function(t){var e=Object.getPrototypeOf(t);if(Object.prototype.hasOwnProperty.call(e,\"cssRules\"))try{f.asArray(t.cssRules||[]).forEach(n.push.bind(n))}catch(e){console.error(\"domtoimage: Error while reading CSS rules from \"+t.href,e.toString())}}),n}).then(function(e){return e.filter(function(e){return e.type===CSSRule.FONT_FACE_RULE}).filter(function(e){return r.shouldProcess(e.style.getPropertyValue(\"src\"))})}).then(function(e){return e.map(t)});function t(t){return{resolve:function(){var e=(t.parentStyleSheet||{}).href;return r.inlineAll(t.cssText,e)},src:function(){return t.style.getPropertyValue(\"src\")}}}}let n={inlineAll:function t(e){if(!f.isElement(e))return Promise.resolve(e);return n(e).then(function(){return f.isHTMLImageElement(e)?o(e).inline():Promise.all(f.asArray(e.childNodes).map(function(e){return t(e)}))});function n(o){let e=[\"background\",\"background-image\"],t=e.map(function(t){let e=o.style.getPropertyValue(t),n=o.style.getPropertyPriority(t);return e?r.inlineAll(e).then(function(e){o.style.setProperty(t,e,n)}):Promise.resolve()});return Promise.all(t).then(function(){return o})}},impl:{newImage:o}};function o(n){return{inline:function(e){if(f.isDataUrl(n.src))return Promise.resolve();return Promise.resolve(n.src).then(e||f.getAndEncode).then(function(t){return new Promise(function(e){n.onload=e,n.onerror=e,n.src=t})})}}}let s={copyDefaultStyles:!0,imagePlaceholder:void 0,cacheBust:!1,useCredentials:!1,useCredentialsFilters:[],httpTimeout:3e4,styleCaching:\"strict\",corsImg:void 0,adjustClonedNode:void 0,filterStyles:void 0},d={toSvg:a,toPng:function(e,t){return i(e,t).then(function(e){return e.toDataURL()})},toJpeg:function(e,t){return i(e,t).then(function(e){return e.toDataURL(\"image/jpeg\",(t?t.quality:void 0)||1)})},toBlob:function(e,t){return i(e,t).then(f.canvasToBlob)},toPixelData:function(t,e){return i(t,e).then(function(e){return e.getContext(\"2d\").getImageData(0,0,f.width(t),f.height(t)).data})},toCanvas:i,impl:{fontFaces:e,images:n,util:f,inliner:r,urlCache:[],options:{}}},m=( true?module.exports=d:0,(\"undefined\"!=typeof Node?Node.ELEMENT_NODE:void 0)||1),h=(void 0!==l?l.getComputedStyle:void 0)||(\"undefined\"!=typeof window?window.getComputedStyle:void 0)||globalThis.getComputedStyle,u=(void 0!==l?l.atob:void 0)||(\"undefined\"!=typeof window?window.atob:void 0)||globalThis.atob;function a(e,r){let t=d.impl.util.getWindow(e);var n=r=r||{};void 0===n.copyDefaultStyles?d.impl.options.copyDefaultStyles=s.copyDefaultStyles:d.impl.options.copyDefaultStyles=n.copyDefaultStyles,d.impl.options.imagePlaceholder=(void 0===n.imagePlaceholder?s:n).imagePlaceholder,d.impl.options.cacheBust=(void 0===n.cacheBust?s:n).cacheBust,d.impl.options.corsImg=(void 0===n.corsImg?s:n).corsImg,d.impl.options.useCredentials=(void 0===n.useCredentials?s:n).useCredentials,d.impl.options.useCredentialsFilters=(void 0===n.useCredentialsFilters?s:n).useCredentialsFilters,d.impl.options.httpTimeout=(void 0===n.httpTimeout?s:n).httpTimeout,d.impl.options.styleCaching=(void 0===n.styleCaching?s:n).styleCaching;let i=[];return Promise.resolve(e).then(function(e){if(e.nodeType===m)return e;var t=e,n=e.parentNode,o=document.createElement(\"span\");return n.replaceChild(o,t),o.append(e),i.push({parent:n,child:t,wrapper:o}),o}).then(function(e){return function l(t,s,r,u){let e=s.filter;if(t===p||f.isHTMLScriptElement(t)||f.isHTMLStyleElement(t)||f.isHTMLLinkElement(t)||null!==r&&e&&!e(t))return Promise.resolve();return Promise.resolve(t).then(n).then(o).then(function(e){return c(e,a(t))}).then(i).then(function(e){return d(e,t)});function n(e){return f.isHTMLCanvasElement(e)?f.makeImage(e.toDataURL()):e.cloneNode(!1)}function o(e){return s.adjustClonedNode&&s.adjustClonedNode(t,e,!1),Promise.resolve(e)}function i(e){return s.adjustClonedNode&&s.adjustClonedNode(t,e,!0),Promise.resolve(e)}function a(e){return f.isElementHostForOpenShadowRoot(e)?e.shadowRoot:e}function c(n,e){let o=t(e),r=Promise.resolve();if(0!==o.length){let t=h(i(e));f.asArray(o).forEach(function(e){r=r.then(function(){return l(e,s,t,u).then(function(e){e&&n.appendChild(e)})})})}return r.then(function(){return n});function i(e){return f.isShadowRoot(e)?e.host:e}function t(t){if(f.isShadowSlotElement(t)){let e=t.assignedNodes();if(e&&0<e.length)return e}return t.childNodes}}function d(u,a){return!f.isElement(u)||f.isShadowSlotElement(a)?Promise.resolve(u):Promise.resolve().then(e).then(t).then(n).then(o).then(function(){return u});function e(){function o(e,t){t.font=e.font,t.fontFamily=e.fontFamily,t.fontFeatureSettings=e.fontFeatureSettings,t.fontKerning=e.fontKerning,t.fontSize=e.fontSize,t.fontStretch=e.fontStretch,t.fontStyle=e.fontStyle,t.fontVariant=e.fontVariant,t.fontVariantCaps=e.fontVariantCaps,t.fontVariantEastAsian=e.fontVariantEastAsian,t.fontVariantLigatures=e.fontVariantLigatures,t.fontVariantNumeric=e.fontVariantNumeric,t.fontVariationSettings=e.fontVariationSettings,t.fontWeight=e.fontWeight}function e(e,t){let n=h(e);n.cssText?(t.style.cssText=n.cssText,o(n,t.style)):(y(s,e,n,r,t),null===r&&([\"inset-block\",\"inset-block-start\",\"inset-block-end\"].forEach(e=>t.style.removeProperty(e)),[\"left\",\"right\",\"top\",\"bottom\"].forEach(e=>{t.style.getPropertyValue(e)&&t.style.setProperty(e,\"0px\")})))}e(a,u)}function t(){let s=f.uid();function t(r){let i=h(a,r),l=i.getPropertyValue(\"content\");if(\"\"!==l&&\"none\"!==l){let e=u.getAttribute(\"class\")||\"\",t=(u.setAttribute(\"class\",e+\" \"+s),document.createElement(\"style\"));function n(){let e=`.${s}:`+r,t=(i.cssText?n:o)();return document.createTextNode(e+`{${t}}`);function n(){return`${i.cssText} content: ${l};`}function o(){let e=f.asArray(i).map(t).join(\"; \");return e+\";\";function t(e){let t=i.getPropertyValue(e),n=i.getPropertyPriority(e)?\" !important\":\"\";return e+\": \"+t+n}}}t.appendChild(n()),u.appendChild(t)}}[\":before\",\":after\"].forEach(function(e){t(e)})}function n(){f.isHTMLTextAreaElement(a)&&(u.innerHTML=a.value),f.isHTMLInputElement(a)&&u.setAttribute(\"value\",a.value)}function o(){f.isSVGElement(u)&&(u.setAttribute(\"xmlns\",\"http://www.w3.org/2000/svg\"),f.isSVGRectElement(u))&&[\"width\",\"height\"].forEach(function(e){let t=u.getAttribute(e);t&&u.style.setProperty(e,t)})}}}(e,r,null,t)}).then(r.disableEmbedFonts?Promise.resolve(e):c).then(g).then(function(t){r.bgcolor&&(t.style.backgroundColor=r.bgcolor);r.width&&(t.style.width=r.width+\"px\");r.height&&(t.style.height=r.height+\"px\");r.style&&Object.keys(r.style).forEach(function(e){t.style[e]=r.style[e]});let e=null;\"function\"==typeof r.onclone&&(e=r.onclone(t));return Promise.resolve(e).then(function(){return t})}).then(function(e){let n=r.width||f.width(e),o=r.height||f.height(e);return Promise.resolve(e).then(function(e){return e.setAttribute(\"xmlns\",\"http://www.w3.org/1999/xhtml\"),(new XMLSerializer).serializeToString(e)}).then(f.escapeXhtml).then(function(e){var t=(f.isDimensionMissing(n)?' width=\"100%\"':` width=\"${n}\"`)+(f.isDimensionMissing(o)?' height=\"100%\"':` height=\"${o}\"`);return`<svg xmlns=\"http://www.w3.org/2000/svg\"${(f.isDimensionMissing(n)?\"\":` width=\"${n}\"`)+(f.isDimensionMissing(o)?\"\":` height=\"${o}\"`)}><foreignObject${t}>${e}</foreignObject></svg>`}).then(function(e){return\"data:image/svg+xml;charset=utf-8,\"+e})}).then(function(e){for(;0<i.length;){var t=i.pop();t.parent.replaceChild(t.child,t.wrapper)}return e}).then(function(e){return d.impl.urlCache=[],(()=>{p&&(document.body.removeChild(p),p=null),v&&clearTimeout(v),v=setTimeout(()=>{v=null,w={}},2e4)})(),e})}function i(r,i){return a(r,i=i||{}).then(f.makeImage).then(function(e){var t=\"number\"!=typeof i.scale?1:i.scale,n=((e,t)=>{let n=i.width||f.width(e),o=i.height||f.height(e);return f.isDimensionMissing(n)&&(n=f.isDimensionMissing(o)?300:2*o),f.isDimensionMissing(o)&&(o=n/2),(e=document.createElement(\"canvas\")).width=n*t,e.height=o*t,i.bgcolor&&((t=e.getContext(\"2d\")).fillStyle=i.bgcolor,t.fillRect(0,0,e.width,e.height)),e})(r,t),o=n.getContext(\"2d\");return o.msImageSmoothingEnabled=!1,o.imageSmoothingEnabled=!1,e&&(o.scale(t,t),o.drawImage(e,0,0)),n})}let p=null;function c(n){return e.resolveAll().then(function(e){var t;return\"\"!==e&&(t=document.createElement(\"style\"),n.appendChild(t),t.appendChild(document.createTextNode(e))),n})}function g(e){return n.inlineAll(e).then(function(){return e})}function y(i,l,s,u,e){let a=d.impl.options.copyDefaultStyles?((t,e)=>{var n,o=(e=>(\"relaxed\"!==t.styleCaching?e:e.filter((e,t,n)=>0===t||t===n.length-1)).join(\">\"))(e=(e=>{var t=[];do{if(e.nodeType===m){var n=e.tagName;if(t.push(n),E.includes(n))break}}while(e=e.parentNode);return t})(e));{if(w[o])return w[o];e=((e,t)=>{let n=e.body;do{var o=t.pop(),o=e.createElement(o);n.appendChild(o),n=o}while(0<t.length);return n.textContent=\"​\",n})((n=(()=>{if(p)return p.contentWindow;t=document.characterSet||\"UTF-8\",e=(e=document.doctype)?(`<!DOCTYPE ${s(e.name)} ${s(e.publicId)} `+s(e.systemId)).trim()+\">\":\"\",(p=document.createElement(\"iframe\")).id=\"domtoimage-sandbox-\"+f.uid(),p.style.visibility=\"hidden\",p.style.position=\"fixed\",document.body.appendChild(p);var e,t,n=p,o=\"domtoimage-sandbox\";try{return n.contentWindow.document.write(e+`<html><head><meta charset='${t}'><title>${o}</title></head><body></body></html>`),n.contentWindow}catch(e){}var r=document.createElement(\"meta\");r.setAttribute(\"charset\",t);try{var i=document.implementation.createHTMLDocument(o),l=(i.head.appendChild(r),e+i.documentElement.outerHTML);return n.setAttribute(\"srcdoc\",l),n.contentWindow}catch(e){}return n.contentDocument.head.appendChild(r),n.contentDocument.title=o,n.contentWindow;function s(e){var t;return e?((t=document.createElement(\"div\")).innerText=e,t.innerHTML):\"\"}})()).document,e),n=((e,t)=>{let n={},o=e.getComputedStyle(t);return f.asArray(o).forEach(function(e){n[e]=\"width\"===e||\"height\"===e?\"auto\":o.getPropertyValue(e)}),n})(n,e);var r=e;do{var i=r.parentElement;null!==i&&i.removeChild(r),r=i}while(r&&\"BODY\"!==r.tagName);return w[o]=n}})(i,l):{},c=e.style;f.asArray(s).forEach(function(e){var t,n,o,r;i.filterStyles&&!i.filterStyles(l,e)||(n=s.getPropertyValue(e),o=a[e],t=u?u.getPropertyValue(e):void 0,c.getPropertyValue(e))||(n!==o||u&&n!==t)&&(o=s.getPropertyPriority(e),t=c,n=n,o=o,r=0<=[\"background-clip\"].indexOf(e=e),o?(t.setProperty(e,n,o),r&&t.setProperty(\"-webkit-\"+e,n,o)):(t.setProperty(e,n),r&&t.setProperty(\"-webkit-\"+e,n)))})}let v=null,w={},E=[\"ADDRESS\",\"ARTICLE\",\"ASIDE\",\"BLOCKQUOTE\",\"DETAILS\",\"DIALOG\",\"DD\",\"DIV\",\"DL\",\"DT\",\"FIELDSET\",\"FIGCAPTION\",\"FIGURE\",\"FOOTER\",\"FORM\",\"H1\",\"H2\",\"H3\",\"H4\",\"H5\",\"H6\",\"HEADER\",\"HGROUP\",\"HR\",\"LI\",\"MAIN\",\"NAV\",\"OL\",\"P\",\"PRE\",\"SECTION\",\"SVG\",\"TABLE\",\"UL\",\"math\",\"svg\",\"BODY\",\"HEAD\",\"HTML\"]})(this);\n//# sourceMappingURL=dom-to-image-more.min.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vZG9tLXRvLWltYWdlLW1vcmVAMy42LjAvbm9kZV9tb2R1bGVzL2RvbS10by1pbWFnZS1tb3JlL2Rpc3QvZG9tLXRvLWltYWdlLW1vcmUubWluLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0EsS0FBSyxZQUFZLFFBQVEsT0FBTyxtQkFBbUIsNEJBQTRCLHNCQUFzQix1QkFBdUIsZ0NBQWdDLDBCQUEwQiwyQ0FBMkMsWUFBWSxFQUFFLGtDQUFrQyxvRUFBb0UsWUFBWSxJQUFJLHlCQUF5QixnQkFBZ0IsaUJBQWlCLEdBQUcsTUFBTSwwQkFBMEIsNEhBQTRILHNEQUFzRCwwQkFBMEIsdUNBQXVDLGlCQUFpQixFQUFFLE9BQU8sbUJBQW1CLDBCQUEwQixnSUFBZ0ksc0RBQXNELG1DQUFtQyx3RUFBd0UsRUFBRSxzQkFBc0IsS0FBSyxxQkFBcUIsdUJBQXVCLFlBQVksOEJBQThCLHdCQUF3Qix1QkFBdUIsRUFBRSx5Q0FBeUMsdVVBQXVVLGlGQUFpRixxREFBcUQsS0FBSyxTQUFTLDZDQUE2Qyx1Q0FBdUMseUVBQXlFLE9BQU8sSUFBSSxxQ0FBcUMsU0FBUyx1REFBdUQsb0NBQW9DLG1DQUFtQyw2Q0FBNkMsS0FBSyxNQUFNLGdDQUFnQyxpQ0FBaUMsTUFBTSxjQUFjLHVCQUF1QixnR0FBZ0csR0FBRyxpQkFBaUIsZ0JBQWdCLGdGQUFnRixtQkFBbUIsbUJBQW1CLCtCQUErQixzQkFBc0IsS0FBSyxJQUFJLEdBQUcscUJBQXFCLG9CQUFvQixZQUFZLElBQUksaUJBQWlCLFNBQVMseUJBQXlCLHNFQUFzRSx1QkFBdUIsNkNBQTZDLCtFQUErRSxxRkFBcUYsMEdBQTBHLEtBQUssT0FBTyxlQUFlLGtDQUFrQyx1REFBdUQsb0JBQW9CLG1CQUFtQixtQkFBbUIsc0JBQXNCLDJEQUEyRCx5QkFBeUIsb0JBQW9CLG9CQUFvQixzQkFBc0IsMkRBQTJELDBCQUEwQixvRUFBb0UsaUNBQWlDLDJEQUEyRCxxQ0FBcUMsaUNBQWlDLDJDQUEyQyxnQ0FBZ0MsMENBQTBDLGdDQUFnQywwQ0FBMEMsK0JBQStCLHlDQUF5QyxpQ0FBaUMsMkNBQTJDLGdDQUFnQywwQ0FBMEMsbUNBQW1DLDZDQUE2QyxpQ0FBaUMsK0NBQStDLDBCQUEwQixvQ0FBb0MsOEJBQThCLHdDQUF3QyxnQ0FBZ0Msd0JBQXdCLGNBQWMsMkJBQTJCLDBDQUEwQyxjQUFjLG9DQUFvQyxjQUFjLDJEQUEyRCxjQUFjLGlDQUFpQyxnQkFBZ0IsbUJBQW1CLCtCQUErQiwyREFBMkQsWUFBWSxZQUFZLHVDQUF1QyxPQUFPLDBCQUEwQixtQ0FBbUMsbURBQW1ELHlCQUF5Qiw2QkFBNkIscUJBQXFCLGtCQUFrQixFQUFFLElBQUksRUFBRSx1QkFBdUIsbUNBQW1DLGNBQWMsdUJBQXVCLGNBQWMsZUFBZSxxQkFBcUIsY0FBYyw0QkFBNEIsc0JBQXNCLEVBQUUsY0FBYyxtQ0FBbUMsWUFBWSxlQUFlLG9CQUFvQiwyQ0FBMkMsNkJBQTZCLDJDQUEyQyxXQUFXLDRCQUE0QixFQUFFLE1BQU0sR0FBRyxPQUFPLHNCQUFzQiw0QkFBNEIscUNBQXFDLG1CQUFtQixHQUFHLG1CQUFtQixvQkFBb0IsRUFBRSxPQUFPLFlBQVksYUFBYSx5RUFBeUUsU0FBUyw2QkFBNkIsK0JBQStCLDBEQUEwRCxrREFBa0QsU0FBUyxzRkFBc0YsSUFBSSxtQkFBbUIsNEJBQTRCLHVDQUF1QyxxQkFBcUIsd0RBQXdELEVBQUUsbUJBQW1CLGdCQUFnQixFQUFFLGNBQWMsT0FBTyxtQkFBbUIsNkJBQTZCLE9BQU8sZ0NBQWdDLGdCQUFnQiwwQ0FBMEMsT0FBTyx3QkFBd0IsNkNBQTZDLDRCQUE0QixpR0FBaUcsWUFBWSxHQUFHLEVBQUUsY0FBYyw0REFBNEQsbUVBQW1FLHlDQUF5QywyQkFBMkIsb0JBQW9CLEVBQUUsc0NBQXNDLFNBQVMsR0FBRyxPQUFPLGFBQWEsY0FBYyxPQUFPLG1CQUFtQiwrQ0FBK0MsdUVBQXVFLCtCQUErQiwrQkFBK0IsRUFBRSxJQUFJLE9BQU8sc01BQXNNLElBQUksNEJBQTRCLCtCQUErQixxQkFBcUIsRUFBRSxzQkFBc0IsK0JBQStCLHlEQUF5RCxFQUFFLHNCQUFzQixtQ0FBbUMsMkJBQTJCLCtCQUErQix3RUFBd0UsRUFBRSxrQkFBa0IsOERBQThELElBQUksS0FBaUQsa0JBQWtCLENBQWMsMlJBQTJSLGdCQUFnQiwrQkFBK0IsY0FBYywwb0JBQTBvQixTQUFTLDJDQUEyQywyQkFBMkIsd0RBQXdELCtDQUErQywyQkFBMkIsSUFBSSxtQkFBbUIsMkJBQTJCLGVBQWUsaUlBQWlJLDJEQUEyRCxpQkFBaUIsMkJBQTJCLGNBQWMsRUFBRSxjQUFjLDJFQUEyRSxjQUFjLHlFQUF5RSxjQUFjLHlFQUF5RSxjQUFjLDBEQUEwRCxnQkFBZ0IsK0JBQStCLGlCQUFpQixjQUFjLGlDQUFpQyxvQkFBb0IsbUNBQW1DLG9CQUFvQixFQUFFLEVBQUUsRUFBRSx5QkFBeUIsU0FBUyxFQUFFLGNBQWMsa0NBQWtDLGNBQWMsNkJBQTZCLHdCQUF3QiwwQkFBMEIscUJBQXFCLGdCQUFnQixxSUFBcUksU0FBUyxFQUFFLGFBQWEsZ0JBQWdCLDBjQUEwYyxnQkFBZ0IsV0FBVyxvTkFBb04sMERBQTBELElBQUksT0FBTyxhQUFhLGNBQWMsY0FBYyw2Q0FBNkMsdUJBQXVCLHNHQUFzRyxhQUFhLFVBQVUsRUFBRSx5QkFBeUIsbUNBQW1DLEVBQUUsR0FBRyxHQUFHLGFBQWEsU0FBUyxXQUFXLFdBQVcsR0FBRyxFQUFFLGFBQWEsa0NBQWtDLEdBQUcsV0FBVyxFQUFFLGNBQWMsd0VBQXdFLG9CQUFvQixxQ0FBcUMseUNBQXlDLEtBQUssRUFBRSxhQUFhLDJHQUEyRyxhQUFhLHdJQUF3SSx3QkFBd0IsNEJBQTRCLElBQUksYUFBYSwwRUFBMEUsK0NBQStDLHNDQUFzQyx5Q0FBeUMsa0RBQWtELHNCQUFzQixFQUFFLFdBQVcsK0NBQStDLDBDQUEwQyxTQUFTLEVBQUUsbUJBQW1CLGtEQUFrRCwyQ0FBMkMsdUdBQXVHLHVDQUF1QywwREFBMEQsRUFBRSwwREFBMEQsRUFBRSxJQUFJLGdEQUFnRCx1Q0FBdUMsRUFBRSw0Q0FBNEMsRUFBRSxJQUFJLGlCQUFpQixFQUFFLEdBQUcsRUFBRSx3QkFBd0IsbUJBQW1CLDBCQUEwQixrQkFBa0IsRUFBRSxtQkFBbUIsS0FBSyxXQUFXLEVBQUUsY0FBYyx5Q0FBeUMsU0FBUyxtQkFBbUIsZ0NBQWdDLDhFQUE4RSxZQUFZLE1BQU0sTUFBTSxFQUFFLGdCQUFnQixrQkFBa0IscUNBQXFDLG9EQUFvRCxrREFBa0QsNFBBQTRQLDRCQUE0QixzR0FBc0csRUFBRSxXQUFXLGNBQWMsdUNBQXVDLE1BQU0sK0dBQStHLEVBQUUsY0FBYyxzQ0FBc0MsU0FBUyxFQUFFLHNCQUFzQixnREFBZ0Qsc0dBQXNHLFNBQVMsR0FBRyxtQkFBbUIsZ0JBQWdCLGtDQUFrQyxzQkFBc0IsU0FBUyxPQUFPLG9CQUFvQixXQUFXLGFBQWEsR0FBRyxtQ0FBbUMscUJBQXFCLGtCQUFrQiwyQkFBMkIsV0FBVyw0QkFBNEIsc0VBQXNFLFdBQVcsRUFBRSxlQUFlLHVMQUF1TCxtQ0FBbUMsSUFBSSxzRUFBc0UsRUFBRSxXQUFXLEVBQUUsc0RBQXNELFVBQVUscUNBQXFDLDRCQUE0QixJQUFJLDRHQUE0RyxrREFBa0QsVUFBVSx1RkFBdUYsY0FBYyxNQUFNLHlFQUF5RSw0QkFBNEIsUUFBUSx5QkFBeUIsd0NBQXdDLDREQUE0RCxJQUFJLE9BQU8sUUFBUSxHQUFHLHNCQUFzQiwrQkFBK0IsNkJBQTZCLGVBQWUsU0FBUyxXQUFXLGlDQUFpQyxZQUFZLG1WQUFtVixFQUFFLGVBQWUsNFJBQTRSO0FBQy9zZ0IiLCJzb3VyY2VzIjpbIkQ6XFxweWNvZGVcXHN1cHBvcnRfY2hhcnQyXFxob3RlbC1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGRvbS10by1pbWFnZS1tb3JlQDMuNi4wXFxub2RlX21vZHVsZXNcXGRvbS10by1pbWFnZS1tb3JlXFxkaXN0XFxkb20tdG8taW1hZ2UtbW9yZS5taW4uanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyohIGRvbS10by1pbWFnZS1tb3JlIDA4LTA1LTIwMjUgKi9cbihsPT57bGV0IGY9KCgpPT57bGV0IGU9MDtyZXR1cm57ZXNjYXBlOmZ1bmN0aW9uKGUpe3JldHVybiBlLnJlcGxhY2UoLyhbLiorP14ke30oKXxbXFxdL1xcXFxdKS9nLFwiXFxcXCQxXCIpfSxpc0RhdGFVcmw6ZnVuY3Rpb24oZSl7cmV0dXJuLTEhPT1lLnNlYXJjaCgvXihkYXRhOikvKX0sY2FudmFzVG9CbG9iOmZ1bmN0aW9uKHQpe2lmKHQudG9CbG9iKXJldHVybiBuZXcgUHJvbWlzZShmdW5jdGlvbihlKXt0LnRvQmxvYihlKX0pO3JldHVybihyPT5uZXcgUHJvbWlzZShmdW5jdGlvbihlKXt2YXIgdD11KHIudG9EYXRhVVJMKCkuc3BsaXQoXCIsXCIpWzFdKSxuPXQubGVuZ3RoLG89bmV3IFVpbnQ4QXJyYXkobik7Zm9yKGxldCBlPTA7ZTxuO2UrKylvW2VdPXQuY2hhckNvZGVBdChlKTtlKG5ldyBCbG9iKFtvXSx7dHlwZTpcImltYWdlL3BuZ1wifSkpfSkpKHQpfSxyZXNvbHZlVXJsOmZ1bmN0aW9uKGUsdCl7dmFyIG49ZG9jdW1lbnQuaW1wbGVtZW50YXRpb24uY3JlYXRlSFRNTERvY3VtZW50KCksbz1uLmNyZWF0ZUVsZW1lbnQoXCJiYXNlXCIpLHI9KG4uaGVhZC5hcHBlbmRDaGlsZChvKSxuLmNyZWF0ZUVsZW1lbnQoXCJhXCIpKTtyZXR1cm4gbi5ib2R5LmFwcGVuZENoaWxkKHIpLG8uaHJlZj10LHIuaHJlZj1lLHIuaHJlZn0sZ2V0QW5kRW5jb2RlOmZ1bmN0aW9uKHMpe2xldCBlPWQuaW1wbC51cmxDYWNoZS5maW5kKGZ1bmN0aW9uKGUpe3JldHVybiBlLnVybD09PXN9KTtlfHwoZT17dXJsOnMscHJvbWlzZTpudWxsfSxkLmltcGwudXJsQ2FjaGUucHVzaChlKSk7bnVsbD09PWUucHJvbWlzZSYmKGQuaW1wbC5vcHRpb25zLmNhY2hlQnVzdCYmKHMrPSgvXFw/Ly50ZXN0KHMpP1wiJlwiOlwiP1wiKSsobmV3IERhdGUpLmdldFRpbWUoKSksZS5wcm9taXNlPW5ldyBQcm9taXNlKGZ1bmN0aW9uKHQpe2xldCBlPWQuaW1wbC5vcHRpb25zLmh0dHBUaW1lb3V0LHI9bmV3IFhNTEh0dHBSZXF1ZXN0O2lmKHIub25yZWFkeXN0YXRlY2hhbmdlPWZ1bmN0aW9uKCl7aWYoND09PXIucmVhZHlTdGF0ZSlpZigzMDA8PXIuc3RhdHVzKW4/dChuKTpsKGBjYW5ub3QgZmV0Y2ggcmVzb3VyY2U6ICR7c30sIHN0YXR1czogYCtyLnN0YXR1cyk7ZWxzZXtsZXQgZT1uZXcgRmlsZVJlYWRlcjtlLm9ubG9hZGVuZD1mdW5jdGlvbigpe3QoZS5yZXN1bHQpfSxlLnJlYWRBc0RhdGFVUkwoci5yZXNwb25zZSl9fSxyLm9udGltZW91dD1mdW5jdGlvbigpe24/dChuKTpsKGB0aW1lb3V0IG9mICR7ZX1tcyBvY2N1cmVkIHdoaWxlIGZldGNoaW5nIHJlc291cmNlOiBgK3MpfSxyLnJlc3BvbnNlVHlwZT1cImJsb2JcIixyLnRpbWVvdXQ9ZSwwPGQuaW1wbC5vcHRpb25zLnVzZUNyZWRlbnRpYWxzRmlsdGVycy5sZW5ndGgmJihkLmltcGwub3B0aW9ucy51c2VDcmVkZW50aWFscz0wPGQuaW1wbC5vcHRpb25zLnVzZUNyZWRlbnRpYWxzRmlsdGVycy5maWx0ZXIoZT0+MDw9cy5zZWFyY2goZSkpLmxlbmd0aCksZC5pbXBsLm9wdGlvbnMudXNlQ3JlZGVudGlhbHMmJihyLndpdGhDcmVkZW50aWFscz0hMCksZC5pbXBsLm9wdGlvbnMuY29yc0ltZyYmMD09PXMuaW5kZXhPZihcImh0dHBcIikmJi0xPT09cy5pbmRleE9mKHdpbmRvdy5sb2NhdGlvbi5vcmlnaW4pKXt2YXIgaT1cIlBPU1RcIj09PShkLmltcGwub3B0aW9ucy5jb3JzSW1nLm1ldGhvZHx8XCJHRVRcIikudG9VcHBlckNhc2UoKT9cIlBPU1RcIjpcIkdFVFwiO3Iub3BlbihpLChkLmltcGwub3B0aW9ucy5jb3JzSW1nLnVybHx8XCJcIikucmVwbGFjZShcIiN7Y29yc31cIixzKSwhMCk7bGV0IHQ9ITEsbj1kLmltcGwub3B0aW9ucy5jb3JzSW1nLmhlYWRlcnN8fHt9LG89KE9iamVjdC5rZXlzKG4pLmZvckVhY2goZnVuY3Rpb24oZSl7LTEhPT1uW2VdLmluZGV4T2YoXCJhcHBsaWNhdGlvbi9qc29uXCIpJiYodD0hMCksci5zZXRSZXF1ZXN0SGVhZGVyKGUsbltlXSl9KSwoZT0+e3RyeXtyZXR1cm4gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShlKSl9Y2F0Y2goZSl7bChcImNvcnNJbWcuZGF0YSBpcyBtaXNzaW5nIG9yIGludmFsaWQ6XCIrZS50b1N0cmluZygpKX19KShkLmltcGwub3B0aW9ucy5jb3JzSW1nLmRhdGF8fFwiXCIpKTtPYmplY3Qua2V5cyhvKS5mb3JFYWNoKGZ1bmN0aW9uKGUpe1wic3RyaW5nXCI9PXR5cGVvZiBvW2VdJiYob1tlXT1vW2VdLnJlcGxhY2UoXCIje2NvcnN9XCIscykpfSksci5zZW5kKHQ/SlNPTi5zdHJpbmdpZnkobyk6byl9ZWxzZSByLm9wZW4oXCJHRVRcIixzLCEwKSxyLnNlbmQoKTtsZXQgbjtmdW5jdGlvbiBsKGUpe2NvbnNvbGUuZXJyb3IoZSksdChcIlwiKX1kLmltcGwub3B0aW9ucy5pbWFnZVBsYWNlaG9sZGVyJiYoaT1kLmltcGwub3B0aW9ucy5pbWFnZVBsYWNlaG9sZGVyLnNwbGl0KC8sLykpJiZpWzFdJiYobj1pWzFdKX0pKTtyZXR1cm4gZS5wcm9taXNlfSx1aWQ6ZnVuY3Rpb24oKXtyZXR1cm5cInVcIisoXCIwMDAwXCIrKE1hdGgucmFuZG9tKCkqTWF0aC5wb3coMzYsNCk8PDApLnRvU3RyaW5nKDM2KSkuc2xpY2UoLTQpK2UrK30sZGVsYXk6ZnVuY3Rpb24obil7cmV0dXJuIGZ1bmN0aW9uKHQpe3JldHVybiBuZXcgUHJvbWlzZShmdW5jdGlvbihlKXtzZXRUaW1lb3V0KGZ1bmN0aW9uKCl7ZSh0KX0sbil9KX19LGFzQXJyYXk6ZnVuY3Rpb24odCl7dmFyIG49W10sbz10Lmxlbmd0aDtmb3IobGV0IGU9MDtlPG87ZSsrKW4ucHVzaCh0W2VdKTtyZXR1cm4gbn0sZXNjYXBlWGh0bWw6ZnVuY3Rpb24oZSl7cmV0dXJuIGUucmVwbGFjZSgvJS9nLFwiJTI1XCIpLnJlcGxhY2UoLyMvZyxcIiUyM1wiKS5yZXBsYWNlKC9cXG4vZyxcIiUwQVwiKX0sbWFrZUltYWdlOmZ1bmN0aW9uKHIpe3JldHVyblwiZGF0YTosXCIhPT1yP25ldyBQcm9taXNlKGZ1bmN0aW9uKGUsdCl7bGV0IG49ZG9jdW1lbnQuY3JlYXRlRWxlbWVudE5TKFwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIixcInN2Z1wiKSxvPW5ldyBJbWFnZTtkLmltcGwub3B0aW9ucy51c2VDcmVkZW50aWFscyYmKG8uY3Jvc3NPcmlnaW49XCJ1c2UtY3JlZGVudGlhbHNcIiksby5vbmxvYWQ9ZnVuY3Rpb24oKXtkb2N1bWVudC5ib2R5LnJlbW92ZUNoaWxkKG4pLHdpbmRvdyYmd2luZG93LnJlcXVlc3RBbmltYXRpb25GcmFtZT93aW5kb3cucmVxdWVzdEFuaW1hdGlvbkZyYW1lKGZ1bmN0aW9uKCl7ZShvKX0pOmUobyl9LG8ub25lcnJvcj1lPT57ZG9jdW1lbnQuYm9keS5yZW1vdmVDaGlsZChuKSx0KGUpfSxuLmFwcGVuZENoaWxkKG8pLG8uc3JjPXIsZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChuKX0pOlByb21pc2UucmVzb2x2ZSgpfSx3aWR0aDpmdW5jdGlvbihlKXt2YXIgdD1pKGUsXCJ3aWR0aFwiKTtpZighaXNOYU4odCkpcmV0dXJuIHQ7dmFyIHQ9aShlLFwiYm9yZGVyLWxlZnQtd2lkdGhcIiksbj1pKGUsXCJib3JkZXItcmlnaHQtd2lkdGhcIik7cmV0dXJuIGUuc2Nyb2xsV2lkdGgrdCtufSxoZWlnaHQ6ZnVuY3Rpb24oZSl7dmFyIHQ9aShlLFwiaGVpZ2h0XCIpO2lmKCFpc05hTih0KSlyZXR1cm4gdDt2YXIgdD1pKGUsXCJib3JkZXItdG9wLXdpZHRoXCIpLG49aShlLFwiYm9yZGVyLWJvdHRvbS13aWR0aFwiKTtyZXR1cm4gZS5zY3JvbGxIZWlnaHQrdCtufSxnZXRXaW5kb3c6dCxpc0VsZW1lbnQ6cixpc0VsZW1lbnRIb3N0Rm9yT3BlblNoYWRvd1Jvb3Q6ZnVuY3Rpb24oZSl7cmV0dXJuIHIoZSkmJm51bGwhPT1lLnNoYWRvd1Jvb3R9LGlzU2hhZG93Um9vdDpuLGlzSW5TaGFkb3dSb290Om8saXNIVE1MRWxlbWVudDpmdW5jdGlvbihlKXtyZXR1cm4gZSBpbnN0YW5jZW9mIHQoZSkuSFRNTEVsZW1lbnR9LGlzSFRNTENhbnZhc0VsZW1lbnQ6ZnVuY3Rpb24oZSl7cmV0dXJuIGUgaW5zdGFuY2VvZiB0KGUpLkhUTUxDYW52YXNFbGVtZW50fSxpc0hUTUxJbnB1dEVsZW1lbnQ6ZnVuY3Rpb24oZSl7cmV0dXJuIGUgaW5zdGFuY2VvZiB0KGUpLkhUTUxJbnB1dEVsZW1lbnR9LGlzSFRNTEltYWdlRWxlbWVudDpmdW5jdGlvbihlKXtyZXR1cm4gZSBpbnN0YW5jZW9mIHQoZSkuSFRNTEltYWdlRWxlbWVudH0saXNIVE1MTGlua0VsZW1lbnQ6ZnVuY3Rpb24oZSl7cmV0dXJuIGUgaW5zdGFuY2VvZiB0KGUpLkhUTUxMaW5rRWxlbWVudH0saXNIVE1MU2NyaXB0RWxlbWVudDpmdW5jdGlvbihlKXtyZXR1cm4gZSBpbnN0YW5jZW9mIHQoZSkuSFRNTFNjcmlwdEVsZW1lbnR9LGlzSFRNTFN0eWxlRWxlbWVudDpmdW5jdGlvbihlKXtyZXR1cm4gZSBpbnN0YW5jZW9mIHQoZSkuSFRNTFN0eWxlRWxlbWVudH0saXNIVE1MVGV4dEFyZWFFbGVtZW50OmZ1bmN0aW9uKGUpe3JldHVybiBlIGluc3RhbmNlb2YgdChlKS5IVE1MVGV4dEFyZWFFbGVtZW50fSxpc1NoYWRvd1Nsb3RFbGVtZW50OmZ1bmN0aW9uKGUpe3JldHVybiBvKGUpJiZlIGluc3RhbmNlb2YgdChlKS5IVE1MU2xvdEVsZW1lbnR9LGlzU1ZHRWxlbWVudDpmdW5jdGlvbihlKXtyZXR1cm4gZSBpbnN0YW5jZW9mIHQoZSkuU1ZHRWxlbWVudH0saXNTVkdSZWN0RWxlbWVudDpmdW5jdGlvbihlKXtyZXR1cm4gZSBpbnN0YW5jZW9mIHQoZSkuU1ZHUmVjdEVsZW1lbnR9LGlzRGltZW5zaW9uTWlzc2luZzpmdW5jdGlvbihlKXtyZXR1cm4gaXNOYU4oZSl8fGU8PTB9fTtmdW5jdGlvbiB0KGUpe2U9ZT9lLm93bmVyRG9jdW1lbnQ6dm9pZCAwO3JldHVybihlP2UuZGVmYXVsdFZpZXc6dm9pZCAwKXx8d2luZG93fHxsfWZ1bmN0aW9uIG4oZSl7cmV0dXJuIGUgaW5zdGFuY2VvZiB0KGUpLlNoYWRvd1Jvb3R9ZnVuY3Rpb24gbyhlKXtyZXR1cm4gbnVsbCE9ZSYmdm9pZCAwIT09ZS5nZXRSb290Tm9kZSYmbihlLmdldFJvb3ROb2RlKCkpfWZ1bmN0aW9uIHIoZSl7cmV0dXJuIGUgaW5zdGFuY2VvZiB0KGUpLkVsZW1lbnR9ZnVuY3Rpb24gaSh0LG4pe2lmKHQubm9kZVR5cGU9PT1tKXtsZXQgZT1oKHQpLmdldFByb3BlcnR5VmFsdWUobik7aWYoXCJweFwiPT09ZS5zbGljZSgtMikpcmV0dXJuIGU9ZS5zbGljZSgwLC0yKSxwYXJzZUZsb2F0KGUpfXJldHVybiBOYU59fSkoKSxyPSgoKT0+e2xldCBvPS91cmxcXCgoW1wiJ10/KSgoPzpcXFxcPy4pKj8pXFwxXFwpL2dtO3JldHVybntpbmxpbmVBbGw6ZnVuY3Rpb24odCxvLHIpe2lmKCFlKHQpKXJldHVybiBQcm9taXNlLnJlc29sdmUodCk7cmV0dXJuIFByb21pc2UucmVzb2x2ZSh0KS50aGVuKG4pLnRoZW4oZnVuY3Rpb24oZSl7bGV0IG49UHJvbWlzZS5yZXNvbHZlKHQpO3JldHVybiBlLmZvckVhY2goZnVuY3Rpb24odCl7bj1uLnRoZW4oZnVuY3Rpb24oZSl7cmV0dXJuIGkoZSx0LG8scil9KX0pLG59KX0sc2hvdWxkUHJvY2VzczplLGltcGw6e3JlYWRVcmxzOm4saW5saW5lOmksdXJsQXNSZWdleDpyfX07ZnVuY3Rpb24gZShlKXtyZXR1cm4tMSE9PWUuc2VhcmNoKG8pfWZ1bmN0aW9uIG4oZSl7Zm9yKHZhciB0LG49W107bnVsbCE9PSh0PW8uZXhlYyhlKSk7KW4ucHVzaCh0WzJdKTtyZXR1cm4gbi5maWx0ZXIoZnVuY3Rpb24oZSl7cmV0dXJuIWYuaXNEYXRhVXJsKGUpfSl9ZnVuY3Rpb24gcihlKXtyZXR1cm4gbmV3IFJlZ0V4cChgdXJsXFxcXCgoW1wiJ10/KSgke2YuZXNjYXBlKGUpfSlcXFxcMVxcXFwpYCxcImdtXCIpfWZ1bmN0aW9uIGkobixvLHQsZSl7cmV0dXJuIFByb21pc2UucmVzb2x2ZShvKS50aGVuKGZ1bmN0aW9uKGUpe3JldHVybiB0P2YucmVzb2x2ZVVybChlLHQpOmV9KS50aGVuKGV8fGYuZ2V0QW5kRW5jb2RlKS50aGVuKGZ1bmN0aW9uKGUpe3ZhciB0PXIobyk7cmV0dXJuIG4ucmVwbGFjZSh0LGB1cmwoJDEke2V9JDEpYCl9KX19KSgpLGU9e3Jlc29sdmVBbGw6ZnVuY3Rpb24oKXtyZXR1cm4gdCgpLnRoZW4oZnVuY3Rpb24oZSl7cmV0dXJuIFByb21pc2UuYWxsKGUubWFwKGZ1bmN0aW9uKGUpe3JldHVybiBlLnJlc29sdmUoKX0pKX0pLnRoZW4oZnVuY3Rpb24oZSl7cmV0dXJuIGUuam9pbihcIlxcblwiKX0pfSxpbXBsOntyZWFkQWxsOnR9fTtmdW5jdGlvbiB0KCl7cmV0dXJuIFByb21pc2UucmVzb2x2ZShmLmFzQXJyYXkoZG9jdW1lbnQuc3R5bGVTaGVldHMpKS50aGVuKGZ1bmN0aW9uKGUpe2xldCBuPVtdO3JldHVybiBlLmZvckVhY2goZnVuY3Rpb24odCl7dmFyIGU9T2JqZWN0LmdldFByb3RvdHlwZU9mKHQpO2lmKE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChlLFwiY3NzUnVsZXNcIikpdHJ5e2YuYXNBcnJheSh0LmNzc1J1bGVzfHxbXSkuZm9yRWFjaChuLnB1c2guYmluZChuKSl9Y2F0Y2goZSl7Y29uc29sZS5lcnJvcihcImRvbXRvaW1hZ2U6IEVycm9yIHdoaWxlIHJlYWRpbmcgQ1NTIHJ1bGVzIGZyb20gXCIrdC5ocmVmLGUudG9TdHJpbmcoKSl9fSksbn0pLnRoZW4oZnVuY3Rpb24oZSl7cmV0dXJuIGUuZmlsdGVyKGZ1bmN0aW9uKGUpe3JldHVybiBlLnR5cGU9PT1DU1NSdWxlLkZPTlRfRkFDRV9SVUxFfSkuZmlsdGVyKGZ1bmN0aW9uKGUpe3JldHVybiByLnNob3VsZFByb2Nlc3MoZS5zdHlsZS5nZXRQcm9wZXJ0eVZhbHVlKFwic3JjXCIpKX0pfSkudGhlbihmdW5jdGlvbihlKXtyZXR1cm4gZS5tYXAodCl9KTtmdW5jdGlvbiB0KHQpe3JldHVybntyZXNvbHZlOmZ1bmN0aW9uKCl7dmFyIGU9KHQucGFyZW50U3R5bGVTaGVldHx8e30pLmhyZWY7cmV0dXJuIHIuaW5saW5lQWxsKHQuY3NzVGV4dCxlKX0sc3JjOmZ1bmN0aW9uKCl7cmV0dXJuIHQuc3R5bGUuZ2V0UHJvcGVydHlWYWx1ZShcInNyY1wiKX19fX1sZXQgbj17aW5saW5lQWxsOmZ1bmN0aW9uIHQoZSl7aWYoIWYuaXNFbGVtZW50KGUpKXJldHVybiBQcm9taXNlLnJlc29sdmUoZSk7cmV0dXJuIG4oZSkudGhlbihmdW5jdGlvbigpe3JldHVybiBmLmlzSFRNTEltYWdlRWxlbWVudChlKT9vKGUpLmlubGluZSgpOlByb21pc2UuYWxsKGYuYXNBcnJheShlLmNoaWxkTm9kZXMpLm1hcChmdW5jdGlvbihlKXtyZXR1cm4gdChlKX0pKX0pO2Z1bmN0aW9uIG4obyl7bGV0IGU9W1wiYmFja2dyb3VuZFwiLFwiYmFja2dyb3VuZC1pbWFnZVwiXSx0PWUubWFwKGZ1bmN0aW9uKHQpe2xldCBlPW8uc3R5bGUuZ2V0UHJvcGVydHlWYWx1ZSh0KSxuPW8uc3R5bGUuZ2V0UHJvcGVydHlQcmlvcml0eSh0KTtyZXR1cm4gZT9yLmlubGluZUFsbChlKS50aGVuKGZ1bmN0aW9uKGUpe28uc3R5bGUuc2V0UHJvcGVydHkodCxlLG4pfSk6UHJvbWlzZS5yZXNvbHZlKCl9KTtyZXR1cm4gUHJvbWlzZS5hbGwodCkudGhlbihmdW5jdGlvbigpe3JldHVybiBvfSl9fSxpbXBsOntuZXdJbWFnZTpvfX07ZnVuY3Rpb24gbyhuKXtyZXR1cm57aW5saW5lOmZ1bmN0aW9uKGUpe2lmKGYuaXNEYXRhVXJsKG4uc3JjKSlyZXR1cm4gUHJvbWlzZS5yZXNvbHZlKCk7cmV0dXJuIFByb21pc2UucmVzb2x2ZShuLnNyYykudGhlbihlfHxmLmdldEFuZEVuY29kZSkudGhlbihmdW5jdGlvbih0KXtyZXR1cm4gbmV3IFByb21pc2UoZnVuY3Rpb24oZSl7bi5vbmxvYWQ9ZSxuLm9uZXJyb3I9ZSxuLnNyYz10fSl9KX19fWxldCBzPXtjb3B5RGVmYXVsdFN0eWxlczohMCxpbWFnZVBsYWNlaG9sZGVyOnZvaWQgMCxjYWNoZUJ1c3Q6ITEsdXNlQ3JlZGVudGlhbHM6ITEsdXNlQ3JlZGVudGlhbHNGaWx0ZXJzOltdLGh0dHBUaW1lb3V0OjNlNCxzdHlsZUNhY2hpbmc6XCJzdHJpY3RcIixjb3JzSW1nOnZvaWQgMCxhZGp1c3RDbG9uZWROb2RlOnZvaWQgMCxmaWx0ZXJTdHlsZXM6dm9pZCAwfSxkPXt0b1N2ZzphLHRvUG5nOmZ1bmN0aW9uKGUsdCl7cmV0dXJuIGkoZSx0KS50aGVuKGZ1bmN0aW9uKGUpe3JldHVybiBlLnRvRGF0YVVSTCgpfSl9LHRvSnBlZzpmdW5jdGlvbihlLHQpe3JldHVybiBpKGUsdCkudGhlbihmdW5jdGlvbihlKXtyZXR1cm4gZS50b0RhdGFVUkwoXCJpbWFnZS9qcGVnXCIsKHQ/dC5xdWFsaXR5OnZvaWQgMCl8fDEpfSl9LHRvQmxvYjpmdW5jdGlvbihlLHQpe3JldHVybiBpKGUsdCkudGhlbihmLmNhbnZhc1RvQmxvYil9LHRvUGl4ZWxEYXRhOmZ1bmN0aW9uKHQsZSl7cmV0dXJuIGkodCxlKS50aGVuKGZ1bmN0aW9uKGUpe3JldHVybiBlLmdldENvbnRleHQoXCIyZFwiKS5nZXRJbWFnZURhdGEoMCwwLGYud2lkdGgodCksZi5oZWlnaHQodCkpLmRhdGF9KX0sdG9DYW52YXM6aSxpbXBsOntmb250RmFjZXM6ZSxpbWFnZXM6bix1dGlsOmYsaW5saW5lcjpyLHVybENhY2hlOltdLG9wdGlvbnM6e319fSxtPShcIm9iamVjdFwiPT10eXBlb2YgZXhwb3J0cyYmXCJvYmplY3RcIj09dHlwZW9mIG1vZHVsZT9tb2R1bGUuZXhwb3J0cz1kOmwuZG9tdG9pbWFnZT1kLChcInVuZGVmaW5lZFwiIT10eXBlb2YgTm9kZT9Ob2RlLkVMRU1FTlRfTk9ERTp2b2lkIDApfHwxKSxoPSh2b2lkIDAhPT1sP2wuZ2V0Q29tcHV0ZWRTdHlsZTp2b2lkIDApfHwoXCJ1bmRlZmluZWRcIiE9dHlwZW9mIHdpbmRvdz93aW5kb3cuZ2V0Q29tcHV0ZWRTdHlsZTp2b2lkIDApfHxnbG9iYWxUaGlzLmdldENvbXB1dGVkU3R5bGUsdT0odm9pZCAwIT09bD9sLmF0b2I6dm9pZCAwKXx8KFwidW5kZWZpbmVkXCIhPXR5cGVvZiB3aW5kb3c/d2luZG93LmF0b2I6dm9pZCAwKXx8Z2xvYmFsVGhpcy5hdG9iO2Z1bmN0aW9uIGEoZSxyKXtsZXQgdD1kLmltcGwudXRpbC5nZXRXaW5kb3coZSk7dmFyIG49cj1yfHx7fTt2b2lkIDA9PT1uLmNvcHlEZWZhdWx0U3R5bGVzP2QuaW1wbC5vcHRpb25zLmNvcHlEZWZhdWx0U3R5bGVzPXMuY29weURlZmF1bHRTdHlsZXM6ZC5pbXBsLm9wdGlvbnMuY29weURlZmF1bHRTdHlsZXM9bi5jb3B5RGVmYXVsdFN0eWxlcyxkLmltcGwub3B0aW9ucy5pbWFnZVBsYWNlaG9sZGVyPSh2b2lkIDA9PT1uLmltYWdlUGxhY2Vob2xkZXI/czpuKS5pbWFnZVBsYWNlaG9sZGVyLGQuaW1wbC5vcHRpb25zLmNhY2hlQnVzdD0odm9pZCAwPT09bi5jYWNoZUJ1c3Q/czpuKS5jYWNoZUJ1c3QsZC5pbXBsLm9wdGlvbnMuY29yc0ltZz0odm9pZCAwPT09bi5jb3JzSW1nP3M6bikuY29yc0ltZyxkLmltcGwub3B0aW9ucy51c2VDcmVkZW50aWFscz0odm9pZCAwPT09bi51c2VDcmVkZW50aWFscz9zOm4pLnVzZUNyZWRlbnRpYWxzLGQuaW1wbC5vcHRpb25zLnVzZUNyZWRlbnRpYWxzRmlsdGVycz0odm9pZCAwPT09bi51c2VDcmVkZW50aWFsc0ZpbHRlcnM/czpuKS51c2VDcmVkZW50aWFsc0ZpbHRlcnMsZC5pbXBsLm9wdGlvbnMuaHR0cFRpbWVvdXQ9KHZvaWQgMD09PW4uaHR0cFRpbWVvdXQ/czpuKS5odHRwVGltZW91dCxkLmltcGwub3B0aW9ucy5zdHlsZUNhY2hpbmc9KHZvaWQgMD09PW4uc3R5bGVDYWNoaW5nP3M6bikuc3R5bGVDYWNoaW5nO2xldCBpPVtdO3JldHVybiBQcm9taXNlLnJlc29sdmUoZSkudGhlbihmdW5jdGlvbihlKXtpZihlLm5vZGVUeXBlPT09bSlyZXR1cm4gZTt2YXIgdD1lLG49ZS5wYXJlbnROb2RlLG89ZG9jdW1lbnQuY3JlYXRlRWxlbWVudChcInNwYW5cIik7cmV0dXJuIG4ucmVwbGFjZUNoaWxkKG8sdCksby5hcHBlbmQoZSksaS5wdXNoKHtwYXJlbnQ6bixjaGlsZDp0LHdyYXBwZXI6b30pLG99KS50aGVuKGZ1bmN0aW9uKGUpe3JldHVybiBmdW5jdGlvbiBsKHQscyxyLHUpe2xldCBlPXMuZmlsdGVyO2lmKHQ9PT1wfHxmLmlzSFRNTFNjcmlwdEVsZW1lbnQodCl8fGYuaXNIVE1MU3R5bGVFbGVtZW50KHQpfHxmLmlzSFRNTExpbmtFbGVtZW50KHQpfHxudWxsIT09ciYmZSYmIWUodCkpcmV0dXJuIFByb21pc2UucmVzb2x2ZSgpO3JldHVybiBQcm9taXNlLnJlc29sdmUodCkudGhlbihuKS50aGVuKG8pLnRoZW4oZnVuY3Rpb24oZSl7cmV0dXJuIGMoZSxhKHQpKX0pLnRoZW4oaSkudGhlbihmdW5jdGlvbihlKXtyZXR1cm4gZChlLHQpfSk7ZnVuY3Rpb24gbihlKXtyZXR1cm4gZi5pc0hUTUxDYW52YXNFbGVtZW50KGUpP2YubWFrZUltYWdlKGUudG9EYXRhVVJMKCkpOmUuY2xvbmVOb2RlKCExKX1mdW5jdGlvbiBvKGUpe3JldHVybiBzLmFkanVzdENsb25lZE5vZGUmJnMuYWRqdXN0Q2xvbmVkTm9kZSh0LGUsITEpLFByb21pc2UucmVzb2x2ZShlKX1mdW5jdGlvbiBpKGUpe3JldHVybiBzLmFkanVzdENsb25lZE5vZGUmJnMuYWRqdXN0Q2xvbmVkTm9kZSh0LGUsITApLFByb21pc2UucmVzb2x2ZShlKX1mdW5jdGlvbiBhKGUpe3JldHVybiBmLmlzRWxlbWVudEhvc3RGb3JPcGVuU2hhZG93Um9vdChlKT9lLnNoYWRvd1Jvb3Q6ZX1mdW5jdGlvbiBjKG4sZSl7bGV0IG89dChlKSxyPVByb21pc2UucmVzb2x2ZSgpO2lmKDAhPT1vLmxlbmd0aCl7bGV0IHQ9aChpKGUpKTtmLmFzQXJyYXkobykuZm9yRWFjaChmdW5jdGlvbihlKXtyPXIudGhlbihmdW5jdGlvbigpe3JldHVybiBsKGUscyx0LHUpLnRoZW4oZnVuY3Rpb24oZSl7ZSYmbi5hcHBlbmRDaGlsZChlKX0pfSl9KX1yZXR1cm4gci50aGVuKGZ1bmN0aW9uKCl7cmV0dXJuIG59KTtmdW5jdGlvbiBpKGUpe3JldHVybiBmLmlzU2hhZG93Um9vdChlKT9lLmhvc3Q6ZX1mdW5jdGlvbiB0KHQpe2lmKGYuaXNTaGFkb3dTbG90RWxlbWVudCh0KSl7bGV0IGU9dC5hc3NpZ25lZE5vZGVzKCk7aWYoZSYmMDxlLmxlbmd0aClyZXR1cm4gZX1yZXR1cm4gdC5jaGlsZE5vZGVzfX1mdW5jdGlvbiBkKHUsYSl7cmV0dXJuIWYuaXNFbGVtZW50KHUpfHxmLmlzU2hhZG93U2xvdEVsZW1lbnQoYSk/UHJvbWlzZS5yZXNvbHZlKHUpOlByb21pc2UucmVzb2x2ZSgpLnRoZW4oZSkudGhlbih0KS50aGVuKG4pLnRoZW4obykudGhlbihmdW5jdGlvbigpe3JldHVybiB1fSk7ZnVuY3Rpb24gZSgpe2Z1bmN0aW9uIG8oZSx0KXt0LmZvbnQ9ZS5mb250LHQuZm9udEZhbWlseT1lLmZvbnRGYW1pbHksdC5mb250RmVhdHVyZVNldHRpbmdzPWUuZm9udEZlYXR1cmVTZXR0aW5ncyx0LmZvbnRLZXJuaW5nPWUuZm9udEtlcm5pbmcsdC5mb250U2l6ZT1lLmZvbnRTaXplLHQuZm9udFN0cmV0Y2g9ZS5mb250U3RyZXRjaCx0LmZvbnRTdHlsZT1lLmZvbnRTdHlsZSx0LmZvbnRWYXJpYW50PWUuZm9udFZhcmlhbnQsdC5mb250VmFyaWFudENhcHM9ZS5mb250VmFyaWFudENhcHMsdC5mb250VmFyaWFudEVhc3RBc2lhbj1lLmZvbnRWYXJpYW50RWFzdEFzaWFuLHQuZm9udFZhcmlhbnRMaWdhdHVyZXM9ZS5mb250VmFyaWFudExpZ2F0dXJlcyx0LmZvbnRWYXJpYW50TnVtZXJpYz1lLmZvbnRWYXJpYW50TnVtZXJpYyx0LmZvbnRWYXJpYXRpb25TZXR0aW5ncz1lLmZvbnRWYXJpYXRpb25TZXR0aW5ncyx0LmZvbnRXZWlnaHQ9ZS5mb250V2VpZ2h0fWZ1bmN0aW9uIGUoZSx0KXtsZXQgbj1oKGUpO24uY3NzVGV4dD8odC5zdHlsZS5jc3NUZXh0PW4uY3NzVGV4dCxvKG4sdC5zdHlsZSkpOih5KHMsZSxuLHIsdCksbnVsbD09PXImJihbXCJpbnNldC1ibG9ja1wiLFwiaW5zZXQtYmxvY2stc3RhcnRcIixcImluc2V0LWJsb2NrLWVuZFwiXS5mb3JFYWNoKGU9PnQuc3R5bGUucmVtb3ZlUHJvcGVydHkoZSkpLFtcImxlZnRcIixcInJpZ2h0XCIsXCJ0b3BcIixcImJvdHRvbVwiXS5mb3JFYWNoKGU9Pnt0LnN0eWxlLmdldFByb3BlcnR5VmFsdWUoZSkmJnQuc3R5bGUuc2V0UHJvcGVydHkoZSxcIjBweFwiKX0pKSl9ZShhLHUpfWZ1bmN0aW9uIHQoKXtsZXQgcz1mLnVpZCgpO2Z1bmN0aW9uIHQocil7bGV0IGk9aChhLHIpLGw9aS5nZXRQcm9wZXJ0eVZhbHVlKFwiY29udGVudFwiKTtpZihcIlwiIT09bCYmXCJub25lXCIhPT1sKXtsZXQgZT11LmdldEF0dHJpYnV0ZShcImNsYXNzXCIpfHxcIlwiLHQ9KHUuc2V0QXR0cmlidXRlKFwiY2xhc3NcIixlK1wiIFwiK3MpLGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoXCJzdHlsZVwiKSk7ZnVuY3Rpb24gbigpe2xldCBlPWAuJHtzfTpgK3IsdD0oaS5jc3NUZXh0P246bykoKTtyZXR1cm4gZG9jdW1lbnQuY3JlYXRlVGV4dE5vZGUoZStgeyR7dH19YCk7ZnVuY3Rpb24gbigpe3JldHVybmAke2kuY3NzVGV4dH0gY29udGVudDogJHtsfTtgfWZ1bmN0aW9uIG8oKXtsZXQgZT1mLmFzQXJyYXkoaSkubWFwKHQpLmpvaW4oXCI7IFwiKTtyZXR1cm4gZStcIjtcIjtmdW5jdGlvbiB0KGUpe2xldCB0PWkuZ2V0UHJvcGVydHlWYWx1ZShlKSxuPWkuZ2V0UHJvcGVydHlQcmlvcml0eShlKT9cIiAhaW1wb3J0YW50XCI6XCJcIjtyZXR1cm4gZStcIjogXCIrdCtufX19dC5hcHBlbmRDaGlsZChuKCkpLHUuYXBwZW5kQ2hpbGQodCl9fVtcIjpiZWZvcmVcIixcIjphZnRlclwiXS5mb3JFYWNoKGZ1bmN0aW9uKGUpe3QoZSl9KX1mdW5jdGlvbiBuKCl7Zi5pc0hUTUxUZXh0QXJlYUVsZW1lbnQoYSkmJih1LmlubmVySFRNTD1hLnZhbHVlKSxmLmlzSFRNTElucHV0RWxlbWVudChhKSYmdS5zZXRBdHRyaWJ1dGUoXCJ2YWx1ZVwiLGEudmFsdWUpfWZ1bmN0aW9uIG8oKXtmLmlzU1ZHRWxlbWVudCh1KSYmKHUuc2V0QXR0cmlidXRlKFwieG1sbnNcIixcImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCIpLGYuaXNTVkdSZWN0RWxlbWVudCh1KSkmJltcIndpZHRoXCIsXCJoZWlnaHRcIl0uZm9yRWFjaChmdW5jdGlvbihlKXtsZXQgdD11LmdldEF0dHJpYnV0ZShlKTt0JiZ1LnN0eWxlLnNldFByb3BlcnR5KGUsdCl9KX19fShlLHIsbnVsbCx0KX0pLnRoZW4oci5kaXNhYmxlRW1iZWRGb250cz9Qcm9taXNlLnJlc29sdmUoZSk6YykudGhlbihnKS50aGVuKGZ1bmN0aW9uKHQpe3IuYmdjb2xvciYmKHQuc3R5bGUuYmFja2dyb3VuZENvbG9yPXIuYmdjb2xvcik7ci53aWR0aCYmKHQuc3R5bGUud2lkdGg9ci53aWR0aCtcInB4XCIpO3IuaGVpZ2h0JiYodC5zdHlsZS5oZWlnaHQ9ci5oZWlnaHQrXCJweFwiKTtyLnN0eWxlJiZPYmplY3Qua2V5cyhyLnN0eWxlKS5mb3JFYWNoKGZ1bmN0aW9uKGUpe3Quc3R5bGVbZV09ci5zdHlsZVtlXX0pO2xldCBlPW51bGw7XCJmdW5jdGlvblwiPT10eXBlb2Ygci5vbmNsb25lJiYoZT1yLm9uY2xvbmUodCkpO3JldHVybiBQcm9taXNlLnJlc29sdmUoZSkudGhlbihmdW5jdGlvbigpe3JldHVybiB0fSl9KS50aGVuKGZ1bmN0aW9uKGUpe2xldCBuPXIud2lkdGh8fGYud2lkdGgoZSksbz1yLmhlaWdodHx8Zi5oZWlnaHQoZSk7cmV0dXJuIFByb21pc2UucmVzb2x2ZShlKS50aGVuKGZ1bmN0aW9uKGUpe3JldHVybiBlLnNldEF0dHJpYnV0ZShcInhtbG5zXCIsXCJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hodG1sXCIpLChuZXcgWE1MU2VyaWFsaXplcikuc2VyaWFsaXplVG9TdHJpbmcoZSl9KS50aGVuKGYuZXNjYXBlWGh0bWwpLnRoZW4oZnVuY3Rpb24oZSl7dmFyIHQ9KGYuaXNEaW1lbnNpb25NaXNzaW5nKG4pPycgd2lkdGg9XCIxMDAlXCInOmAgd2lkdGg9XCIke259XCJgKSsoZi5pc0RpbWVuc2lvbk1pc3Npbmcobyk/JyBoZWlnaHQ9XCIxMDAlXCInOmAgaGVpZ2h0PVwiJHtvfVwiYCk7cmV0dXJuYDxzdmcgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiJHsoZi5pc0RpbWVuc2lvbk1pc3Npbmcobik/XCJcIjpgIHdpZHRoPVwiJHtufVwiYCkrKGYuaXNEaW1lbnNpb25NaXNzaW5nKG8pP1wiXCI6YCBoZWlnaHQ9XCIke299XCJgKX0+PGZvcmVpZ25PYmplY3Qke3R9PiR7ZX08L2ZvcmVpZ25PYmplY3Q+PC9zdmc+YH0pLnRoZW4oZnVuY3Rpb24oZSl7cmV0dXJuXCJkYXRhOmltYWdlL3N2Zyt4bWw7Y2hhcnNldD11dGYtOCxcIitlfSl9KS50aGVuKGZ1bmN0aW9uKGUpe2Zvcig7MDxpLmxlbmd0aDspe3ZhciB0PWkucG9wKCk7dC5wYXJlbnQucmVwbGFjZUNoaWxkKHQuY2hpbGQsdC53cmFwcGVyKX1yZXR1cm4gZX0pLnRoZW4oZnVuY3Rpb24oZSl7cmV0dXJuIGQuaW1wbC51cmxDYWNoZT1bXSwoKCk9PntwJiYoZG9jdW1lbnQuYm9keS5yZW1vdmVDaGlsZChwKSxwPW51bGwpLHYmJmNsZWFyVGltZW91dCh2KSx2PXNldFRpbWVvdXQoKCk9Pnt2PW51bGwsdz17fX0sMmU0KX0pKCksZX0pfWZ1bmN0aW9uIGkocixpKXtyZXR1cm4gYShyLGk9aXx8e30pLnRoZW4oZi5tYWtlSW1hZ2UpLnRoZW4oZnVuY3Rpb24oZSl7dmFyIHQ9XCJudW1iZXJcIiE9dHlwZW9mIGkuc2NhbGU/MTppLnNjYWxlLG49KChlLHQpPT57bGV0IG49aS53aWR0aHx8Zi53aWR0aChlKSxvPWkuaGVpZ2h0fHxmLmhlaWdodChlKTtyZXR1cm4gZi5pc0RpbWVuc2lvbk1pc3NpbmcobikmJihuPWYuaXNEaW1lbnNpb25NaXNzaW5nKG8pPzMwMDoyKm8pLGYuaXNEaW1lbnNpb25NaXNzaW5nKG8pJiYobz1uLzIpLChlPWRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoXCJjYW52YXNcIikpLndpZHRoPW4qdCxlLmhlaWdodD1vKnQsaS5iZ2NvbG9yJiYoKHQ9ZS5nZXRDb250ZXh0KFwiMmRcIikpLmZpbGxTdHlsZT1pLmJnY29sb3IsdC5maWxsUmVjdCgwLDAsZS53aWR0aCxlLmhlaWdodCkpLGV9KShyLHQpLG89bi5nZXRDb250ZXh0KFwiMmRcIik7cmV0dXJuIG8ubXNJbWFnZVNtb290aGluZ0VuYWJsZWQ9ITEsby5pbWFnZVNtb290aGluZ0VuYWJsZWQ9ITEsZSYmKG8uc2NhbGUodCx0KSxvLmRyYXdJbWFnZShlLDAsMCkpLG59KX1sZXQgcD1udWxsO2Z1bmN0aW9uIGMobil7cmV0dXJuIGUucmVzb2x2ZUFsbCgpLnRoZW4oZnVuY3Rpb24oZSl7dmFyIHQ7cmV0dXJuXCJcIiE9PWUmJih0PWRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoXCJzdHlsZVwiKSxuLmFwcGVuZENoaWxkKHQpLHQuYXBwZW5kQ2hpbGQoZG9jdW1lbnQuY3JlYXRlVGV4dE5vZGUoZSkpKSxufSl9ZnVuY3Rpb24gZyhlKXtyZXR1cm4gbi5pbmxpbmVBbGwoZSkudGhlbihmdW5jdGlvbigpe3JldHVybiBlfSl9ZnVuY3Rpb24geShpLGwscyx1LGUpe2xldCBhPWQuaW1wbC5vcHRpb25zLmNvcHlEZWZhdWx0U3R5bGVzPygodCxlKT0+e3ZhciBuLG89KGU9PihcInJlbGF4ZWRcIiE9PXQuc3R5bGVDYWNoaW5nP2U6ZS5maWx0ZXIoKGUsdCxuKT0+MD09PXR8fHQ9PT1uLmxlbmd0aC0xKSkuam9pbihcIj5cIikpKGU9KGU9Pnt2YXIgdD1bXTtkb3tpZihlLm5vZGVUeXBlPT09bSl7dmFyIG49ZS50YWdOYW1lO2lmKHQucHVzaChuKSxFLmluY2x1ZGVzKG4pKWJyZWFrfX13aGlsZShlPWUucGFyZW50Tm9kZSk7cmV0dXJuIHR9KShlKSk7e2lmKHdbb10pcmV0dXJuIHdbb107ZT0oKGUsdCk9PntsZXQgbj1lLmJvZHk7ZG97dmFyIG89dC5wb3AoKSxvPWUuY3JlYXRlRWxlbWVudChvKTtuLmFwcGVuZENoaWxkKG8pLG49b313aGlsZSgwPHQubGVuZ3RoKTtyZXR1cm4gbi50ZXh0Q29udGVudD1cIuKAi1wiLG59KSgobj0oKCk9PntpZihwKXJldHVybiBwLmNvbnRlbnRXaW5kb3c7dD1kb2N1bWVudC5jaGFyYWN0ZXJTZXR8fFwiVVRGLThcIixlPShlPWRvY3VtZW50LmRvY3R5cGUpPyhgPCFET0NUWVBFICR7cyhlLm5hbWUpfSAke3MoZS5wdWJsaWNJZCl9IGArcyhlLnN5c3RlbUlkKSkudHJpbSgpK1wiPlwiOlwiXCIsKHA9ZG9jdW1lbnQuY3JlYXRlRWxlbWVudChcImlmcmFtZVwiKSkuaWQ9XCJkb210b2ltYWdlLXNhbmRib3gtXCIrZi51aWQoKSxwLnN0eWxlLnZpc2liaWxpdHk9XCJoaWRkZW5cIixwLnN0eWxlLnBvc2l0aW9uPVwiZml4ZWRcIixkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKHApO3ZhciBlLHQsbj1wLG89XCJkb210b2ltYWdlLXNhbmRib3hcIjt0cnl7cmV0dXJuIG4uY29udGVudFdpbmRvdy5kb2N1bWVudC53cml0ZShlK2A8aHRtbD48aGVhZD48bWV0YSBjaGFyc2V0PScke3R9Jz48dGl0bGU+JHtvfTwvdGl0bGU+PC9oZWFkPjxib2R5PjwvYm9keT48L2h0bWw+YCksbi5jb250ZW50V2luZG93fWNhdGNoKGUpe312YXIgcj1kb2N1bWVudC5jcmVhdGVFbGVtZW50KFwibWV0YVwiKTtyLnNldEF0dHJpYnV0ZShcImNoYXJzZXRcIix0KTt0cnl7dmFyIGk9ZG9jdW1lbnQuaW1wbGVtZW50YXRpb24uY3JlYXRlSFRNTERvY3VtZW50KG8pLGw9KGkuaGVhZC5hcHBlbmRDaGlsZChyKSxlK2kuZG9jdW1lbnRFbGVtZW50Lm91dGVySFRNTCk7cmV0dXJuIG4uc2V0QXR0cmlidXRlKFwic3JjZG9jXCIsbCksbi5jb250ZW50V2luZG93fWNhdGNoKGUpe31yZXR1cm4gbi5jb250ZW50RG9jdW1lbnQuaGVhZC5hcHBlbmRDaGlsZChyKSxuLmNvbnRlbnREb2N1bWVudC50aXRsZT1vLG4uY29udGVudFdpbmRvdztmdW5jdGlvbiBzKGUpe3ZhciB0O3JldHVybiBlPygodD1kb2N1bWVudC5jcmVhdGVFbGVtZW50KFwiZGl2XCIpKS5pbm5lclRleHQ9ZSx0LmlubmVySFRNTCk6XCJcIn19KSgpKS5kb2N1bWVudCxlKSxuPSgoZSx0KT0+e2xldCBuPXt9LG89ZS5nZXRDb21wdXRlZFN0eWxlKHQpO3JldHVybiBmLmFzQXJyYXkobykuZm9yRWFjaChmdW5jdGlvbihlKXtuW2VdPVwid2lkdGhcIj09PWV8fFwiaGVpZ2h0XCI9PT1lP1wiYXV0b1wiOm8uZ2V0UHJvcGVydHlWYWx1ZShlKX0pLG59KShuLGUpO3ZhciByPWU7ZG97dmFyIGk9ci5wYXJlbnRFbGVtZW50O251bGwhPT1pJiZpLnJlbW92ZUNoaWxkKHIpLHI9aX13aGlsZShyJiZcIkJPRFlcIiE9PXIudGFnTmFtZSk7cmV0dXJuIHdbb109bn19KShpLGwpOnt9LGM9ZS5zdHlsZTtmLmFzQXJyYXkocykuZm9yRWFjaChmdW5jdGlvbihlKXt2YXIgdCxuLG8scjtpLmZpbHRlclN0eWxlcyYmIWkuZmlsdGVyU3R5bGVzKGwsZSl8fChuPXMuZ2V0UHJvcGVydHlWYWx1ZShlKSxvPWFbZV0sdD11P3UuZ2V0UHJvcGVydHlWYWx1ZShlKTp2b2lkIDAsYy5nZXRQcm9wZXJ0eVZhbHVlKGUpKXx8KG4hPT1vfHx1JiZuIT09dCkmJihvPXMuZ2V0UHJvcGVydHlQcmlvcml0eShlKSx0PWMsbj1uLG89byxyPTA8PVtcImJhY2tncm91bmQtY2xpcFwiXS5pbmRleE9mKGU9ZSksbz8odC5zZXRQcm9wZXJ0eShlLG4sbyksciYmdC5zZXRQcm9wZXJ0eShcIi13ZWJraXQtXCIrZSxuLG8pKToodC5zZXRQcm9wZXJ0eShlLG4pLHImJnQuc2V0UHJvcGVydHkoXCItd2Via2l0LVwiK2UsbikpKX0pfWxldCB2PW51bGwsdz17fSxFPVtcIkFERFJFU1NcIixcIkFSVElDTEVcIixcIkFTSURFXCIsXCJCTE9DS1FVT1RFXCIsXCJERVRBSUxTXCIsXCJESUFMT0dcIixcIkREXCIsXCJESVZcIixcIkRMXCIsXCJEVFwiLFwiRklFTERTRVRcIixcIkZJR0NBUFRJT05cIixcIkZJR1VSRVwiLFwiRk9PVEVSXCIsXCJGT1JNXCIsXCJIMVwiLFwiSDJcIixcIkgzXCIsXCJINFwiLFwiSDVcIixcIkg2XCIsXCJIRUFERVJcIixcIkhHUk9VUFwiLFwiSFJcIixcIkxJXCIsXCJNQUlOXCIsXCJOQVZcIixcIk9MXCIsXCJQXCIsXCJQUkVcIixcIlNFQ1RJT05cIixcIlNWR1wiLFwiVEFCTEVcIixcIlVMXCIsXCJtYXRoXCIsXCJzdmdcIixcIkJPRFlcIixcIkhFQURcIixcIkhUTUxcIl19KSh0aGlzKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWRvbS10by1pbWFnZS1tb3JlLm1pbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/dom-to-image-more@3.6.0/node_modules/dom-to-image-more/dist/dom-to-image-more.min.js\n");

/***/ })

};
;