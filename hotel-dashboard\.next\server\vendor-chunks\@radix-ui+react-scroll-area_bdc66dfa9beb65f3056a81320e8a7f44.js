"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-scroll-area_bdc66dfa9beb65f3056a81320e8a7f44";
exports.ids = ["vendor-chunks/@radix-ui+react-scroll-area_bdc66dfa9beb65f3056a81320e8a7f44"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-scroll-area_bdc66dfa9beb65f3056a81320e8a7f44/node_modules/@radix-ui/react-scroll-area/dist/index.mjs":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-scroll-area_bdc66dfa9beb65f3056a81320e8a7f44/node_modules/@radix-ui/react-scroll-area/dist/index.mjs ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Corner: () => (/* binding */ Corner),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   ScrollArea: () => (/* binding */ ScrollArea),\n/* harmony export */   ScrollAreaCorner: () => (/* binding */ ScrollAreaCorner),\n/* harmony export */   ScrollAreaScrollbar: () => (/* binding */ ScrollAreaScrollbar),\n/* harmony export */   ScrollAreaThumb: () => (/* binding */ ScrollAreaThumb),\n/* harmony export */   ScrollAreaViewport: () => (/* binding */ ScrollAreaViewport),\n/* harmony export */   Scrollbar: () => (/* binding */ Scrollbar),\n/* harmony export */   Thumb: () => (/* binding */ Thumb),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createScrollAreaScope: () => (/* binding */ createScrollAreaScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_997b35f2e2aa9d3174fc03a0f79e437b/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1._d340d9d39508cb250c25fc66451df4dd/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_4fe40d510edca7ae4ca9c92afeb1ae6d/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_2a0e526a8f7e7aada080206d385bb572/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callbac_2dc63ba6354ec7ef7d955ba47145829a/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1_033f2d9864c311ebc46e3d65a72ec4b7/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-_7d9c308966eafc0f645092b629b133a3/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_number__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/number */ \"(ssr)/./node_modules/.pnpm/@radix-ui+number@1.1.0/node_modules/@radix-ui/number/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Corner,Root,ScrollArea,ScrollAreaCorner,ScrollAreaScrollbar,ScrollAreaThumb,ScrollAreaViewport,Scrollbar,Thumb,Viewport,createScrollAreaScope auto */ // packages/react/scroll-area/src/ScrollArea.tsx\n\n\n\n\n\n\n\n\n\n\n// packages/react/scroll-area/src/useStateMachine.ts\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer({\n        \"useStateMachine.useReducer\": (state, event)=>{\n            const nextState = machine[state][event];\n            return nextState ?? state;\n        }\n    }[\"useStateMachine.useReducer\"], initialState);\n}\n// packages/react/scroll-area/src/ScrollArea.tsx\n\nvar SCROLL_AREA_NAME = \"ScrollArea\";\nvar [createScrollAreaContext, createScrollAreaScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(SCROLL_AREA_NAME);\nvar [ScrollAreaProvider, useScrollAreaContext] = createScrollAreaContext(SCROLL_AREA_NAME);\nvar ScrollArea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, type = \"hover\", dir, scrollHideDelay = 600, ...scrollAreaProps } = props;\n    const [scrollArea, setScrollArea] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [scrollbarX, setScrollbarX] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [scrollbarY, setScrollbarY] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [cornerWidth, setCornerWidth] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [cornerHeight, setCornerHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [scrollbarXEnabled, setScrollbarXEnabled] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [scrollbarYEnabled, setScrollbarYEnabled] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"ScrollArea.useComposedRefs[composedRefs]\": (node)=>setScrollArea(node)\n    }[\"ScrollArea.useComposedRefs[composedRefs]\"]);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__.useDirection)(dir);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaProvider, {\n        scope: __scopeScrollArea,\n        type,\n        dir: direction,\n        scrollHideDelay,\n        scrollArea,\n        viewport,\n        onViewportChange: setViewport,\n        content,\n        onContentChange: setContent,\n        scrollbarX,\n        onScrollbarXChange: setScrollbarX,\n        scrollbarXEnabled,\n        onScrollbarXEnabledChange: setScrollbarXEnabled,\n        scrollbarY,\n        onScrollbarYChange: setScrollbarY,\n        scrollbarYEnabled,\n        onScrollbarYEnabledChange: setScrollbarYEnabled,\n        onCornerWidthChange: setCornerWidth,\n        onCornerHeightChange: setCornerHeight,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n            dir: direction,\n            ...scrollAreaProps,\n            ref: composedRefs,\n            style: {\n                position: \"relative\",\n                // Pass corner sizes as CSS vars to reduce re-renders of context consumers\n                [\"--radix-scroll-area-corner-width\"]: cornerWidth + \"px\",\n                [\"--radix-scroll-area-corner-height\"]: cornerHeight + \"px\",\n                ...props.style\n            }\n        })\n    });\n});\nScrollArea.displayName = SCROLL_AREA_NAME;\nvar VIEWPORT_NAME = \"ScrollAreaViewport\";\nvar ScrollAreaViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, children, nonce, ...viewportProps } = props;\n    const context = useScrollAreaContext(VIEWPORT_NAME, __scopeScrollArea);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onViewportChange);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"style\", {\n                dangerouslySetInnerHTML: {\n                    __html: `[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}`\n                },\n                nonce\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n                \"data-radix-scroll-area-viewport\": \"\",\n                ...viewportProps,\n                ref: composedRefs,\n                style: {\n                    /**\n             * We don't support `visible` because the intention is to have at least one scrollbar\n             * if this component is used and `visible` will behave like `auto` in that case\n             * https://developer.mozilla.org/en-US/docs/Web/CSS/overflow#description\n             *\n             * We don't handle `auto` because the intention is for the native implementation\n             * to be hidden if using this component. We just want to ensure the node is scrollable\n             * so could have used either `scroll` or `auto` here. We picked `scroll` to prevent\n             * the browser from having to work out whether to render native scrollbars or not,\n             * we tell it to with the intention of hiding them in CSS.\n             */ overflowX: context.scrollbarXEnabled ? \"scroll\" : \"hidden\",\n                    overflowY: context.scrollbarYEnabled ? \"scroll\" : \"hidden\",\n                    ...props.style\n                },\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n                    ref: context.onContentChange,\n                    style: {\n                        minWidth: \"100%\",\n                        display: \"table\"\n                    },\n                    children\n                })\n            })\n        ]\n    });\n});\nScrollAreaViewport.displayName = VIEWPORT_NAME;\nvar SCROLLBAR_NAME = \"ScrollAreaScrollbar\";\nvar ScrollAreaScrollbar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { onScrollbarXEnabledChange, onScrollbarYEnabledChange } = context;\n    const isHorizontal = props.orientation === \"horizontal\";\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbar.useEffect\": ()=>{\n            isHorizontal ? onScrollbarXEnabledChange(true) : onScrollbarYEnabledChange(true);\n            return ({\n                \"ScrollAreaScrollbar.useEffect\": ()=>{\n                    isHorizontal ? onScrollbarXEnabledChange(false) : onScrollbarYEnabledChange(false);\n                }\n            })[\"ScrollAreaScrollbar.useEffect\"];\n        }\n    }[\"ScrollAreaScrollbar.useEffect\"], [\n        isHorizontal,\n        onScrollbarXEnabledChange,\n        onScrollbarYEnabledChange\n    ]);\n    return context.type === \"hover\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarHover, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"scroll\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarScroll, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"auto\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarAuto, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"always\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n        ...scrollbarProps,\n        ref: forwardedRef\n    }) : null;\n});\nScrollAreaScrollbar.displayName = SCROLLBAR_NAME;\nvar ScrollAreaScrollbarHover = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [visible, setVisible] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarHover.useEffect\": ()=>{\n            const scrollArea = context.scrollArea;\n            let hideTimer = 0;\n            if (scrollArea) {\n                const handlePointerEnter = {\n                    \"ScrollAreaScrollbarHover.useEffect.handlePointerEnter\": ()=>{\n                        window.clearTimeout(hideTimer);\n                        setVisible(true);\n                    }\n                }[\"ScrollAreaScrollbarHover.useEffect.handlePointerEnter\"];\n                const handlePointerLeave = {\n                    \"ScrollAreaScrollbarHover.useEffect.handlePointerLeave\": ()=>{\n                        hideTimer = window.setTimeout({\n                            \"ScrollAreaScrollbarHover.useEffect.handlePointerLeave\": ()=>setVisible(false)\n                        }[\"ScrollAreaScrollbarHover.useEffect.handlePointerLeave\"], context.scrollHideDelay);\n                    }\n                }[\"ScrollAreaScrollbarHover.useEffect.handlePointerLeave\"];\n                scrollArea.addEventListener(\"pointerenter\", handlePointerEnter);\n                scrollArea.addEventListener(\"pointerleave\", handlePointerLeave);\n                return ({\n                    \"ScrollAreaScrollbarHover.useEffect\": ()=>{\n                        window.clearTimeout(hideTimer);\n                        scrollArea.removeEventListener(\"pointerenter\", handlePointerEnter);\n                        scrollArea.removeEventListener(\"pointerleave\", handlePointerLeave);\n                    }\n                })[\"ScrollAreaScrollbarHover.useEffect\"];\n            }\n        }\n    }[\"ScrollAreaScrollbarHover.useEffect\"], [\n        context.scrollArea,\n        context.scrollHideDelay\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || visible,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarAuto, {\n            \"data-state\": visible ? \"visible\" : \"hidden\",\n            ...scrollbarProps,\n            ref: forwardedRef\n        })\n    });\n});\nvar ScrollAreaScrollbarScroll = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const isHorizontal = props.orientation === \"horizontal\";\n    const debounceScrollEnd = useDebounceCallback({\n        \"ScrollAreaScrollbarScroll.useDebounceCallback[debounceScrollEnd]\": ()=>send(\"SCROLL_END\")\n    }[\"ScrollAreaScrollbarScroll.useDebounceCallback[debounceScrollEnd]\"], 100);\n    const [state, send] = useStateMachine(\"hidden\", {\n        hidden: {\n            SCROLL: \"scrolling\"\n        },\n        scrolling: {\n            SCROLL_END: \"idle\",\n            POINTER_ENTER: \"interacting\"\n        },\n        interacting: {\n            SCROLL: \"interacting\",\n            POINTER_LEAVE: \"idle\"\n        },\n        idle: {\n            HIDE: \"hidden\",\n            SCROLL: \"scrolling\",\n            POINTER_ENTER: \"interacting\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarScroll.useEffect\": ()=>{\n            if (state === \"idle\") {\n                const hideTimer = window.setTimeout({\n                    \"ScrollAreaScrollbarScroll.useEffect.hideTimer\": ()=>send(\"HIDE\")\n                }[\"ScrollAreaScrollbarScroll.useEffect.hideTimer\"], context.scrollHideDelay);\n                return ({\n                    \"ScrollAreaScrollbarScroll.useEffect\": ()=>window.clearTimeout(hideTimer)\n                })[\"ScrollAreaScrollbarScroll.useEffect\"];\n            }\n        }\n    }[\"ScrollAreaScrollbarScroll.useEffect\"], [\n        state,\n        context.scrollHideDelay,\n        send\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarScroll.useEffect\": ()=>{\n            const viewport = context.viewport;\n            const scrollDirection = isHorizontal ? \"scrollLeft\" : \"scrollTop\";\n            if (viewport) {\n                let prevScrollPos = viewport[scrollDirection];\n                const handleScroll = {\n                    \"ScrollAreaScrollbarScroll.useEffect.handleScroll\": ()=>{\n                        const scrollPos = viewport[scrollDirection];\n                        const hasScrollInDirectionChanged = prevScrollPos !== scrollPos;\n                        if (hasScrollInDirectionChanged) {\n                            send(\"SCROLL\");\n                            debounceScrollEnd();\n                        }\n                        prevScrollPos = scrollPos;\n                    }\n                }[\"ScrollAreaScrollbarScroll.useEffect.handleScroll\"];\n                viewport.addEventListener(\"scroll\", handleScroll);\n                return ({\n                    \"ScrollAreaScrollbarScroll.useEffect\": ()=>viewport.removeEventListener(\"scroll\", handleScroll)\n                })[\"ScrollAreaScrollbarScroll.useEffect\"];\n            }\n        }\n    }[\"ScrollAreaScrollbarScroll.useEffect\"], [\n        context.viewport,\n        isHorizontal,\n        send,\n        debounceScrollEnd\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || state !== \"hidden\",\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n            \"data-state\": state === \"hidden\" ? \"hidden\" : \"visible\",\n            ...scrollbarProps,\n            ref: forwardedRef,\n            onPointerEnter: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerEnter, ()=>send(\"POINTER_ENTER\")),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerLeave, ()=>send(\"POINTER_LEAVE\"))\n        })\n    });\n});\nvar ScrollAreaScrollbarAuto = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { forceMount, ...scrollbarProps } = props;\n    const [visible, setVisible] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const isHorizontal = props.orientation === \"horizontal\";\n    const handleResize = useDebounceCallback({\n        \"ScrollAreaScrollbarAuto.useDebounceCallback[handleResize]\": ()=>{\n            if (context.viewport) {\n                const isOverflowX = context.viewport.offsetWidth < context.viewport.scrollWidth;\n                const isOverflowY = context.viewport.offsetHeight < context.viewport.scrollHeight;\n                setVisible(isHorizontal ? isOverflowX : isOverflowY);\n            }\n        }\n    }[\"ScrollAreaScrollbarAuto.useDebounceCallback[handleResize]\"], 10);\n    useResizeObserver(context.viewport, handleResize);\n    useResizeObserver(context.content, handleResize);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || visible,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n            \"data-state\": visible ? \"visible\" : \"hidden\",\n            ...scrollbarProps,\n            ref: forwardedRef\n        })\n    });\n});\nvar ScrollAreaScrollbarVisible = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { orientation = \"vertical\", ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const thumbRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const pointerOffsetRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const [sizes, setSizes] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        content: 0,\n        viewport: 0,\n        scrollbar: {\n            size: 0,\n            paddingStart: 0,\n            paddingEnd: 0\n        }\n    });\n    const thumbRatio = getThumbRatio(sizes.viewport, sizes.content);\n    const commonProps = {\n        ...scrollbarProps,\n        sizes,\n        onSizesChange: setSizes,\n        hasThumb: Boolean(thumbRatio > 0 && thumbRatio < 1),\n        onThumbChange: (thumb)=>thumbRef.current = thumb,\n        onThumbPointerUp: ()=>pointerOffsetRef.current = 0,\n        onThumbPointerDown: (pointerPos)=>pointerOffsetRef.current = pointerPos\n    };\n    function getScrollPosition(pointerPos, dir) {\n        return getScrollPositionFromPointer(pointerPos, pointerOffsetRef.current, sizes, dir);\n    }\n    if (orientation === \"horizontal\") {\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarX, {\n            ...commonProps,\n            ref: forwardedRef,\n            onThumbPositionChange: ()=>{\n                if (context.viewport && thumbRef.current) {\n                    const scrollPos = context.viewport.scrollLeft;\n                    const offset = getThumbOffsetFromScroll(scrollPos, sizes, context.dir);\n                    thumbRef.current.style.transform = `translate3d(${offset}px, 0, 0)`;\n                }\n            },\n            onWheelScroll: (scrollPos)=>{\n                if (context.viewport) context.viewport.scrollLeft = scrollPos;\n            },\n            onDragScroll: (pointerPos)=>{\n                if (context.viewport) {\n                    context.viewport.scrollLeft = getScrollPosition(pointerPos, context.dir);\n                }\n            }\n        });\n    }\n    if (orientation === \"vertical\") {\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarY, {\n            ...commonProps,\n            ref: forwardedRef,\n            onThumbPositionChange: ()=>{\n                if (context.viewport && thumbRef.current) {\n                    const scrollPos = context.viewport.scrollTop;\n                    const offset = getThumbOffsetFromScroll(scrollPos, sizes);\n                    thumbRef.current.style.transform = `translate3d(0, ${offset}px, 0)`;\n                }\n            },\n            onWheelScroll: (scrollPos)=>{\n                if (context.viewport) context.viewport.scrollTop = scrollPos;\n            },\n            onDragScroll: (pointerPos)=>{\n                if (context.viewport) context.viewport.scrollTop = getScrollPosition(pointerPos);\n            }\n        });\n    }\n    return null;\n});\nvar ScrollAreaScrollbarX = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { sizes, onSizesChange, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [computedStyle, setComputedStyle] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onScrollbarXChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarX.useEffect\": ()=>{\n            if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n        }\n    }[\"ScrollAreaScrollbarX.useEffect\"], [\n        ref\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarImpl, {\n        \"data-orientation\": \"horizontal\",\n        ...scrollbarProps,\n        ref: composeRefs,\n        sizes,\n        style: {\n            bottom: 0,\n            left: context.dir === \"rtl\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n            right: context.dir === \"ltr\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n            [\"--radix-scroll-area-thumb-width\"]: getThumbSize(sizes) + \"px\",\n            ...props.style\n        },\n        onThumbPointerDown: (pointerPos)=>props.onThumbPointerDown(pointerPos.x),\n        onDragScroll: (pointerPos)=>props.onDragScroll(pointerPos.x),\n        onWheelScroll: (event, maxScrollPos)=>{\n            if (context.viewport) {\n                const scrollPos = context.viewport.scrollLeft + event.deltaX;\n                props.onWheelScroll(scrollPos);\n                if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n                    event.preventDefault();\n                }\n            }\n        },\n        onResize: ()=>{\n            if (ref.current && context.viewport && computedStyle) {\n                onSizesChange({\n                    content: context.viewport.scrollWidth,\n                    viewport: context.viewport.offsetWidth,\n                    scrollbar: {\n                        size: ref.current.clientWidth,\n                        paddingStart: toInt(computedStyle.paddingLeft),\n                        paddingEnd: toInt(computedStyle.paddingRight)\n                    }\n                });\n            }\n        }\n    });\n});\nvar ScrollAreaScrollbarY = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { sizes, onSizesChange, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [computedStyle, setComputedStyle] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onScrollbarYChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarY.useEffect\": ()=>{\n            if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n        }\n    }[\"ScrollAreaScrollbarY.useEffect\"], [\n        ref\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarImpl, {\n        \"data-orientation\": \"vertical\",\n        ...scrollbarProps,\n        ref: composeRefs,\n        sizes,\n        style: {\n            top: 0,\n            right: context.dir === \"ltr\" ? 0 : void 0,\n            left: context.dir === \"rtl\" ? 0 : void 0,\n            bottom: \"var(--radix-scroll-area-corner-height)\",\n            [\"--radix-scroll-area-thumb-height\"]: getThumbSize(sizes) + \"px\",\n            ...props.style\n        },\n        onThumbPointerDown: (pointerPos)=>props.onThumbPointerDown(pointerPos.y),\n        onDragScroll: (pointerPos)=>props.onDragScroll(pointerPos.y),\n        onWheelScroll: (event, maxScrollPos)=>{\n            if (context.viewport) {\n                const scrollPos = context.viewport.scrollTop + event.deltaY;\n                props.onWheelScroll(scrollPos);\n                if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n                    event.preventDefault();\n                }\n            }\n        },\n        onResize: ()=>{\n            if (ref.current && context.viewport && computedStyle) {\n                onSizesChange({\n                    content: context.viewport.scrollHeight,\n                    viewport: context.viewport.offsetHeight,\n                    scrollbar: {\n                        size: ref.current.clientHeight,\n                        paddingStart: toInt(computedStyle.paddingTop),\n                        paddingEnd: toInt(computedStyle.paddingBottom)\n                    }\n                });\n            }\n        }\n    });\n});\nvar [ScrollbarProvider, useScrollbarContext] = createScrollAreaContext(SCROLLBAR_NAME);\nvar ScrollAreaScrollbarImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, sizes, hasThumb, onThumbChange, onThumbPointerUp, onThumbPointerDown, onThumbPositionChange, onDragScroll, onWheelScroll, onResize, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, __scopeScrollArea);\n    const [scrollbar, setScrollbar] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"ScrollAreaScrollbarImpl.useComposedRefs[composeRefs]\": (node)=>setScrollbar(node)\n    }[\"ScrollAreaScrollbarImpl.useComposedRefs[composeRefs]\"]);\n    const rectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevWebkitUserSelectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"\");\n    const viewport = context.viewport;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const handleWheelScroll = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onWheelScroll);\n    const handleThumbPositionChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPositionChange);\n    const handleResize = useDebounceCallback(onResize, 10);\n    function handleDragScroll(event) {\n        if (rectRef.current) {\n            const x = event.clientX - rectRef.current.left;\n            const y = event.clientY - rectRef.current.top;\n            onDragScroll({\n                x,\n                y\n            });\n        }\n    }\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarImpl.useEffect\": ()=>{\n            const handleWheel = {\n                \"ScrollAreaScrollbarImpl.useEffect.handleWheel\": (event)=>{\n                    const element = event.target;\n                    const isScrollbarWheel = scrollbar?.contains(element);\n                    if (isScrollbarWheel) handleWheelScroll(event, maxScrollPos);\n                }\n            }[\"ScrollAreaScrollbarImpl.useEffect.handleWheel\"];\n            document.addEventListener(\"wheel\", handleWheel, {\n                passive: false\n            });\n            return ({\n                \"ScrollAreaScrollbarImpl.useEffect\": ()=>document.removeEventListener(\"wheel\", handleWheel, {\n                        passive: false\n                    })\n            })[\"ScrollAreaScrollbarImpl.useEffect\"];\n        }\n    }[\"ScrollAreaScrollbarImpl.useEffect\"], [\n        viewport,\n        scrollbar,\n        maxScrollPos,\n        handleWheelScroll\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(handleThumbPositionChange, [\n        sizes,\n        handleThumbPositionChange\n    ]);\n    useResizeObserver(scrollbar, handleResize);\n    useResizeObserver(context.content, handleResize);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollbarProvider, {\n        scope: __scopeScrollArea,\n        scrollbar,\n        hasThumb,\n        onThumbChange: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbChange),\n        onThumbPointerUp: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPointerUp),\n        onThumbPositionChange: handleThumbPositionChange,\n        onThumbPointerDown: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPointerDown),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n            ...scrollbarProps,\n            ref: composeRefs,\n            style: {\n                position: \"absolute\",\n                ...scrollbarProps.style\n            },\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDown, (event)=>{\n                const mainPointer = 0;\n                if (event.button === mainPointer) {\n                    const element = event.target;\n                    element.setPointerCapture(event.pointerId);\n                    rectRef.current = scrollbar.getBoundingClientRect();\n                    prevWebkitUserSelectRef.current = document.body.style.webkitUserSelect;\n                    document.body.style.webkitUserSelect = \"none\";\n                    if (context.viewport) context.viewport.style.scrollBehavior = \"auto\";\n                    handleDragScroll(event);\n                }\n            }),\n            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerMove, handleDragScroll),\n            onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerUp, (event)=>{\n                const element = event.target;\n                if (element.hasPointerCapture(event.pointerId)) {\n                    element.releasePointerCapture(event.pointerId);\n                }\n                document.body.style.webkitUserSelect = prevWebkitUserSelectRef.current;\n                if (context.viewport) context.viewport.style.scrollBehavior = \"\";\n                rectRef.current = null;\n            })\n        })\n    });\n});\nvar THUMB_NAME = \"ScrollAreaThumb\";\nvar ScrollAreaThumb = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...thumbProps } = props;\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, props.__scopeScrollArea);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || scrollbarContext.hasThumb,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaThumbImpl, {\n            ref: forwardedRef,\n            ...thumbProps\n        })\n    });\n});\nvar ScrollAreaThumbImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, style, ...thumbProps } = props;\n    const scrollAreaContext = useScrollAreaContext(THUMB_NAME, __scopeScrollArea);\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, __scopeScrollArea);\n    const { onThumbPositionChange } = scrollbarContext;\n    const composedRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"ScrollAreaThumbImpl.useComposedRefs[composedRef]\": (node)=>scrollbarContext.onThumbChange(node)\n    }[\"ScrollAreaThumbImpl.useComposedRefs[composedRef]\"]);\n    const removeUnlinkedScrollListenerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n    const debounceScrollEnd = useDebounceCallback({\n        \"ScrollAreaThumbImpl.useDebounceCallback[debounceScrollEnd]\": ()=>{\n            if (removeUnlinkedScrollListenerRef.current) {\n                removeUnlinkedScrollListenerRef.current();\n                removeUnlinkedScrollListenerRef.current = void 0;\n            }\n        }\n    }[\"ScrollAreaThumbImpl.useDebounceCallback[debounceScrollEnd]\"], 100);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaThumbImpl.useEffect\": ()=>{\n            const viewport = scrollAreaContext.viewport;\n            if (viewport) {\n                const handleScroll = {\n                    \"ScrollAreaThumbImpl.useEffect.handleScroll\": ()=>{\n                        debounceScrollEnd();\n                        if (!removeUnlinkedScrollListenerRef.current) {\n                            const listener = addUnlinkedScrollListener(viewport, onThumbPositionChange);\n                            removeUnlinkedScrollListenerRef.current = listener;\n                            onThumbPositionChange();\n                        }\n                    }\n                }[\"ScrollAreaThumbImpl.useEffect.handleScroll\"];\n                onThumbPositionChange();\n                viewport.addEventListener(\"scroll\", handleScroll);\n                return ({\n                    \"ScrollAreaThumbImpl.useEffect\": ()=>viewport.removeEventListener(\"scroll\", handleScroll)\n                })[\"ScrollAreaThumbImpl.useEffect\"];\n            }\n        }\n    }[\"ScrollAreaThumbImpl.useEffect\"], [\n        scrollAreaContext.viewport,\n        debounceScrollEnd,\n        onThumbPositionChange\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n        \"data-state\": scrollbarContext.hasThumb ? \"visible\" : \"hidden\",\n        ...thumbProps,\n        ref: composedRef,\n        style: {\n            width: \"var(--radix-scroll-area-thumb-width)\",\n            height: \"var(--radix-scroll-area-thumb-height)\",\n            ...style\n        },\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDownCapture, (event)=>{\n            const thumb = event.target;\n            const thumbRect = thumb.getBoundingClientRect();\n            const x = event.clientX - thumbRect.left;\n            const y = event.clientY - thumbRect.top;\n            scrollbarContext.onThumbPointerDown({\n                x,\n                y\n            });\n        }),\n        onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerUp, scrollbarContext.onThumbPointerUp)\n    });\n});\nScrollAreaThumb.displayName = THUMB_NAME;\nvar CORNER_NAME = \"ScrollAreaCorner\";\nvar ScrollAreaCorner = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useScrollAreaContext(CORNER_NAME, props.__scopeScrollArea);\n    const hasBothScrollbarsVisible = Boolean(context.scrollbarX && context.scrollbarY);\n    const hasCorner = context.type !== \"scroll\" && hasBothScrollbarsVisible;\n    return hasCorner ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaCornerImpl, {\n        ...props,\n        ref: forwardedRef\n    }) : null;\n});\nScrollAreaCorner.displayName = CORNER_NAME;\nvar ScrollAreaCornerImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, ...cornerProps } = props;\n    const context = useScrollAreaContext(CORNER_NAME, __scopeScrollArea);\n    const [width, setWidth] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [height, setHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const hasSize = Boolean(width && height);\n    useResizeObserver(context.scrollbarX, {\n        \"ScrollAreaCornerImpl.useResizeObserver\": ()=>{\n            const height2 = context.scrollbarX?.offsetHeight || 0;\n            context.onCornerHeightChange(height2);\n            setHeight(height2);\n        }\n    }[\"ScrollAreaCornerImpl.useResizeObserver\"]);\n    useResizeObserver(context.scrollbarY, {\n        \"ScrollAreaCornerImpl.useResizeObserver\": ()=>{\n            const width2 = context.scrollbarY?.offsetWidth || 0;\n            context.onCornerWidthChange(width2);\n            setWidth(width2);\n        }\n    }[\"ScrollAreaCornerImpl.useResizeObserver\"]);\n    return hasSize ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n        ...cornerProps,\n        ref: forwardedRef,\n        style: {\n            width,\n            height,\n            position: \"absolute\",\n            right: context.dir === \"ltr\" ? 0 : void 0,\n            left: context.dir === \"rtl\" ? 0 : void 0,\n            bottom: 0,\n            ...props.style\n        }\n    }) : null;\n});\nfunction toInt(value) {\n    return value ? parseInt(value, 10) : 0;\n}\nfunction getThumbRatio(viewportSize, contentSize) {\n    const ratio = viewportSize / contentSize;\n    return isNaN(ratio) ? 0 : ratio;\n}\nfunction getThumbSize(sizes) {\n    const ratio = getThumbRatio(sizes.viewport, sizes.content);\n    const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n    const thumbSize = (sizes.scrollbar.size - scrollbarPadding) * ratio;\n    return Math.max(thumbSize, 18);\n}\nfunction getScrollPositionFromPointer(pointerPos, pointerOffset, sizes, dir = \"ltr\") {\n    const thumbSizePx = getThumbSize(sizes);\n    const thumbCenter = thumbSizePx / 2;\n    const offset = pointerOffset || thumbCenter;\n    const thumbOffsetFromEnd = thumbSizePx - offset;\n    const minPointerPos = sizes.scrollbar.paddingStart + offset;\n    const maxPointerPos = sizes.scrollbar.size - sizes.scrollbar.paddingEnd - thumbOffsetFromEnd;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const scrollRange = dir === \"ltr\" ? [\n        0,\n        maxScrollPos\n    ] : [\n        maxScrollPos * -1,\n        0\n    ];\n    const interpolate = linearScale([\n        minPointerPos,\n        maxPointerPos\n    ], scrollRange);\n    return interpolate(pointerPos);\n}\nfunction getThumbOffsetFromScroll(scrollPos, sizes, dir = \"ltr\") {\n    const thumbSizePx = getThumbSize(sizes);\n    const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n    const scrollbar = sizes.scrollbar.size - scrollbarPadding;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const maxThumbPos = scrollbar - thumbSizePx;\n    const scrollClampRange = dir === \"ltr\" ? [\n        0,\n        maxScrollPos\n    ] : [\n        maxScrollPos * -1,\n        0\n    ];\n    const scrollWithoutMomentum = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_9__.clamp)(scrollPos, scrollClampRange);\n    const interpolate = linearScale([\n        0,\n        maxScrollPos\n    ], [\n        0,\n        maxThumbPos\n    ]);\n    return interpolate(scrollWithoutMomentum);\n}\nfunction linearScale(input, output) {\n    return (value)=>{\n        if (input[0] === input[1] || output[0] === output[1]) return output[0];\n        const ratio = (output[1] - output[0]) / (input[1] - input[0]);\n        return output[0] + ratio * (value - input[0]);\n    };\n}\nfunction isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos) {\n    return scrollPos > 0 && scrollPos < maxScrollPos;\n}\nvar addUnlinkedScrollListener = (node, handler = ()=>{})=>{\n    let prevPosition = {\n        left: node.scrollLeft,\n        top: node.scrollTop\n    };\n    let rAF = 0;\n    (function loop() {\n        const position = {\n            left: node.scrollLeft,\n            top: node.scrollTop\n        };\n        const isHorizontalScroll = prevPosition.left !== position.left;\n        const isVerticalScroll = prevPosition.top !== position.top;\n        if (isHorizontalScroll || isVerticalScroll) handler();\n        prevPosition = position;\n        rAF = window.requestAnimationFrame(loop);\n    })();\n    return ()=>window.cancelAnimationFrame(rAF);\n};\nfunction useDebounceCallback(callback, delay) {\n    const handleCallback = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(callback);\n    const debounceTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useDebounceCallback.useEffect\": ()=>({\n                \"useDebounceCallback.useEffect\": ()=>window.clearTimeout(debounceTimerRef.current)\n            })[\"useDebounceCallback.useEffect\"]\n    }[\"useDebounceCallback.useEffect\"], []);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"useDebounceCallback.useCallback\": ()=>{\n            window.clearTimeout(debounceTimerRef.current);\n            debounceTimerRef.current = window.setTimeout(handleCallback, delay);\n        }\n    }[\"useDebounceCallback.useCallback\"], [\n        handleCallback,\n        delay\n    ]);\n}\nfunction useResizeObserver(element, onResize) {\n    const handleResize = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onResize);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_10__.useLayoutEffect)({\n        \"useResizeObserver.useLayoutEffect\": ()=>{\n            let rAF = 0;\n            if (element) {\n                const resizeObserver = new ResizeObserver({\n                    \"useResizeObserver.useLayoutEffect\": ()=>{\n                        cancelAnimationFrame(rAF);\n                        rAF = window.requestAnimationFrame(handleResize);\n                    }\n                }[\"useResizeObserver.useLayoutEffect\"]);\n                resizeObserver.observe(element);\n                return ({\n                    \"useResizeObserver.useLayoutEffect\": ()=>{\n                        window.cancelAnimationFrame(rAF);\n                        resizeObserver.unobserve(element);\n                    }\n                })[\"useResizeObserver.useLayoutEffect\"];\n            }\n        }\n    }[\"useResizeObserver.useLayoutEffect\"], [\n        element,\n        handleResize\n    ]);\n}\nvar Root = ScrollArea;\nvar Viewport = ScrollAreaViewport;\nvar Scrollbar = ScrollAreaScrollbar;\nvar Thumb = ScrollAreaThumb;\nvar Corner = ScrollAreaCorner;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-scroll-area_bdc66dfa9beb65f3056a81320e8a7f44/node_modules/@radix-ui/react-scroll-area/dist/index.mjs\n");

/***/ })

};
;