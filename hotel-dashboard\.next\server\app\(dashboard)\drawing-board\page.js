(()=>{var e={};e.id=654,e.ids=[654],e.modules={2641:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,85770,23)),Promise.resolve().then(t.t.bind(t,88204,23)),Promise.resolve().then(t.t.bind(t,82576,23)),Promise.resolve().then(t.t.bind(t,59507,23)),Promise.resolve().then(t.t.bind(t,61283,23)),Promise.resolve().then(t.t.bind(t,75147,23)),Promise.resolve().then(t.t.bind(t,83163,23)),Promise.resolve().then(t.t.bind(t,99773,23))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14784:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>c,ZB:()=>d,Zp:()=>i,aR:()=>a,wL:()=>u});var o=t(45781),n=t(13072),s=t(77401);let i=n.forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));i.displayName="Card";let a=n.forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",e),...r}));a.displayName="CardHeader";let d=n.forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));d.displayName="CardTitle";let l=n.forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:(0,s.cn)("text-sm text-muted-foreground",e),...r}));l.displayName="CardDescription";let c=n.forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:(0,s.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent";let u=n.forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:(0,s.cn)("flex items-center p-6 pt-0",e),...r}));u.displayName="CardFooter"},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25290:()=>{},25794:()=>{},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32667:(e,r,t)=>{"use strict";t.d(r,{default:()=>o});let o=(0,t(51129).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\drawing-board\\\\DrawingBoard.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\pycode\\support_chart2\\hotel-dashboard\\components\\drawing-board\\DrawingBoard.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},43030:(e,r,t)=>{"use strict";t.d(r,{J:()=>l});var o=t(45781),n=t(13072),s=t(11687),i=t(87990),a=t(77401);let d=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=n.forwardRef(({className:e,...r},t)=>(0,o.jsx)(s.b,{ref:t,className:(0,a.cn)(d(),e),...r}));l.displayName=s.b.displayName},44238:(e,r,t)=>{"use strict";t.d(r,{$:()=>l});var o=t(45781),n=t(13072),s=t(83759),i=t(87990),a=t(77401);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=n.forwardRef(({className:e,variant:r,size:t,asChild:n=!1,...i},l)=>{let c=n?s.DX:"button";return(0,o.jsx)(c,{className:(0,a.cn)(d({variant:r,size:t,className:e})),ref:l,...i})});l.displayName="Button"},55737:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});var o=t(95479),n=t(32667);let s=()=>(0,o.jsxs)("div",{className:"flex flex-col h-full p-4",children:[(0,o.jsx)("h1",{className:"text-2xl font-bold mb-4 flex-shrink-0",children:"Drawing Board"}),(0,o.jsx)("div",{className:"flex-grow w-full min-h-0",children:(0,o.jsx)(n.default,{})})]})},57431:(e,r,t)=>{Promise.resolve().then(t.bind(t,72357))},59650:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s,metadata:()=>n});var o=t(95479);t(25290);let n={title:"v0 App",description:"Created with v0",generator:"v0.dev"};function s({children:e}){return(0,o.jsx)("html",{lang:"en",children:(0,o.jsx)("body",{children:e})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77401:(e,r,t)=>{"use strict";t.d(r,{cn:()=>s});var o=t(42366),n=t(73927);function s(...e){return(0,n.QP)((0,o.$)(e))}},78366:(e,r,t)=>{"use strict";t.d(r,{d:()=>a});var o=t(45781),n=t(13072),s=t(25130),i=t(77401);let a=n.forwardRef(({className:e,...r},t)=>(0,o.jsx)(s.bL,{className:(0,i.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...r,ref:t,children:(0,o.jsx)(s.zi,{className:(0,i.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));a.displayName=s.bL.displayName},82051:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var o=t(7025),n=t(18198),s=t(82576),i=t.n(s),a=t(45239),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let l={children:["",{children:["(dashboard)",{children:["drawing-board",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,55737)),"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\(dashboard)\\drawing-board\\page.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,4540,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,53117,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,6874,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,59650)),"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,4540,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,53117,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,6874,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["D:\\pycode\\support_chart2\\hotel-dashboard\\app\\(dashboard)\\drawing-board\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new o.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(dashboard)/drawing-board/page",pathname:"/drawing-board",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},84597:(e,r,t)=>{"use strict";t.d(r,{N:()=>d});var o=t(13072),n=t(5016),s=t(25182),i=t(83759),a=t(45781);function d(e){let r=e+"CollectionProvider",[t,d]=(0,n.A)(r),[l,c]=t(r,{collectionRef:{current:null},itemMap:new Map}),u=e=>{let{scope:r,children:t}=e,n=o.useRef(null),s=o.useRef(new Map).current;return(0,a.jsx)(l,{scope:r,itemMap:s,collectionRef:n,children:t})};u.displayName=r;let p=e+"CollectionSlot",f=o.forwardRef((e,r)=>{let{scope:t,children:o}=e,n=c(p,t),d=(0,s.s)(r,n.collectionRef);return(0,a.jsx)(i.DX,{ref:d,children:o})});f.displayName=p;let m=e+"CollectionItemSlot",b="data-radix-collection-item",h=o.forwardRef((e,r)=>{let{scope:t,children:n,...d}=e,l=o.useRef(null),u=(0,s.s)(r,l),p=c(m,t);return o.useEffect(()=>(p.itemMap.set(l,{ref:l,...d}),()=>void p.itemMap.delete(l))),(0,a.jsx)(i.DX,{[b]:"",ref:u,children:n})});return h.displayName=m,[{Provider:u,Slot:f,ItemSlot:h},function(r){let t=c(e+"CollectionConsumer",r);return o.useCallback(()=>{let e=t.collectionRef.current;if(!e)return[];let r=Array.from(e.querySelectorAll(`[${b}]`));return Array.from(t.itemMap.values()).sort((e,t)=>r.indexOf(e.ref.current)-r.indexOf(t.ref.current))},[t.collectionRef,t.itemMap])},d]}},91514:()=>{},92913:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,60140,23)),Promise.resolve().then(t.t.bind(t,18946,23)),Promise.resolve().then(t.t.bind(t,74178,23)),Promise.resolve().then(t.t.bind(t,6229,23)),Promise.resolve().then(t.t.bind(t,31281,23)),Promise.resolve().then(t.t.bind(t,93833,23)),Promise.resolve().then(t.t.bind(t,97857,23)),Promise.resolve().then(t.t.bind(t,4947,23))},97183:(e,r,t)=>{Promise.resolve().then(t.bind(t,32667))}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[557,273,403,823,508,357],()=>t(82051));module.exports=o})();