"use client";

import React, { useEffect, useRef } from 'react';
import { Shape, Alignment } from '@/types/drawing-board';

interface CanvasProps {
  width: number;
  height: number;
  grid: { rows: number; cols: number };
  shapes: Shape[];
  addShape: (cell: { row: number; col: number }, alignment: Alignment, coordinates?: { x: number; y: number }) => void;
  deleteShape: (shapeId: number) => void;
  backgroundColor: string;
}

const Canvas: React.FC<CanvasProps> = ({ width, height, grid, shapes, addShape, deleteShape, backgroundColor }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const dpr = window.devicePixelRatio || 1;

    // Set the actual size of the canvas in memory to match the device's pixel ratio.
    canvas.width = width * dpr;
    canvas.height = height * dpr;

    // Set the display size of the canvas.
    canvas.style.width = `${width}px`;
    canvas.style.height = `${height}px`;

    // Scale the context to ensure all drawing operations are scaled up.
    ctx.scale(dpr, dpr);

    // Clear canvas with background color.
    ctx.fillStyle = backgroundColor;
    ctx.fillRect(0, 0, width, height);

    // Draw grid
    drawGrid(ctx);

    // Define drawing functions inside useEffect to ensure they are not part of the component's
    // top-level scope, which can cause issues with SSR in Next.js.
    const getShapeCenter = (shape: Shape): { cx: number, cy: number } => {
      const cellWidth = width / grid.cols;
      const cellHeight = height / grid.rows;
      const radius = shape.diameter / 2;
      const cellTopLeftX = shape.cell.col * cellWidth;
      const cellTopLeftY = shape.cell.row * cellHeight;
      let cx, cy;
      switch (shape.alignment) {
          case 'center':
              cx = cellTopLeftX + cellWidth / 2;
              cy = cellTopLeftY + cellHeight / 2;
              break;
          case 'topLeft':
              cx = cellTopLeftX + radius;
              cy = cellTopLeftY + radius;
              break;
          case 'coordinates':
              if (shape.coordinates) {
                  cx = cellTopLeftX + shape.coordinates.x;
                  cy = cellTopLeftY + shape.coordinates.y;
              } else {
                  cx = cellTopLeftX + cellWidth / 2;
                  cy = cellTopLeftY + cellHeight / 2;
              }
              break;
      }
      return { cx, cy };
    }

    const drawMtfPattern = (ictx: CanvasRenderingContext2D, x: number, y: number, size: number, color: string, idpr: number) => {
      const tempCanvas = document.createElement('canvas');
      tempCanvas.width = size * idpr;
      tempCanvas.height = size * idpr;
      const tempCtx = tempCanvas.getContext('2d');
      if (!tempCtx) return;
      tempCtx.scale(idpr, idpr);
      tempCtx.fillStyle = '#FFFFFF';
      tempCtx.fillRect(0, 0, size, size);
      tempCtx.fillStyle = color;
      tempCtx.imageSmoothingEnabled = false;
      const center_x = size / 2;
      const center_y = size / 2;
      const lineWidth = 2;
      const gap = 2;
      const step = lineWidth + gap;
      tempCtx.fillRect(Math.round(0), Math.round(center_y - lineWidth / 2), Math.round(size), Math.round(lineWidth));
      tempCtx.fillRect(Math.round(center_x - lineWidth / 2), Math.round(0), Math.round(lineWidth), Math.round(size));
      for (let i = center_x - lineWidth / 2 - gap; i > 0; i -= step) {
          tempCtx.fillRect(Math.round(i - lineWidth), Math.round(0), Math.round(lineWidth), Math.round(size / 2 - lineWidth/2));
      }
      for (let i = center_y - lineWidth / 2 - gap; i > 0; i -= step) {
          tempCtx.fillRect(Math.round(center_x + lineWidth/2), Math.round(i - lineWidth), Math.round(size / 2 - lineWidth/2), Math.round(lineWidth));
      }
      for (let i = center_y + lineWidth / 2 + gap; i < size; i += step) {
          tempCtx.fillRect(Math.round(0), Math.round(i), Math.round(size / 2 - lineWidth/2), Math.round(lineWidth));
      }
      for (let i = center_x + lineWidth / 2 + gap; i < size; i += step) {
          tempCtx.fillRect(Math.round(i), Math.round(center_y + lineWidth/2), Math.round(lineWidth), Math.round(size / 2 - lineWidth/2));
      }
      ictx.imageSmoothingEnabled = false;
      ictx.drawImage(tempCanvas, x, y, size, size);
    };

    const drawShape = (ictx: CanvasRenderingContext2D, shape: Shape, idpr: number) => {
      const { cx, cy } = getShapeCenter(shape);
      const radius = shape.diameter / 2;
      if (shape.type === 'circle') {
        ictx.save();
        ictx.imageSmoothingEnabled = false;
        ictx.fillStyle = shape.color;
        ictx.beginPath();
        ictx.arc(cx, cy, radius, 0, 2 * Math.PI);
        ictx.fill();
        ictx.restore();
      } else if (shape.type === 'square') {
        drawMtfPattern(ictx, cx - radius, cy - radius, shape.diameter, shape.color, idpr);
      }
    };

    // Draw shapes
    shapes.forEach(shape => drawShape(ctx, shape, dpr));

  }, [width, height, grid, shapes, backgroundColor]);

  const drawGrid = (ctx: CanvasRenderingContext2D) => {
    ctx.strokeStyle = 'rgba(0, 0, 0, 0.5)';
    ctx.setLineDash([5, 5]);
    ctx.lineWidth = 1;

    const cellWidth = width / grid.cols;
    const cellHeight = height / grid.rows;

    for (let i = 1; i < grid.cols; i++) {
      ctx.beginPath();
      ctx.moveTo(i * cellWidth, 0);
      ctx.lineTo(i * cellWidth, height);
      ctx.stroke();
    }

    for (let i = 1; i < grid.rows; i++) {
      ctx.beginPath();
      ctx.moveTo(0, i * cellHeight);
      ctx.lineTo(width, i * cellHeight);
      ctx.stroke();
    }
    ctx.setLineDash([]);
  };

  const findShapeAt = (x: number, y: number): Shape | undefined => {
    // This function is called from an event handler, so it's safe to be outside useEffect.
    // However, it needs to calculate shape centers, so we duplicate that logic or pass it.
    // For simplicity, we can just call getShapeCenter which is now defined in the component scope.
    // But getShapeCenter is NOT in the component scope anymore.
    // So we must redefine it or move findShapeAt into useEffect as well, which is not ideal.
    // Let's redefine the calculation logic here.
    const cellWidth = width / grid.cols;
    const cellHeight = height / grid.rows;

    // Iterate in reverse to find the top-most shape
    for (let i = shapes.length - 1; i >= 0; i--) {
        const shape = shapes[i];
        const radius = shape.diameter / 2;
        const cellTopLeftX = shape.cell.col * cellWidth;
        const cellTopLeftY = shape.cell.row * cellHeight;
        let cx, cy;
        switch (shape.alignment) {
            case 'center':
                cx = cellTopLeftX + cellWidth / 2;
                cy = cellTopLeftY + cellHeight / 2;
                break;
            case 'topLeft':
                cx = cellTopLeftX + radius;
                cy = cellTopLeftY + radius;
                break;
            case 'coordinates':
                if (shape.coordinates) {
                    cx = cellTopLeftX + shape.coordinates.x;
                    cy = cellTopLeftY + shape.coordinates.y;
                } else {
                    cx = cellTopLeftX + cellWidth / 2;
                    cy = cellTopLeftY + cellHeight / 2;
                }
                break;
        }

        if (shape.type === 'circle') {
            const distance = Math.sqrt((x - cx) ** 2 + (y - cy) ** 2);
            if (distance <= radius) {
                return shape;
            }
        } else if (shape.type === 'square') {
            if (x >= cx - radius && x <= cx + radius && y >= cy - radius && y <= cy + radius) {
                return shape;
            }
        }
    }
    return undefined;
  };

  const handleCanvasInteraction = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    if (event.button === 2) { // Right-click
        event.preventDefault();
        const shapeToDelete = findShapeAt(x, y);
        if (shapeToDelete) {
            deleteShape(shapeToDelete.id);
        }
        return;
    }

    // Left-click to add shape
    const cellWidth = width / grid.cols;
    const cellHeight = height / grid.rows;
    const col = Math.floor(x / cellWidth);
    const row = Math.floor(y / cellHeight);
    addShape({ row, col }, 'center');
  };

  return (
    <canvas
      ref={canvasRef}
      width={width}
      height={height}
      onMouseDown={handleCanvasInteraction}
      onContextMenu={(e) => e.preventDefault()} // Prevent context menu on right-click
      className="border border-gray-400"
    />
  );
};

export default Canvas;