"use strict";exports.id=357,exports.ids=[357],exports.modules={14132:(e,t,a)=>{a.d(t,{p:()=>o});var l=a(45781),r=a(13072),s=a(77401);let o=r.forwardRef(({className:e,type:t,...a},r)=>(0,l.jsx)("input",{type:t,className:(0,s.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...a}));o.displayName="Input"},48414:(e,t,a)=>{a.d(t,{bq:()=>u,eb:()=>x,gC:()=>m,l6:()=>c,yv:()=>h});var l=a(45781),r=a(13072),s=a(77381),o=a(71776),n=a(48613),d=a(28108),i=a(77401);let c=s.bL;s.YJ;let h=s.WT,u=r.forwardRef(({className:e,children:t,...a},r)=>(0,l.jsxs)(s.l9,{ref:r,className:(0,i.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[t,(0,l.jsx)(s.In,{asChild:!0,children:(0,l.jsx)(o.A,{className:"h-4 w-4 opacity-50"})})]}));u.displayName=s.l9.displayName;let f=r.forwardRef(({className:e,...t},a)=>(0,l.jsx)(s.PP,{ref:a,className:(0,i.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,l.jsx)(n.A,{className:"h-4 w-4"})}));f.displayName=s.PP.displayName;let p=r.forwardRef(({className:e,...t},a)=>(0,l.jsx)(s.wn,{ref:a,className:(0,i.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,l.jsx)(o.A,{className:"h-4 w-4"})}));p.displayName=s.wn.displayName;let m=r.forwardRef(({className:e,children:t,position:a="popper",...r},o)=>(0,l.jsx)(s.ZL,{children:(0,l.jsxs)(s.UC,{ref:o,className:(0,i.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...r,children:[(0,l.jsx)(f,{}),(0,l.jsx)(s.LM,{className:(0,i.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,l.jsx)(p,{})]})}));m.displayName=s.UC.displayName,r.forwardRef(({className:e,...t},a)=>(0,l.jsx)(s.JU,{ref:a,className:(0,i.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=s.JU.displayName;let x=r.forwardRef(({className:e,children:t,...a},r)=>(0,l.jsxs)(s.q7,{ref:r,className:(0,i.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[(0,l.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,l.jsx)(s.VF,{children:(0,l.jsx)(d.A,{className:"h-4 w-4"})})}),(0,l.jsx)(s.p4,{children:t})]}));x.displayName=s.q7.displayName,r.forwardRef(({className:e,...t},a)=>(0,l.jsx)(s.wv,{ref:a,className:(0,i.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=s.wv.displayName},72357:(e,t,a)=>{a.d(t,{default:()=>p});var l=a(45781),r=a(13072),s=a.n(r),o=a(14784),n=a(43030),d=a(14132),i=a(44238),c=a(48414),h=a(78366);let u=({canvasSize:e,setCanvasSize:t,setGrid:a,grid:r,selectedColor:u,setSelectedColor:f,selectedShapeType:p,setSelectedShapeType:m,diameter:x,setDiameter:g,replaceColor:w,resetCanvas:y,exportCanvas:j,binarizationEnabled:b,setBinarizationEnabled:v})=>{let[M,N]=s().useState("#000000"),[C,S]=s().useState("#ff0000"),[F,R]=s().useState({width:0,height:0});return s().useEffect(()=>{r.cols>0&&r.rows>0&&R({width:e.width/r.cols,height:e.height/r.rows})},[e,r]),(0,l.jsxs)(o.Zp,{className:"w-full md:w-80",children:[(0,l.jsx)(o.aR,{children:(0,l.jsx)(o.ZB,{children:"工具栏"})}),(0,l.jsxs)(o.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(n.J,{children:"画布尺寸 (px)"}),(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsx)(d.p,{type:"number",defaultValue:1920,onChange:a=>t({width:parseInt(a.target.value),height:e.height}),placeholder:"宽度"}),(0,l.jsx)(d.p,{type:"number",defaultValue:1080,onChange:a=>t({width:e.width,height:parseInt(a.target.value)}),placeholder:"高度"})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(n.J,{children:"网格 (列x行)"}),(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsx)(d.p,{type:"number",value:r.cols,onChange:e=>a({...r,cols:parseInt(e.target.value)}),placeholder:"列"}),(0,l.jsx)(d.p,{type:"number",value:r.rows,onChange:e=>a({...r,rows:parseInt(e.target.value)}),placeholder:"行"})]})]}),(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(n.J,{children:"单元格尺寸"}),(0,l.jsxs)("div",{className:"text-sm text-muted-foreground p-2 border rounded-md",children:[(0,l.jsxs)("div",{children:["宽: ",F.width.toFixed(2)," px"]}),(0,l.jsxs)("div",{children:["高: ",F.height.toFixed(2)," px"]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(n.J,{children:"图形类型"}),(0,l.jsxs)(c.l6,{value:p,onValueChange:e=>m(e),children:[(0,l.jsx)(c.bq,{children:(0,l.jsx)(c.yv,{placeholder:"选择图形"})}),(0,l.jsxs)(c.gC,{children:[(0,l.jsx)(c.eb,{value:"circle",children:"圆形"}),(0,l.jsx)(c.eb,{value:"square",children:"MTF"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(n.J,{children:"直径"}),(0,l.jsx)(d.p,{type:"number",value:x,onChange:e=>g(parseInt(e.target.value,10)||0)})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(n.J,{children:"图形颜色"}),(0,l.jsx)(d.p,{type:"color",value:u,onChange:e=>f(e.target.value),className:"w-full"})]}),(0,l.jsxs)("div",{className:"space-y-2 border-t pt-4",children:[(0,l.jsx)(n.J,{children:"替换颜色"}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(d.p,{placeholder:"旧颜色",type:"color",value:M,onChange:e=>N(e.target.value)}),(0,l.jsx)("span",{children:">"}),(0,l.jsx)(d.p,{placeholder:"新颜色",type:"color",value:C,onChange:e=>S(e.target.value)})]}),(0,l.jsx)(i.$,{onClick:()=>w(M,C),className:"w-full",children:"替换"})]}),(0,l.jsxs)("div",{className:"space-y-2 border-t pt-4",children:[(0,l.jsx)(n.J,{children:"画布操作"}),(0,l.jsx)(i.$,{onClick:y,className:"w-full",variant:"destructive",children:"重置画布"}),(0,l.jsx)(i.$,{onClick:()=>j("png"),className:"w-full",children:"导出为 PNG"}),(0,l.jsx)(i.$,{onClick:()=>j("jpeg"),className:"w-full",children:"导出为 JPEG"}),(0,l.jsxs)("div",{className:"flex items-center justify-between pt-2",children:[(0,l.jsx)(n.J,{htmlFor:"binarization-switch",children:"导出时二值化"}),(0,l.jsx)(h.d,{id:"binarization-switch",checked:b,onCheckedChange:v})]})]})]})]})},f=({width:e,height:t,grid:a,shapes:s,addShape:o,deleteShape:n,backgroundColor:d})=>{let i=(0,r.useRef)(null);(0,r.useEffect)(()=>{let l=i.current;if(!l)return;let r=l.getContext("2d");if(!r)return;let o=window.devicePixelRatio||1;l.width=e*o,l.height=t*o,l.style.width=`${e}px`,l.style.height=`${t}px`,r.scale(o,o),r.fillStyle=d,r.fillRect(0,0,e,t),c(r);let n=l=>{let r,s;let o=e/a.cols,n=t/a.rows,d=l.diameter/2,i=l.cell.col*o,c=l.cell.row*n;switch(l.alignment){case"center":r=i+o/2,s=c+n/2;break;case"topLeft":r=i+d,s=c+d;break;case"coordinates":l.coordinates?(r=i+l.coordinates.x,s=c+l.coordinates.y):(r=i+o/2,s=c+n/2)}return{cx:r,cy:s}},h=(e,t,a,l,r,s)=>{let o=document.createElement("canvas");o.width=l*s,o.height=l*s;let n=o.getContext("2d");if(!n)return;n.scale(s,s),n.fillStyle="#FFFFFF",n.fillRect(0,0,l,l),n.fillStyle=r,n.imageSmoothingEnabled=!1;let d=l/2,i=l/2;n.fillRect(Math.round(0),Math.round(i-1),Math.round(l),Math.round(2)),n.fillRect(Math.round(d-1),Math.round(0),Math.round(2),Math.round(l));for(let e=d-1-2;e>0;e-=4)n.fillRect(Math.round(e-2),Math.round(0),Math.round(2),Math.round(l/2-1));for(let e=i-1-2;e>0;e-=4)n.fillRect(Math.round(d+1),Math.round(e-2),Math.round(l/2-1),Math.round(2));for(let e=i+1+2;e<l;e+=4)n.fillRect(Math.round(0),Math.round(e),Math.round(l/2-1),Math.round(2));for(let e=d+1+2;e<l;e+=4)n.fillRect(Math.round(e),Math.round(i+1),Math.round(2),Math.round(l/2-1));e.imageSmoothingEnabled=!1,e.drawImage(o,t,a,l,l)},u=(e,t,a)=>{let{cx:l,cy:r}=n(t),s=t.diameter/2;"circle"===t.type?(e.save(),e.imageSmoothingEnabled=!1,e.fillStyle=t.color,e.beginPath(),e.arc(l,r,s,0,2*Math.PI),e.fill(),e.restore()):"square"===t.type&&h(e,l-s,r-s,t.diameter,t.color,a)};s.forEach(e=>u(r,e,o))},[e,t,a,s,d]);let c=l=>{l.strokeStyle="rgba(0, 0, 0, 0.5)",l.setLineDash([5,5]),l.lineWidth=1;let r=e/a.cols,s=t/a.rows;for(let e=1;e<a.cols;e++)l.beginPath(),l.moveTo(e*r,0),l.lineTo(e*r,t),l.stroke();for(let t=1;t<a.rows;t++)l.beginPath(),l.moveTo(0,t*s),l.lineTo(e,t*s),l.stroke();l.setLineDash([])},h=(l,r)=>{let o=e/a.cols,n=t/a.rows;for(let e=s.length-1;e>=0;e--){let t,a;let d=s[e],i=d.diameter/2,c=d.cell.col*o,h=d.cell.row*n;switch(d.alignment){case"center":t=c+o/2,a=h+n/2;break;case"topLeft":t=c+i,a=h+i;break;case"coordinates":d.coordinates?(t=c+d.coordinates.x,a=h+d.coordinates.y):(t=c+o/2,a=h+n/2)}if("circle"===d.type){if(Math.sqrt((l-t)**2+(r-a)**2)<=i)return d}else if("square"===d.type&&l>=t-i&&l<=t+i&&r>=a-i&&r<=a+i)return d}};return(0,l.jsx)("canvas",{ref:i,width:e,height:t,onMouseDown:l=>{let r=i.current;if(!r)return;let s=r.getBoundingClientRect(),d=l.clientX-s.left,c=l.clientY-s.top;if(2===l.button){l.preventDefault();let e=h(d,c);e&&n(e.id);return}let u=e/a.cols;o({row:Math.floor(c/(t/a.rows)),col:Math.floor(d/u)},"center")},onContextMenu:e=>e.preventDefault(),className:"border border-gray-400"})},p=()=>{let[e,t]=(0,r.useState)({width:1920,height:1080}),[a,s]=(0,r.useState)({rows:9,cols:17}),[o,n]=(0,r.useState)([]),[d,i]=(0,r.useState)("#000000"),[c,h]=(0,r.useState)("#FFFFFF"),[p,m]=(0,r.useState)("circle"),[x,g]=(0,r.useState)(50),[w,y]=(0,r.useState)(!0),j=async t=>{if("undefined"==typeof document)return;let l=document.createElement("canvas");l.width=e.width,l.height=e.height;let r=l.getContext("2d");if(!r){console.error("Could not get temporary canvas context for exporting.");return}r.fillStyle=c,r.fillRect(0,0,l.width,l.height);let s=e=>{let t,r;let s=l.width/a.cols,o=l.height/a.rows,n=e.diameter/2,d=e.cell.col*s,i=e.cell.row*o;switch(e.alignment){case"center":t=d+s/2,r=i+o/2;break;case"topLeft":t=d+n,r=i+n;break;case"coordinates":e.coordinates?(t=d+e.coordinates.x,r=i+e.coordinates.y):(t=d+s/2,r=i+o/2)}return{cx:t,cy:r}},n=(e,t,a,l,r)=>{let s=document.createElement("canvas");s.width=l,s.height=l;let o=s.getContext("2d");if(!o)return;o.fillStyle="#FFFFFF",o.fillRect(0,0,l,l),o.fillStyle=r,o.imageSmoothingEnabled=!1;let n=l/2,d=l/2;o.fillRect(Math.round(0),Math.round(d-1),Math.round(l),Math.round(2)),o.fillRect(Math.round(n-1),Math.round(0),Math.round(2),Math.round(l));for(let e=n-1-2;e>0;e-=4)o.fillRect(Math.round(e-2),Math.round(0),Math.round(2),Math.round(l/2-1));for(let e=d-1-2;e>0;e-=4)o.fillRect(Math.round(n+1),Math.round(e-2),Math.round(l/2-1),Math.round(2));for(let e=d+1+2;e<l;e+=4)o.fillRect(Math.round(0),Math.round(e),Math.round(l/2-1),Math.round(2));for(let e=n+1+2;e<l;e+=4)o.fillRect(Math.round(e),Math.round(d+1),Math.round(2),Math.round(l/2-1));e.imageSmoothingEnabled=!1,e.drawImage(s,t,a)};for(let e of o){let{cx:t,cy:a}=s(e),l=e.diameter/2;"circle"===e.type?(r.fillStyle=e.color,r.beginPath(),r.arc(t,a,l,0,2*Math.PI),r.fill()):"square"===e.type&&n(r,t-l,a-l,e.diameter,e.color)}w&&(e=>{let t=e.getImageData(0,0,e.canvas.width,e.canvas.height),a=t.data;for(let e=0;e<a.length;e+=4){let t=a[e];.299*t+.587*a[e+1]+.114*a[e+2]<128?(a[e]=0,a[e+1]=0,a[e+2]=0):(a[e]=255,a[e+1]=255,a[e+2]=255)}e.putImageData(t,0,0)})(r);let d=l.toDataURL(`image/${t}`),i=document.createElement("a");i.href=d,i.download=`drawing-board-${Date.now()}.${t}`,document.body.appendChild(i),i.click(),document.body.removeChild(i)};return(0,l.jsxs)("div",{className:"flex flex-col md:flex-row h-full gap-4",children:[(0,l.jsx)(u,{canvasSize:e,setCanvasSize:t,setGrid:s,grid:a,selectedColor:d,setSelectedColor:i,selectedShapeType:p,setSelectedShapeType:m,diameter:x,setDiameter:g,replaceColor:(e,t)=>{let a=e.toLowerCase(),l=t.toLowerCase();c.toLowerCase()===a&&h(l),n(e=>e.map(e=>e.color.toLowerCase()===a?{...e,color:l}:e))},resetCanvas:()=>{n([]),h("#FFFFFF")},exportCanvas:j,binarizationEnabled:w,setBinarizationEnabled:y}),(0,l.jsx)("div",{className:"flex-grow overflow-auto",children:(0,l.jsx)(f,{width:e.width,height:e.height,grid:a,shapes:o,addShape:(e,t,a)=>{n([...o,{id:Date.now(),type:p,cell:e,color:d,diameter:x,alignment:t,coordinates:a}])},deleteShape:e=>{n(o.filter(t=>t.id!==e))},backgroundColor:c})})]})}}};