"use client";

import React, { useState, useCallback, useEffect, useRef } from 'react';
import jschardet from 'jschardet';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "@/components/ui/use-toast"; 

import { ProcessedBlock } from '@/workers/logParser.definitions';

interface LogFileUploadProps {
  onProcessingStart: () => void;
  onDataProcessed: (data: ProcessedBlock[]) => void;
  onError: (error: string) => void;
}

const LogFileUpload: React.FC<LogFileUploadProps> = ({
  onProcessingStart,
  onDataProcessed,
  onError,
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [fileStats, setFileStats] = useState<{ totalLines: number | null, firstLogTime: string | null, lastLogTime: string | null } | null>(null);
  // NEW STATE for custom input display name
  const [selectedFileName, setSelectedFileName] = useState<string>("未选择文件");
  const workerRef = useRef<Worker | null>(null);

  const onDataProcessedRef = useRef(onDataProcessed);
  const onErrorRef = useRef(onError);
  const onProcessingStartRef = useRef(onProcessingStart);

  useEffect(() => {
    onDataProcessedRef.current = onDataProcessed;
  }, [onDataProcessed]);

  useEffect(() => {
    onErrorRef.current = onError;
  }, [onError]);

  useEffect(() => {
    onProcessingStartRef.current = onProcessingStart;
  }, [onProcessingStart]);

  const calculateAndSetFileStats = (blocks: ProcessedBlock[]) => {
    if (!blocks || blocks.length === 0) {
      setFileStats(null);
      return;
    }

    let totalLines = 0;
    let earliestStartTimeObj: Date | null = null;
    let latestEndTimeObj: Date | null = null;

    for (const block of blocks) {
      totalLines += block.lines_count;

      if (block.start_time) {
        try {
          const currentStartTime = new Date(block.start_time.replace(",", "."));
          if (!earliestStartTimeObj || currentStartTime < earliestStartTimeObj) {
            earliestStartTimeObj = currentStartTime;
          }
        } catch (e) {
          console.warn("无法解析起始时间:", block.start_time, e);
        }
      }

      if (block.end_time) {
        try {
          const currentEndTime = new Date(block.end_time.replace(",", "."));
          if (!latestEndTimeObj || currentEndTime > latestEndTimeObj) {
            latestEndTimeObj = currentEndTime;
          }
        } catch (e) {
          console.warn("无法解析结束时间:", block.end_time, e);
        }
      }
    }

    const formatDisplayDateTime = (date: Date | null): string | null => {
      if (!date || isNaN(date.getTime())) return null;
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    };

    setFileStats({
      totalLines: totalLines,
      firstLogTime: formatDisplayDateTime(earliestStartTimeObj),
      lastLogTime: formatDisplayDateTime(latestEndTimeObj)
    });
  };

  // MODIFIED/NEW FUNCTION to handle actual file input change
  const handleActualFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      setSelectedFile(file); // Existing state for the actual File object
      setSelectedFileName(file.name); // Update display name
      setFileStats(null); // Reset stats as per existing logic
    } else {
      setSelectedFile(null);
      setSelectedFileName("未选择文件");
      setFileStats(null);
    }
  };

  useEffect(() => {
    console.log('[LogFileUpload] useEffect[] - START: Component did mount / Worker setup effect running.');
    if (typeof window !== 'undefined') {
      console.log('[LogFileUpload] useEffect[] - Creating new Worker instance.');
      const newWorker = new Worker(new URL('../../workers/logParser.worker.ts', import.meta.url), {
        type: 'module'
      });
      console.log('[LogFileUpload] useEffect[] - New Worker instance CREATED.');

      newWorker.onmessage = (event: MessageEvent<{ success?: boolean; allBlocks?: ProcessedBlock[]; error?: string; message?: string }>) => {
        console.log('[LogFileUpload] Worker onmessage - RECEIVED:', event.data);
        setIsProcessing(false);
        if (event.data.error) {
          console.log('[LogFileUpload] Worker onmessage - Calling onErrorRef.current with error.');
          onErrorRef.current(event.data.error);
          setFileStats(null);
          toast({ title: "处理错误", description: event.data.error, variant: "destructive" });
        } else if (event.data.success && event.data.allBlocks) {
          console.log('[LogFileUpload] Worker onmessage - Calling onDataProcessedRef.current with data.');
          onDataProcessedRef.current(event.data.allBlocks);
          calculateAndSetFileStats(event.data.allBlocks);
          toast({ title: "处理完成", description: event.data.message || "日志文件已成功解析。" });
        }
        console.log('[LogFileUpload] Worker onmessage - END.');
      };

      newWorker.onerror = (err) => {
        console.log('[LogFileUpload] Worker onerror - RECEIVED error:', err);
        setIsProcessing(false);
        onErrorRef.current(err.message);
        setFileStats(null);
        toast({ title: "Worker 错误", description: err.message, variant: "destructive" });
        console.log('[LogFileUpload] Worker onerror - END.');
      };

      workerRef.current = newWorker;
      console.log('[LogFileUpload] useEffect[] - Worker instance assigned to workerRef.current.');
    } else {
      console.log('[LogFileUpload] useEffect[] - window is undefined, Worker NOT created (SSR or other env).');
    }

    return () => {
      console.log('[LogFileUpload] useEffect[] - CLEANUP FUNCTION START: Component will unmount / Worker teardown.');
      const worker = workerRef.current;
      if (worker) {
        console.log('[LogFileUpload] Cleanup: Attempting to terminate worker...', worker);
        try {
          worker.onmessage = null;
          worker.onerror = null;
          worker.terminate();
          console.log('[LogFileUpload] Cleanup: Worker terminated successfully.');
        } catch (e) {
          console.error('[LogFileUpload] Cleanup: Error terminating worker:', e);
        }
        workerRef.current = null;
        console.log('[LogFileUpload] Cleanup: workerRef.current set to null.');
      } else {
        console.log('[LogFileUpload] Cleanup: No worker instance found in ref to terminate.');
      }
      console.log('[LogFileUpload] useEffect[] - CLEANUP FUNCTION END.');
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps
  // Removed refs from deps as they are stable, calculateAndSetFileStats is also stable due to useState setter

  const handleUpload = useCallback(async () => {
    console.log('[LogFileUpload] handleUpload - START.');
    if (!selectedFile) {
      toast({
        title: "没有选择文件",
        description: "请选择一个日志文件进行上传。",
        variant: "destructive",
      });
      console.log('[LogFileUpload] handleUpload - No file selected, returning.');
      return;
    }

    console.log('[LogFileUpload] handleUpload - Setting isProcessing to true, calling onProcessingStartRef.current().');
    setIsProcessing(true);
    onProcessingStartRef.current();
    setFileStats(null);
    toast({
      title: "开始处理",
      description: `正在处理文件: ${selectedFile.name}`,
    });

    if (!workerRef.current) {
      toast({
        title: "Worker 未初始化",
        description: "Web Worker 尚未准备好，请稍后再试。",
        variant: "destructive",
      });
      onErrorRef.current("Worker not initialized");
      setIsProcessing(false);
      setFileStats(null);
      console.log('[LogFileUpload] handleUpload - Worker not initialized, returning.');
      return;
    }
    console.log('[LogFileUpload] handleUpload - Worker instance exists, proceeding with file processing.');

    try {
      const arrayBuffer = await selectedFile!.arrayBuffer();
      if (!arrayBuffer || arrayBuffer.byteLength === 0) {
        setIsProcessing(false);
        onErrorRef.current('文件为空或读取失败。');
        setFileStats(null);
        toast({ title: "错误", description: '文件为空或读取失败。', variant: "destructive" });
        return;
      }

      let fileContent = '';
      let encodingToTry = 'UTF-8';
      let jschardetFailed = false;

      const uInt8ArrayForDetection = new Uint8Array(arrayBuffer);
      console.log('[LogFileUpload] uInt8ArrayForDetection type:', typeof uInt8ArrayForDetection, 'instanceof Uint8Array:', uInt8ArrayForDetection instanceof Uint8Array, 'length:', uInt8ArrayForDetection.length);
      if (uInt8ArrayForDetection.length > 0) {
        console.log('[LogFileUpload] uInt8ArrayForDetection sample (first 20 bytes):', uInt8ArrayForDetection.slice(0, 20));
      }

      if (uInt8ArrayForDetection.length > 10) {
        try {
          console.log('[LogFileUpload] Attempting jschardet.detect...');
          // Convert Uint8Array to Buffer for jschardet compatibility
          const bufferForDetection = Buffer.from(uInt8ArrayForDetection);
          const detectionResult = jschardet.detect(bufferForDetection);
          console.log('[LogFileUpload] jschardet.detect result:', detectionResult);
          if (detectionResult && detectionResult.encoding && detectionResult.confidence > 0.2) {
            encodingToTry = detectionResult.encoding.toUpperCase();
            console.log(`[LogFileUpload] jschardet confidently detected encoding: ${encodingToTry} with confidence: ${detectionResult.confidence}`);
            if (['GBK', 'GB2312', 'GB18030', 'BIG5'].includes(encodingToTry)) {
              encodingToTry = 'gbk';
            } else if (!['UTF-8', 'ASCII'].includes(encodingToTry)) {
              console.warn(`[LogFileUpload] Detected encoding ${encodingToTry} is not directly handled by TextDecoder as is, defaulting to GBK for now.`);
              encodingToTry = 'gbk';
            }
          } else {
            console.warn('[LogFileUpload] jschardet could not detect encoding with sufficient confidence. Will try fallbacks. Result:', detectionResult);
            jschardetFailed = true;
          }
        } catch (e: any) {
          console.error('[LogFileUpload] Error during jschardet.detect:', e.message, e);
          jschardetFailed = true;
          if (e.message && e.message.includes("aBuf.slice(...).split is not a function")) {
            // This specific error will be handled by the final catch if all decodes fail
          }
        }
      } else {
        console.warn('[LogFileUpload] File too small for reliable encoding detection. Will try fallbacks.');
        jschardetFailed = true;
      }

      try {
        if (!jschardetFailed) {
          console.log(`[LogFileUpload] Attempting to decode with detected/chosen encoding: ${encodingToTry}`);
          fileContent = new TextDecoder(encodingToTry.toLowerCase()).decode(arrayBuffer);
        } else {
          throw new Error("jschardet failed or was not confident, proceeding to fallbacks.");
        }
      } catch (decodeError1: any) {
        console.warn(`[LogFileUpload] Decoding with ${encodingToTry} failed (or skipped). Error: ${decodeError1.message}`);
        try {
          encodingToTry = 'gbk';
          console.log(`[LogFileUpload] Attempting to decode with fallback: ${encodingToTry}`);
          fileContent = new TextDecoder(encodingToTry.toLowerCase()).decode(arrayBuffer);
        } catch (decodeError2: any) {
          console.warn(`[LogFileUpload] Decoding with ${encodingToTry} failed. Error: ${decodeError2.message}`);
          encodingToTry = 'utf-8';
          console.log(`[LogFileUpload] Attempting to decode with last resort: ${encodingToTry}`);
          fileContent = new TextDecoder(encodingToTry.toLowerCase()).decode(arrayBuffer);
        }
      }
      console.log('[LogFileUpload] Successfully decoded file content using encoding:', encodingToTry);

      console.log('[LogFileUpload] handleUpload - Posting message to worker with fileContent.');
      workerRef.current.postMessage(fileContent);

    } catch (finalError: any) {
      console.error('[LogFileUpload] Final catch after all processing attempts in handleUpload:', finalError);
      setIsProcessing(false);
      setFileStats(null);
      const errorMessage = finalError.message.includes("aBuf.slice(...).split is not a function")
        ? finalError.message
        : `文件解码失败: ${finalError.message}. 请确保文件是文本格式且编码受支持。`;
      onErrorRef.current(errorMessage);
      toast({ title: "解码失败", description: errorMessage, variant: "destructive" });
      return;
    }
    console.log('[LogFileUpload] handleUpload - END (processing will continue in worker).');
  }, [selectedFile]); // calculateAndSetFileStats is stable

  return (
    <Card className="w-full max-w-md h-[450px] flex flex-col">
      <CardHeader>
        <CardTitle>胶合日志文件上传</CardTitle>
        <CardDescription>选择一个日志文件进行分析。</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6 p-6 flex-grow flex flex-col overflow-y-auto">
        <div className="flex flex-col items-start gap-1.5"> {/* Ensure this gap is appropriate */}
          <Label htmlFor="hidden-log-file" className="text-sm font-medium">选择文件</Label> {/* Points to hidden input */}

          {/* NEW Custom File Input Structure */}
          <label
            htmlFor="hidden-log-file"
            className="w-full h-10 rounded-md border border-input flex items-center cursor-pointer text-sm ring-offset-background focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2"
          >
            <span className="bg-primary text-primary-foreground h-full flex items-center justify-center px-4 rounded-l-md whitespace-nowrap">
              上传文件
            </span>
            <span className="flex-grow h-full px-3 py-2 flex items-center text-muted-foreground overflow-hidden">
              <span className="truncate">
                {selectedFileName}
              </span>
            </span>
          </label>
          <input
            id="hidden-log-file"
            type="file"
            className="sr-only" // Effectively hidden
            onChange={handleActualFileChange} // NEW handler
            disabled={isProcessing}
            accept=".log,.txt,application/octet-stream"
          />
        </div>
        {selectedFile && (
          <div className="space-y-2 text-sm pt-3">
            <p className="text-muted-foreground">
              已选择: {selectedFile.name} ({(selectedFile.size / 1024).toFixed(2)} KB)
            </p>
            {fileStats && !isProcessing && (
              <div className="border-t pt-3 mt-3 space-y-1 text-xs text-muted-foreground">
                <p><span className="font-semibold text-foreground">总行数:</span> {fileStats.totalLines !== null ? fileStats.totalLines.toLocaleString() : "N/A"}</p>
                <p><span className="font-semibold text-foreground">起始时间:</span> {fileStats.firstLogTime || "N/A"}</p>
                <p><span className="font-semibold text-foreground">结束时间:</span> {fileStats.lastLogTime || "N/A"}</p>
              </div>
            )}
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button onClick={handleUpload} disabled={!selectedFile || isProcessing} className="w-full">
          {isProcessing ? (
            <>
              {/* Optional: Add a spinner icon here */}
              处理中...
            </>
          ) : '上传并分析'}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default LogFileUpload;