"use strict";exports.id=748,exports.ids=[748],exports.modules={4910:(e,t,n)=>{n.d(t,{C1:()=>P,bL:()=>T});var o=n(13072),r=n(25182),i=n(5016),a=n(85271),s=n(75634),l=n(82785),c=n(56486),u=n(98418),d=n(22839),p=n(45781),f="Checkbox",[m,h]=(0,i.A)(f),[v,g]=m(f),w=o.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:i,checked:l,defaultChecked:c,required:u,disabled:f,value:m="on",onCheckedChange:h,form:g,...w}=e,[y,b]=o.useState(null),T=(0,r.s)(t,e=>b(e)),P=o.useRef(!1),E=!y||g||!!y.closest("form"),[k=!1,R]=(0,s.i)({prop:l,defaultProp:c,onChange:h}),D=o.useRef(k);return o.useEffect(()=>{let e=y?.form;if(e){let t=()=>R(D.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[y,R]),(0,p.jsxs)(v,{scope:n,state:k,disabled:f,children:[(0,p.jsx)(d.sG.button,{type:"button",role:"checkbox","aria-checked":C(k)?"mixed":k,"aria-required":u,"data-state":S(k),"data-disabled":f?"":void 0,disabled:f,value:m,...w,ref:T,onKeyDown:(0,a.m)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,a.m)(e.onClick,e=>{R(e=>!!C(e)||!e),E&&(P.current=e.isPropagationStopped(),P.current||e.stopPropagation())})}),E&&(0,p.jsx)(x,{control:y,bubbles:!P.current,name:i,value:m,checked:k,required:u,disabled:f,form:g,style:{transform:"translateX(-100%)"},defaultChecked:!C(c)&&c})]})});w.displayName=f;var y="CheckboxIndicator",b=o.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:o,...r}=e,i=g(y,n);return(0,p.jsx)(u.C,{present:o||C(i.state)||!0===i.state,children:(0,p.jsx)(d.sG.span,{"data-state":S(i.state),"data-disabled":i.disabled?"":void 0,...r,ref:t,style:{pointerEvents:"none",...e.style}})})});b.displayName=y;var x=e=>{let{control:t,checked:n,bubbles:r=!0,defaultChecked:i,...a}=e,s=o.useRef(null),u=(0,l.Z)(n),d=(0,c.X)(t);o.useEffect(()=>{let e=s.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(u!==n&&t){let o=new Event("click",{bubbles:r});e.indeterminate=C(n),t.call(e,!C(n)&&n),e.dispatchEvent(o)}},[u,n,r]);let f=o.useRef(!C(n)&&n);return(0,p.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:i??f.current,...a,tabIndex:-1,ref:s,style:{...e.style,...d,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function C(e){return"indeterminate"===e}function S(e){return C(e)?"indeterminate":e?"checked":"unchecked"}var T=w,P=b},6664:(e,t,n)=>{n.d(t,{Kq:()=>W,UC:()=>B,bL:()=>_,l9:()=>M});var o=n(13072),r=n(85271),i=n(25182),a=n(5016),s=n(98535),l=n(86860),c=n(39365),u=(n(75673),n(98418)),d=n(22839),p=n(83759),f=n(75634),m=n(32890),h=n(45781),[v,g]=(0,a.A)("Tooltip",[c.Bk]),w=(0,c.Bk)(),y="TooltipProvider",b="tooltip.open",[x,C]=v(y),S=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:r=300,disableHoverableContent:i=!1,children:a}=e,[s,l]=o.useState(!0),c=o.useRef(!1),u=o.useRef(0);return o.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,h.jsx)(x,{scope:t,isOpenDelayed:s,delayDuration:n,onOpen:o.useCallback(()=>{window.clearTimeout(u.current),l(!1)},[]),onClose:o.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>l(!0),r)},[r]),isPointerInTransitRef:c,onPointerInTransitChange:o.useCallback(e=>{c.current=e},[]),disableHoverableContent:i,children:a})};S.displayName=y;var T="Tooltip",[P,E]=v(T),k=e=>{let{__scopeTooltip:t,children:n,open:r,defaultOpen:i=!1,onOpenChange:a,disableHoverableContent:s,delayDuration:u}=e,d=C(T,e.__scopeTooltip),p=w(t),[m,v]=o.useState(null),g=(0,l.B)(),y=o.useRef(0),x=s??d.disableHoverableContent,S=u??d.delayDuration,E=o.useRef(!1),[k=!1,R]=(0,f.i)({prop:r,defaultProp:i,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(b))):d.onClose(),a?.(e)}}),D=o.useMemo(()=>k?E.current?"delayed-open":"instant-open":"closed",[k]),Y=o.useCallback(()=>{window.clearTimeout(y.current),y.current=0,E.current=!1,R(!0)},[R]),A=o.useCallback(()=>{window.clearTimeout(y.current),y.current=0,R(!1)},[R]),L=o.useCallback(()=>{window.clearTimeout(y.current),y.current=window.setTimeout(()=>{E.current=!0,R(!0),y.current=0},S)},[S,R]);return o.useEffect(()=>()=>{y.current&&(window.clearTimeout(y.current),y.current=0)},[]),(0,h.jsx)(c.bL,{...p,children:(0,h.jsx)(P,{scope:t,contentId:g,open:k,stateAttribute:D,trigger:m,onTriggerChange:v,onTriggerEnter:o.useCallback(()=>{d.isOpenDelayed?L():Y()},[d.isOpenDelayed,L,Y]),onTriggerLeave:o.useCallback(()=>{x?A():(window.clearTimeout(y.current),y.current=0)},[A,x]),onOpen:Y,onClose:A,disableHoverableContent:x,children:n})})};k.displayName=T;var R="TooltipTrigger",D=o.forwardRef((e,t)=>{let{__scopeTooltip:n,...a}=e,s=E(R,n),l=C(R,n),u=w(n),p=o.useRef(null),f=(0,i.s)(t,p,s.onTriggerChange),m=o.useRef(!1),v=o.useRef(!1),g=o.useCallback(()=>m.current=!1,[]);return o.useEffect(()=>()=>document.removeEventListener("pointerup",g),[g]),(0,h.jsx)(c.Mz,{asChild:!0,...u,children:(0,h.jsx)(d.sG.button,{"aria-describedby":s.open?s.contentId:void 0,"data-state":s.stateAttribute,...a,ref:f,onPointerMove:(0,r.m)(e.onPointerMove,e=>{"touch"===e.pointerType||v.current||l.isPointerInTransitRef.current||(s.onTriggerEnter(),v.current=!0)}),onPointerLeave:(0,r.m)(e.onPointerLeave,()=>{s.onTriggerLeave(),v.current=!1}),onPointerDown:(0,r.m)(e.onPointerDown,()=>{m.current=!0,document.addEventListener("pointerup",g,{once:!0})}),onFocus:(0,r.m)(e.onFocus,()=>{m.current||s.onOpen()}),onBlur:(0,r.m)(e.onBlur,s.onClose),onClick:(0,r.m)(e.onClick,s.onClose)})})});D.displayName=R;var[Y,A]=v("TooltipPortal",{forceMount:void 0}),L="TooltipContent",N=o.forwardRef((e,t)=>{let n=A(L,e.__scopeTooltip),{forceMount:o=n.forceMount,side:r="top",...i}=e,a=E(L,e.__scopeTooltip);return(0,h.jsx)(u.C,{present:o||a.open,children:a.disableHoverableContent?(0,h.jsx)(I,{side:r,...i,ref:t}):(0,h.jsx)(X,{side:r,...i,ref:t})})}),X=o.forwardRef((e,t)=>{let n=E(L,e.__scopeTooltip),r=C(L,e.__scopeTooltip),a=o.useRef(null),s=(0,i.s)(t,a),[l,c]=o.useState(null),{trigger:u,onClose:d}=n,p=a.current,{onPointerInTransitChange:f}=r,m=o.useCallback(()=>{c(null),f(!1)},[f]),v=o.useCallback((e,t)=>{let n=e.currentTarget,o={x:e.clientX,y:e.clientY},r=function(e,t){let n=Math.abs(t.top-e.y),o=Math.abs(t.bottom-e.y),r=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,o,r,i)){case i:return"left";case r:return"right";case n:return"top";case o:return"bottom";default:throw Error("unreachable")}}(o,n.getBoundingClientRect());c(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:+!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let o=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(o.y-n.y)>=(e.y-n.y)*(o.x-n.x))t.pop();else break}t.push(o)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let o=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(o.y-t.y)>=(e.y-t.y)*(o.x-t.x))n.pop();else break}n.push(o)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t,n=5){let o=[];switch(t){case"top":o.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":o.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":o.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":o.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return o}(o,r),...function(e){let{top:t,right:n,bottom:o,left:r}=e;return[{x:r,y:t},{x:n,y:t},{x:n,y:o},{x:r,y:o}]}(t.getBoundingClientRect())])),f(!0)},[f]);return o.useEffect(()=>()=>m(),[m]),o.useEffect(()=>{if(u&&p){let e=e=>v(e,p),t=e=>v(e,u);return u.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{u.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}},[u,p,v,m]),o.useEffect(()=>{if(l){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},o=u?.contains(t)||p?.contains(t),r=!function(e,t){let{x:n,y:o}=e,r=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e].x,s=t[e].y,l=t[i].x,c=t[i].y;s>o!=c>o&&n<(l-a)*(o-s)/(c-s)+a&&(r=!r)}return r}(n,l);o?m():r&&(m(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[u,p,l,d,m]),(0,h.jsx)(I,{...e,ref:s})}),[j,O]=v(T,{isInside:!1}),I=o.forwardRef((e,t)=>{let{__scopeTooltip:n,children:r,"aria-label":i,onEscapeKeyDown:a,onPointerDownOutside:l,...u}=e,d=E(L,n),f=w(n),{onClose:v}=d;return o.useEffect(()=>(document.addEventListener(b,v),()=>document.removeEventListener(b,v)),[v]),o.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;t?.contains(d.trigger)&&v()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,v]),(0,h.jsx)(s.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:l,onFocusOutside:e=>e.preventDefault(),onDismiss:v,children:(0,h.jsxs)(c.UC,{"data-state":d.stateAttribute,...f,...u,ref:t,style:{...u.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,h.jsx)(p.xV,{children:r}),(0,h.jsx)(j,{scope:n,isInside:!0,children:(0,h.jsx)(m.b,{id:d.contentId,role:"tooltip",children:i||r})})]})})});N.displayName=L;var z="TooltipArrow";o.forwardRef((e,t)=>{let{__scopeTooltip:n,...o}=e,r=w(n);return O(z,n).isInside?null:(0,h.jsx)(c.i3,{...r,...o,ref:t})}).displayName=z;var W=S,_=k,M=D,B=N},7563:(e,t,n)=>{n.d(t,{UC:()=>et,VY:()=>eo,ZL:()=>J,bL:()=>Q,bm:()=>er,hE:()=>en,hJ:()=>ee,l9:()=>$});var o=n(13072),r=n(85271),i=n(25182),a=n(5016),s=n(86860),l=n(75634),c=n(98535),u=n(49679),d=n(75673),p=n(98418),f=n(22839),m=n(57427),h=n(56735),v=n(25097),g=n(83759),w=n(45781),y="Dialog",[b,x]=(0,a.A)(y),[C,S]=b(y),T=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:i,onOpenChange:a,modal:c=!0}=e,u=o.useRef(null),d=o.useRef(null),[p=!1,f]=(0,l.i)({prop:r,defaultProp:i,onChange:a});return(0,w.jsx)(C,{scope:t,triggerRef:u,contentRef:d,contentId:(0,s.B)(),titleId:(0,s.B)(),descriptionId:(0,s.B)(),open:p,onOpenChange:f,onOpenToggle:o.useCallback(()=>f(e=>!e),[f]),modal:c,children:n})};T.displayName=y;var P="DialogTrigger",E=o.forwardRef((e,t)=>{let{__scopeDialog:n,...o}=e,a=S(P,n),s=(0,i.s)(t,a.triggerRef);return(0,w.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":K(a.open),...o,ref:s,onClick:(0,r.m)(e.onClick,a.onOpenToggle)})});E.displayName=P;var k="DialogPortal",[R,D]=b(k,{forceMount:void 0}),Y=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:i}=e,a=S(k,t);return(0,w.jsx)(R,{scope:t,forceMount:n,children:o.Children.map(r,e=>(0,w.jsx)(p.C,{present:n||a.open,children:(0,w.jsx)(d.Z,{asChild:!0,container:i,children:e})}))})};Y.displayName=k;var A="DialogOverlay",L=o.forwardRef((e,t)=>{let n=D(A,e.__scopeDialog),{forceMount:o=n.forceMount,...r}=e,i=S(A,e.__scopeDialog);return i.modal?(0,w.jsx)(p.C,{present:o||i.open,children:(0,w.jsx)(N,{...r,ref:t})}):null});L.displayName=A;var N=o.forwardRef((e,t)=>{let{__scopeDialog:n,...o}=e,r=S(A,n);return(0,w.jsx)(h.A,{as:g.DX,allowPinchZoom:!0,shards:[r.contentRef],children:(0,w.jsx)(f.sG.div,{"data-state":K(r.open),...o,ref:t,style:{pointerEvents:"auto",...o.style}})})}),X="DialogContent",j=o.forwardRef((e,t)=>{let n=D(X,e.__scopeDialog),{forceMount:o=n.forceMount,...r}=e,i=S(X,e.__scopeDialog);return(0,w.jsx)(p.C,{present:o||i.open,children:i.modal?(0,w.jsx)(O,{...r,ref:t}):(0,w.jsx)(I,{...r,ref:t})})});j.displayName=X;var O=o.forwardRef((e,t)=>{let n=S(X,e.__scopeDialog),a=o.useRef(null),s=(0,i.s)(t,n.contentRef,a);return o.useEffect(()=>{let e=a.current;if(e)return(0,v.Eq)(e)},[]),(0,w.jsx)(z,{...e,ref:s,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,r.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:(0,r.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,r.m)(e.onFocusOutside,e=>e.preventDefault())})}),I=o.forwardRef((e,t)=>{let n=S(X,e.__scopeDialog),r=o.useRef(!1),i=o.useRef(!1);return(0,w.jsx)(z,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(r.current||n.triggerRef.current?.focus(),t.preventDefault()),r.current=!1,i.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(r.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let o=t.target;n.triggerRef.current?.contains(o)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),z=o.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:a,onCloseAutoFocus:s,...l}=e,d=S(X,n),p=o.useRef(null),f=(0,i.s)(t,p);return(0,m.Oh)(),(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(u.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:a,onUnmountAutoFocus:s,children:(0,w.jsx)(c.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":K(d.open),...l,ref:f,onDismiss:()=>d.onOpenChange(!1)})}),(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(G,{titleId:d.titleId}),(0,w.jsx)(V,{contentRef:p,descriptionId:d.descriptionId})]})]})}),W="DialogTitle",_=o.forwardRef((e,t)=>{let{__scopeDialog:n,...o}=e,r=S(W,n);return(0,w.jsx)(f.sG.h2,{id:r.titleId,...o,ref:t})});_.displayName=W;var M="DialogDescription",B=o.forwardRef((e,t)=>{let{__scopeDialog:n,...o}=e,r=S(M,n);return(0,w.jsx)(f.sG.p,{id:r.descriptionId,...o,ref:t})});B.displayName=M;var H="DialogClose",F=o.forwardRef((e,t)=>{let{__scopeDialog:n,...o}=e,i=S(H,n);return(0,w.jsx)(f.sG.button,{type:"button",...o,ref:t,onClick:(0,r.m)(e.onClick,()=>i.onOpenChange(!1))})});function K(e){return e?"open":"closed"}F.displayName=H;var Z="DialogTitleWarning",[q,U]=(0,a.q)(Z,{contentName:X,titleName:W,docsSlug:"dialog"}),G=({titleId:e})=>{let t=U(Z),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return o.useEffect(()=>{e&&!document.getElementById(e)&&console.error(n)},[n,e]),null},V=({contentRef:e,descriptionId:t})=>{let n=U("DialogDescriptionWarning"),r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${n.contentName}}.`;return o.useEffect(()=>{let n=e.current?.getAttribute("aria-describedby");t&&n&&!document.getElementById(t)&&console.warn(r)},[r,e,t]),null},Q=T,$=E,J=Y,ee=L,et=j,en=_,eo=B,er=F},12710:(e,t,n)=>{n.d(t,{A:()=>o});let o=(0,n(22752).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},19604:(e,t,n)=>{n.d(t,{A:()=>o});let o=(0,n(22752).A)("ZoomOut",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]])},28705:(e,t,n)=>{n.d(t,{A:()=>o});let o=(0,n(22752).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},58967:(e,t,n)=>{n.d(t,{A:()=>o});let o=(0,n(22752).A)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},69662:(e,t,n)=>{n.d(t,{LM:()=>U,OK:()=>G,VM:()=>S,bL:()=>q,lr:()=>X});var o=n(13072),r=n(22839),i=n(98418),a=n(5016),s=n(25182),l=n(22883),c=n(70061),u=n(39068),d=n(1144),p=n(85271),f=n(45781),m="ScrollArea",[h,v]=(0,a.A)(m),[g,w]=h(m),y=o.forwardRef((e,t)=>{let{__scopeScrollArea:n,type:i="hover",dir:a,scrollHideDelay:l=600,...u}=e,[d,p]=o.useState(null),[m,h]=o.useState(null),[v,w]=o.useState(null),[y,b]=o.useState(null),[x,C]=o.useState(null),[S,T]=o.useState(0),[P,E]=o.useState(0),[k,R]=o.useState(!1),[D,Y]=o.useState(!1),A=(0,s.s)(t,e=>p(e)),L=(0,c.jH)(a);return(0,f.jsx)(g,{scope:n,type:i,dir:L,scrollHideDelay:l,scrollArea:d,viewport:m,onViewportChange:h,content:v,onContentChange:w,scrollbarX:y,onScrollbarXChange:b,scrollbarXEnabled:k,onScrollbarXEnabledChange:R,scrollbarY:x,onScrollbarYChange:C,scrollbarYEnabled:D,onScrollbarYEnabledChange:Y,onCornerWidthChange:T,onCornerHeightChange:E,children:(0,f.jsx)(r.sG.div,{dir:L,...u,ref:A,style:{position:"relative","--radix-scroll-area-corner-width":S+"px","--radix-scroll-area-corner-height":P+"px",...e.style}})})});y.displayName=m;var b="ScrollAreaViewport",x=o.forwardRef((e,t)=>{let{__scopeScrollArea:n,children:i,nonce:a,...l}=e,c=w(b,n),u=o.useRef(null),d=(0,s.s)(t,u,c.onViewportChange);return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:a}),(0,f.jsx)(r.sG.div,{"data-radix-scroll-area-viewport":"",...l,ref:d,style:{overflowX:c.scrollbarXEnabled?"scroll":"hidden",overflowY:c.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,f.jsx)("div",{ref:c.onContentChange,style:{minWidth:"100%",display:"table"},children:i})})]})});x.displayName=b;var C="ScrollAreaScrollbar",S=o.forwardRef((e,t)=>{let{forceMount:n,...r}=e,i=w(C,e.__scopeScrollArea),{onScrollbarXEnabledChange:a,onScrollbarYEnabledChange:s}=i,l="horizontal"===e.orientation;return o.useEffect(()=>(l?a(!0):s(!0),()=>{l?a(!1):s(!1)}),[l,a,s]),"hover"===i.type?(0,f.jsx)(T,{...r,ref:t,forceMount:n}):"scroll"===i.type?(0,f.jsx)(P,{...r,ref:t,forceMount:n}):"auto"===i.type?(0,f.jsx)(E,{...r,ref:t,forceMount:n}):"always"===i.type?(0,f.jsx)(k,{...r,ref:t}):null});S.displayName=C;var T=o.forwardRef((e,t)=>{let{forceMount:n,...r}=e,a=w(C,e.__scopeScrollArea),[s,l]=o.useState(!1);return o.useEffect(()=>{let e=a.scrollArea,t=0;if(e){let n=()=>{window.clearTimeout(t),l(!0)},o=()=>{t=window.setTimeout(()=>l(!1),a.scrollHideDelay)};return e.addEventListener("pointerenter",n),e.addEventListener("pointerleave",o),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",n),e.removeEventListener("pointerleave",o)}}},[a.scrollArea,a.scrollHideDelay]),(0,f.jsx)(i.C,{present:n||s,children:(0,f.jsx)(E,{"data-state":s?"visible":"hidden",...r,ref:t})})}),P=o.forwardRef((e,t)=>{var n;let{forceMount:r,...a}=e,s=w(C,e.__scopeScrollArea),l="horizontal"===e.orientation,c=K(()=>d("SCROLL_END"),100),[u,d]=(n={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},o.useReducer((e,t)=>n[e][t]??e,"hidden"));return o.useEffect(()=>{if("idle"===u){let e=window.setTimeout(()=>d("HIDE"),s.scrollHideDelay);return()=>window.clearTimeout(e)}},[u,s.scrollHideDelay,d]),o.useEffect(()=>{let e=s.viewport,t=l?"scrollLeft":"scrollTop";if(e){let n=e[t],o=()=>{let o=e[t];n!==o&&(d("SCROLL"),c()),n=o};return e.addEventListener("scroll",o),()=>e.removeEventListener("scroll",o)}},[s.viewport,l,d,c]),(0,f.jsx)(i.C,{present:r||"hidden"!==u,children:(0,f.jsx)(k,{"data-state":"hidden"===u?"hidden":"visible",...a,ref:t,onPointerEnter:(0,p.m)(e.onPointerEnter,()=>d("POINTER_ENTER")),onPointerLeave:(0,p.m)(e.onPointerLeave,()=>d("POINTER_LEAVE"))})})}),E=o.forwardRef((e,t)=>{let n=w(C,e.__scopeScrollArea),{forceMount:r,...a}=e,[s,l]=o.useState(!1),c="horizontal"===e.orientation,u=K(()=>{if(n.viewport){let e=n.viewport.offsetWidth<n.viewport.scrollWidth,t=n.viewport.offsetHeight<n.viewport.scrollHeight;l(c?e:t)}},10);return Z(n.viewport,u),Z(n.content,u),(0,f.jsx)(i.C,{present:r||s,children:(0,f.jsx)(k,{"data-state":s?"visible":"hidden",...a,ref:t})})}),k=o.forwardRef((e,t)=>{let{orientation:n="vertical",...r}=e,i=w(C,e.__scopeScrollArea),a=o.useRef(null),s=o.useRef(0),[l,c]=o.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),u=_(l.viewport,l.content),d={...r,sizes:l,onSizesChange:c,hasThumb:!!(u>0&&u<1),onThumbChange:e=>a.current=e,onThumbPointerUp:()=>s.current=0,onThumbPointerDown:e=>s.current=e};function p(e,t){return function(e,t,n,o="ltr"){let r=M(n),i=t||r/2,a=n.scrollbar.paddingStart+i,s=n.scrollbar.size-n.scrollbar.paddingEnd-(r-i),l=n.content-n.viewport;return H([a,s],"ltr"===o?[0,l]:[-1*l,0])(e)}(e,s.current,l,t)}return"horizontal"===n?(0,f.jsx)(R,{...d,ref:t,onThumbPositionChange:()=>{if(i.viewport&&a.current){let e=B(i.viewport.scrollLeft,l,i.dir);a.current.style.transform=`translate3d(${e}px, 0, 0)`}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollLeft=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollLeft=p(e,i.dir))}}):"vertical"===n?(0,f.jsx)(D,{...d,ref:t,onThumbPositionChange:()=>{if(i.viewport&&a.current){let e=B(i.viewport.scrollTop,l);a.current.style.transform=`translate3d(0, ${e}px, 0)`}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollTop=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollTop=p(e))}}):null}),R=o.forwardRef((e,t)=>{let{sizes:n,onSizesChange:r,...i}=e,a=w(C,e.__scopeScrollArea),[l,c]=o.useState(),u=o.useRef(null),d=(0,s.s)(t,u,a.onScrollbarXChange);return o.useEffect(()=>{u.current&&c(getComputedStyle(u.current))},[u]),(0,f.jsx)(L,{"data-orientation":"horizontal",...i,ref:d,sizes:n,style:{bottom:0,left:"rtl"===a.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===a.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":M(n)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,n)=>{if(a.viewport){let o=a.viewport.scrollLeft+t.deltaX;e.onWheelScroll(o),function(e,t){return e>0&&e<t}(o,n)&&t.preventDefault()}},onResize:()=>{u.current&&a.viewport&&l&&r({content:a.viewport.scrollWidth,viewport:a.viewport.offsetWidth,scrollbar:{size:u.current.clientWidth,paddingStart:W(l.paddingLeft),paddingEnd:W(l.paddingRight)}})}})}),D=o.forwardRef((e,t)=>{let{sizes:n,onSizesChange:r,...i}=e,a=w(C,e.__scopeScrollArea),[l,c]=o.useState(),u=o.useRef(null),d=(0,s.s)(t,u,a.onScrollbarYChange);return o.useEffect(()=>{u.current&&c(getComputedStyle(u.current))},[u]),(0,f.jsx)(L,{"data-orientation":"vertical",...i,ref:d,sizes:n,style:{top:0,right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":M(n)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,n)=>{if(a.viewport){let o=a.viewport.scrollTop+t.deltaY;e.onWheelScroll(o),function(e,t){return e>0&&e<t}(o,n)&&t.preventDefault()}},onResize:()=>{u.current&&a.viewport&&l&&r({content:a.viewport.scrollHeight,viewport:a.viewport.offsetHeight,scrollbar:{size:u.current.clientHeight,paddingStart:W(l.paddingTop),paddingEnd:W(l.paddingBottom)}})}})}),[Y,A]=h(C),L=o.forwardRef((e,t)=>{let{__scopeScrollArea:n,sizes:i,hasThumb:a,onThumbChange:c,onThumbPointerUp:u,onThumbPointerDown:d,onThumbPositionChange:m,onDragScroll:h,onWheelScroll:v,onResize:g,...y}=e,b=w(C,n),[x,S]=o.useState(null),T=(0,s.s)(t,e=>S(e)),P=o.useRef(null),E=o.useRef(""),k=b.viewport,R=i.content-i.viewport,D=(0,l.c)(v),A=(0,l.c)(m),L=K(g,10);function N(e){P.current&&h({x:e.clientX-P.current.left,y:e.clientY-P.current.top})}return o.useEffect(()=>{let e=e=>{let t=e.target;x?.contains(t)&&D(e,R)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[k,x,R,D]),o.useEffect(A,[i,A]),Z(x,L),Z(b.content,L),(0,f.jsx)(Y,{scope:n,scrollbar:x,hasThumb:a,onThumbChange:(0,l.c)(c),onThumbPointerUp:(0,l.c)(u),onThumbPositionChange:A,onThumbPointerDown:(0,l.c)(d),children:(0,f.jsx)(r.sG.div,{...y,ref:T,style:{position:"absolute",...y.style},onPointerDown:(0,p.m)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),P.current=x.getBoundingClientRect(),E.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",b.viewport&&(b.viewport.style.scrollBehavior="auto"),N(e))}),onPointerMove:(0,p.m)(e.onPointerMove,N),onPointerUp:(0,p.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=E.current,b.viewport&&(b.viewport.style.scrollBehavior=""),P.current=null})})})}),N="ScrollAreaThumb",X=o.forwardRef((e,t)=>{let{forceMount:n,...o}=e,r=A(N,e.__scopeScrollArea);return(0,f.jsx)(i.C,{present:n||r.hasThumb,children:(0,f.jsx)(j,{ref:t,...o})})}),j=o.forwardRef((e,t)=>{let{__scopeScrollArea:n,style:i,...a}=e,l=w(N,n),c=A(N,n),{onThumbPositionChange:u}=c,d=(0,s.s)(t,e=>c.onThumbChange(e)),m=o.useRef(void 0),h=K(()=>{m.current&&(m.current(),m.current=void 0)},100);return o.useEffect(()=>{let e=l.viewport;if(e){let t=()=>{h(),m.current||(m.current=F(e,u),u())};return u(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[l.viewport,h,u]),(0,f.jsx)(r.sG.div,{"data-state":c.hasThumb?"visible":"hidden",...a,ref:d,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...i},onPointerDownCapture:(0,p.m)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),n=e.clientX-t.left,o=e.clientY-t.top;c.onThumbPointerDown({x:n,y:o})}),onPointerUp:(0,p.m)(e.onPointerUp,c.onThumbPointerUp)})});X.displayName=N;var O="ScrollAreaCorner",I=o.forwardRef((e,t)=>{let n=w(O,e.__scopeScrollArea),o=!!(n.scrollbarX&&n.scrollbarY);return"scroll"!==n.type&&o?(0,f.jsx)(z,{...e,ref:t}):null});I.displayName=O;var z=o.forwardRef((e,t)=>{let{__scopeScrollArea:n,...i}=e,a=w(O,n),[s,l]=o.useState(0),[c,u]=o.useState(0),d=!!(s&&c);return Z(a.scrollbarX,()=>{let e=a.scrollbarX?.offsetHeight||0;a.onCornerHeightChange(e),u(e)}),Z(a.scrollbarY,()=>{let e=a.scrollbarY?.offsetWidth||0;a.onCornerWidthChange(e),l(e)}),d?(0,f.jsx)(r.sG.div,{...i,ref:t,style:{width:s,height:c,position:"absolute",right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:0,...e.style}}):null});function W(e){return e?parseInt(e,10):0}function _(e,t){let n=e/t;return isNaN(n)?0:n}function M(e){let t=_(e.viewport,e.content),n=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-n)*t,18)}function B(e,t,n="ltr"){let o=M(t),r=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,i=t.scrollbar.size-r,a=t.content-t.viewport,s=(0,d.q)(e,"ltr"===n?[0,a]:[-1*a,0]);return H([0,a],[0,i-o])(s)}function H(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let o=(t[1]-t[0])/(e[1]-e[0]);return t[0]+o*(n-e[0])}}var F=(e,t=()=>{})=>{let n={left:e.scrollLeft,top:e.scrollTop},o=0;return function r(){let i={left:e.scrollLeft,top:e.scrollTop},a=n.left!==i.left,s=n.top!==i.top;(a||s)&&t(),n=i,o=window.requestAnimationFrame(r)}(),()=>window.cancelAnimationFrame(o)};function K(e,t){let n=(0,l.c)(e),r=o.useRef(0);return o.useEffect(()=>()=>window.clearTimeout(r.current),[]),o.useCallback(()=>{window.clearTimeout(r.current),r.current=window.setTimeout(n,t)},[n,t])}function Z(e,t){let n=(0,l.c)(t);(0,u.N)(()=>{let t=0;if(e){let o=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(n)});return o.observe(e),()=>{window.cancelAnimationFrame(t),o.unobserve(e)}}},[e,n])}var q=y,U=x,G=I},69961:(e,t,n)=>{n.d(t,{A:()=>o});let o=(0,n(22752).A)("ZoomIn",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]])},79252:(e,t,n)=>{n.d(t,{A:()=>o});let o=(0,n(22752).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},82506:(e,t,n)=>{n.d(t,{GT:()=>eb,WZ:()=>eS});var o=n(13072),r=n.n(o),i=function(e,t){return Number(e.toFixed(t))},a=function(e,t,n){n&&"function"==typeof n&&n(e,t)},s={easeOut:function(e){return-Math.cos(e*Math.PI)/2+.5},linear:function(e){return e},easeInQuad:function(e){return e*e},easeOutQuad:function(e){return e*(2-e)},easeInOutQuad:function(e){return e<.5?2*e*e:-1+(4-2*e)*e},easeInCubic:function(e){return e*e*e},easeOutCubic:function(e){return--e*e*e+1},easeInOutCubic:function(e){return e<.5?4*e*e*e:(e-1)*(2*e-2)*(2*e-2)+1},easeInQuart:function(e){return e*e*e*e},easeOutQuart:function(e){return 1- --e*e*e*e},easeInOutQuart:function(e){return e<.5?8*e*e*e*e:1-8*--e*e*e*e},easeInQuint:function(e){return e*e*e*e*e},easeOutQuint:function(e){return 1+--e*e*e*e*e},easeInOutQuint:function(e){return e<.5?16*e*e*e*e*e:1+16*--e*e*e*e*e}},l=function(e){"number"==typeof e&&cancelAnimationFrame(e)},c=function(e){e.mounted&&(l(e.animation),e.animate=!1,e.animation=null,e.velocity=null)};function u(e,t,n,o){if(e.mounted){var r=new Date().getTime();c(e),e.animation=function(){if(!e.mounted)return l(e.animation);var i=new Date().getTime()-r,a=(0,s[t])(i/n);i>=n?(o(1),e.animation=null):e.animation&&(o(a),requestAnimationFrame(e.animation))},requestAnimationFrame(e.animation)}}function d(e,t,n,o){var r,i,a,s=(r=t.scale,i=t.positionX,a=t.positionY,!(Number.isNaN(r)||Number.isNaN(i)||Number.isNaN(a)));if(e.mounted&&s){var l=e.setTransformState,c=e.transformState,d=c.scale,p=c.positionX,f=c.positionY,m=t.scale-d,h=t.positionX-p,v=t.positionY-f;0===n?l(t.scale,t.positionX,t.positionY):u(e,o,n,function(e){l(d+m*e,p+h*e,f+v*e)})}}var p=function(e,t,n,o,r,i,a){var s=e>t?n*(a?1:.5):0,l=o>r?i*(a?1:.5):0;return{minPositionX:e-t-s,maxPositionX:s,minPositionY:o-r-l,maxPositionY:l}},f=function(e,t){var n,o,r,i,a,s,l=e.wrapperComponent,c=e.contentComponent,u=e.setup.centerZoomedOut;if(!l||!c)throw Error("Components are not mounted");var d=(n=l.offsetWidth,o=l.offsetHeight,r=c.offsetWidth,i=c.offsetHeight,{wrapperWidth:n,wrapperHeight:o,newContentWidth:a=r*t,newDiffWidth:n-a,newContentHeight:s=i*t,newDiffHeight:o-s}),f=d.wrapperWidth,m=d.wrapperHeight;return p(f,d.newContentWidth,d.newDiffWidth,m,d.newContentHeight,d.newDiffHeight,!!u)},m=function(e,t,n,o){return o?e<t?i(t,2):e>n?i(n,2):i(e,2):i(e,2)},h=function(e,t){var n=f(e,t);return e.bounds=n,n};function v(e,t,n,o,r,i,a){var s=n.minPositionX,l=n.minPositionY,c=n.maxPositionX,u=n.maxPositionY,d=0,p=0;return a&&(d=r,p=i),{x:m(e,s-d,c+d,o),y:m(t,l-p,u+p,o)}}function g(e,t,n,o,r,i){var a=e.transformState,s=a.scale,l=a.positionX,c=a.positionY,u=o-s;return"number"!=typeof t||"number"!=typeof n?(console.error("Mouse X and Y position were not provided!"),{x:l,y:c}):v(l-t*u,c-n*u,r,i,0,0,null)}function w(e,t,n,o,r){var i=t-(r?o:0);return!Number.isNaN(n)&&e>=n?n:!Number.isNaN(t)&&e<=i?i:e}var y=function(e,t){var n=e.setup.panning.excluded,o=e.isInitialized,r=e.wrapperComponent,i=t.target,a="shadowRoot"in i&&"composedPath"in t?t.composedPath().some(function(e){return e instanceof Element&&(null==r?void 0:r.contains(e))}):null==r?void 0:r.contains(i);return!(!(o&&i&&a)||V(i,n))},b=function(e){var t=e.isInitialized,n=e.isPanning,o=e.setup.panning.disabled;return!!t&&!!n&&!o},x=function(e,t){var n=e.transformState,o=n.positionX,r=n.positionY;e.isPanning=!0,e.startCoords={x:t.clientX-o,y:t.clientY-r}},C=function(e,t){var n=t.touches,o=e.transformState,r=o.positionX,i=o.positionY;e.isPanning=!0,1===n.length&&(e.startCoords={x:n[0].clientX-r,y:n[0].clientY-i})};function S(e,t,n,o,r){var i=e.setup.limitToBounds,a=e.wrapperComponent,s=e.bounds,l=e.transformState,c=l.scale,u=l.positionX,d=l.positionY;if(null!==a&&null!==s&&(t!==u||n!==d)){var p=v(t,n,s,i,o,r,a),f=p.x,m=p.y;e.setTransformState(c,f,m)}}var T=function(e,t,n){var o=e.startCoords,r=e.transformState,i=e.setup.panning,a=i.lockAxisX,s=i.lockAxisY,l=r.positionX,c=r.positionY;if(!o)return{x:l,y:c};var u=t-o.x,d=n-o.y;return{x:a?l:u,y:s?c:d}},P=function(e,t){var n=e.setup,o=e.transformState.scale,r=n.minScale,i=n.disablePadding;return t>0&&o>=r&&!i?t:0},E=function(e){var t=e.mounted,n=e.setup,o=n.disabled,r=n.velocityAnimation,i=e.transformState.scale;return!r.disabled||!!(i>1)||!o||!!t},k=function(e){var t=e.mounted,n=e.velocity,o=e.bounds,r=e.setup,i=r.disabled,a=r.velocityAnimation,s=e.transformState.scale;return(!a.disabled||!!(s>1)||!i||!!t)&&!!n&&!!o};function R(e,t,n,o,r,i,a,s,l,c){if(r){if(t>a&&n>a){var u=a+(e-a)*c;return u>l?l:u<a?a:u}if(t<i&&n<i){var u=i+(e-i)*c;return u<s?s:u>i?i:u}}return o?t:m(e,i,a,r)}function D(e,t){var n=e.transformState.scale;c(e),h(e,n),void 0!==window.TouchEvent&&t instanceof TouchEvent?C(e,t):x(e,t)}function Y(e,t){var n=e.transformState.scale,o=e.setup,r=o.minScale,i=o.alignmentAnimation,a=i.disabled,s=i.sizeX,l=i.sizeY,c=i.animationTime,u=i.animationType;if(!(a||n<r||!s&&!l)){var p=function(e){var t=e.transformState,n=t.positionX,o=t.positionY,r=t.scale,i=e.setup,a=i.disabled,s=i.limitToBounds,l=i.centerZoomedOut,c=e.wrapperComponent;if(!a&&c&&e.bounds){var u=e.bounds,d=u.maxPositionX,p=u.minPositionX,f=u.maxPositionY,m=u.minPositionY,h=n>d?c.offsetWidth:e.setup.minPositionX||0,v=o>f?c.offsetHeight:e.setup.minPositionY||0,w=g(e,h,v,r,e.bounds,s||l),y=w.x,b=w.y;return{scale:r,positionX:n>d||n<p?y:n,positionY:o>f||o<m?b:o}}}(e);p&&d(e,p,null!=t?t:c,u)}}function A(e,t,n){var o=e.startCoords,r=e.setup.alignmentAnimation,i=r.sizeX,a=r.sizeY;if(o){var s=T(e,t,n),l=s.x,c=s.y,u=P(e,i),d=P(e,a);(function(e,t){if(E(e)){var n=e.lastMousePosition,o=e.velocityTime,r=e.setup,i=e.wrapperComponent,a=r.velocityAnimation.equalToMove,s=Date.now();if(n&&o&&i){var l=a?Math.min(1,i.offsetWidth/window.innerWidth):1,c=t.x-n.x,u=t.y-n.y,d=Math.sqrt(c*c+u*u)/(s-o);e.velocity={velocityX:c/l,velocityY:u/l,total:d}}e.lastMousePosition=t,e.velocityTime=s}})(e,{x:l,y:c}),S(e,l,c,u,d)}}function L(e,t,n,o){var r=e.setup,a=r.minScale,s=r.maxScale,l=r.limitToBounds,c=w(i(t,2),a,s,0,!1),u=h(e,c),d=g(e,n,o,c,u,l);return{scale:c,positionX:d.x,positionY:d.y}}function N(e,t,n){var o=e.transformState.scale,r=e.wrapperComponent,i=e.setup,a=i.minScale,s=i.limitToBounds,l=i.zoomAnimation,c=l.disabled,u=l.animationTime,p=l.animationType,f=c||o>=a;if((o>=1||s)&&Y(e),!f&&r&&e.mounted){var m=L(e,a,t||r.offsetWidth/2,n||r.offsetHeight/2);m&&d(e,m,u,p)}}var X=function(){return(X=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)};function j(e,t,n){if(n||2==arguments.length)for(var o,r=0,i=t.length;r<i;r++)!o&&r in t||(o||(o=Array.prototype.slice.call(t,0,r)),o[r]=t[r]);return e.concat(o||Array.prototype.slice.call(t))}"function"==typeof SuppressedError&&SuppressedError;var O={scale:1,positionX:0,positionY:0},I={disabled:!1,minPositionX:null,maxPositionX:null,minPositionY:null,maxPositionY:null,minScale:1,maxScale:8,limitToBounds:!0,centerZoomedOut:!1,centerOnInit:!1,disablePadding:!1,smooth:!0,wheel:{step:.2,disabled:!1,smoothStep:.001,wheelDisabled:!1,touchPadDisabled:!1,activationKeys:[],excluded:[]},panning:{disabled:!1,velocityDisabled:!1,lockAxisX:!1,lockAxisY:!1,allowLeftClickPan:!0,allowMiddleClickPan:!0,allowRightClickPan:!0,wheelPanning:!1,activationKeys:[],excluded:[]},pinch:{step:5,disabled:!1,excluded:[]},doubleClick:{disabled:!1,step:.7,mode:"zoomIn",animationType:"easeOut",animationTime:200,excluded:[]},zoomAnimation:{disabled:!1,size:.4,animationTime:200,animationType:"easeOut"},alignmentAnimation:{disabled:!1,sizeX:100,sizeY:100,animationTime:200,velocityAlignmentTime:400,animationType:"easeOut"},velocityAnimation:{disabled:!1,sensitivity:1,animationTime:400,animationType:"easeOut",equalToMove:!0}},z={wrapperClass:"react-transform-wrapper",contentClass:"react-transform-component"},W=function(e){var t,n,o,r;return{previousScale:null!==(t=e.initialScale)&&void 0!==t?t:O.scale,scale:null!==(n=e.initialScale)&&void 0!==n?n:O.scale,positionX:null!==(o=e.initialPositionX)&&void 0!==o?o:O.positionX,positionY:null!==(r=e.initialPositionY)&&void 0!==r?r:O.positionY}},_=function(e){var t=X({},I);return Object.keys(e).forEach(function(n){var o=void 0!==e[n];if(void 0!==I[n]&&o){var r=Object.prototype.toString.call(I[n]);"[object Object]"===r?t[n]=X(X({},I[n]),e[n]):"[object Array]"===r?t[n]=j(j([],I[n],!0),e[n],!0):t[n]=e[n]}}),t},M=function(e,t,n){var o=e.transformState.scale,r=e.wrapperComponent,a=e.setup,s=a.maxScale,l=a.minScale,c=a.zoomAnimation,u=a.smooth,d=c.size;if(!r)throw Error("Wrapper is not mounted");return w(i(u?o*Math.exp(t*n):o+t*n,3),l,s,d,!1)};function B(e,t,n,o,r){var i=e.wrapperComponent,a=e.transformState,s=a.scale,l=a.positionX,c=a.positionY;if(!i)return console.error("No WrapperComponent found");var u=i.offsetWidth,p=i.offsetHeight,f=M(e,t,n),m=L(e,f,(u/2-l)/s,(p/2-c)/s);if(!m)return console.error("Error during zoom event. New transformation state was not calculated.");d(e,m,o,r)}function H(e,t,n,o){var r=e.setup,i=e.wrapperComponent,a=r.limitToBounds,s=W(e.props),l=e.transformState,c=l.scale,u=l.positionX,p=l.positionY;if(i){var m=f(e,s.scale),h=v(s.positionX,s.positionY,m,a,0,0,i),g={scale:s.scale,positionX:h.x,positionY:h.y};if(c===s.scale&&u===s.positionX&&p===s.positionY)return;null==o||o(),d(e,g,t,n)}}var F=function(e){return{instance:e,zoomIn:function(t,n,o){void 0===t&&(t=.5),void 0===n&&(n=300),void 0===o&&(o="easeOut"),B(e,1,t,n,o)},zoomOut:function(t,n,o){void 0===t&&(t=.5),void 0===n&&(n=300),void 0===o&&(o="easeOut"),B(e,-1,t,n,o)},setTransform:function(t,n,o,r,i){void 0===r&&(r=300),void 0===i&&(i="easeOut");var a=e.transformState,s=a.positionX,l=a.positionY,c=a.scale,u=e.wrapperComponent,p=e.contentComponent;!e.setup.disabled&&u&&p&&d(e,{positionX:Number.isNaN(t)?s:t,positionY:Number.isNaN(n)?l:n,scale:Number.isNaN(o)?c:o},r,i)},resetTransform:function(t,n){void 0===t&&(t=200),void 0===n&&(n="easeOut"),H(e,t,n)},centerView:function(t,n,o){void 0===n&&(n=200),void 0===o&&(o="easeOut");var r=e.transformState,i=e.wrapperComponent,a=e.contentComponent;i&&a&&d(e,$(t||r.scale,i,a),n,o)},zoomToElement:function(t,n,o,r){void 0===o&&(o=600),void 0===r&&(r="easeOut"),c(e);var i=e.wrapperComponent,a="string"==typeof t?document.getElementById(t):t;if(i&&a&&i.contains(a)){var s=function(e,t,n){var o,r,i,a,s,l=e.wrapperComponent,c=e.contentComponent,u=e.transformState,d=e.setup,p=d.limitToBounds,m=d.minScale,h=d.maxScale;if(!l||!c)return u;var g=l.getBoundingClientRect(),y=t.getBoundingClientRect(),b=(o=t.getBoundingClientRect(),r=l.getBoundingClientRect(),i=c.getBoundingClientRect(),a=r.x*u.scale,s=r.y*u.scale,{x:(o.x-i.x+a)/u.scale,y:(o.y-i.y+s)/u.scale}),x=b.x,C=b.y,S=y.width/u.scale,T=y.height/u.scale,P=l.offsetWidth/S,E=l.offsetHeight/T,k=w(n||Math.min(P,E),m,h,0,!1),R=(g.width-S*k)/2,D=(g.height-T*k)/2,Y=v((g.left-x)*k+R,(g.top-C)*k+D,f(e,k),p,0,0,l);return{positionX:Y.x,positionY:Y.y,scale:k}}(e,a,n);d(e,s,o,r)}}}},K=function(e){return{instance:e,state:e.transformState}},Z=function(e){var t={};return Object.assign(t,K(e)),Object.assign(t,F(e)),t},q=!1;function U(){try{return{get passive(){return!1}}}catch(e){return!1}}var G=".".concat(z.wrapperClass),V=function(e,t){return t.some(function(t){return e.matches("".concat(G," ").concat(t,", ").concat(G," .").concat(t,", ").concat(G," ").concat(t," *, ").concat(G," .").concat(t," *"))})},Q=function(e){e&&clearTimeout(e)},$=function(e,t,n){var o=n.offsetWidth*e,r=n.offsetHeight*e;return{scale:e,positionX:(t.offsetWidth-o)/2,positionY:(t.offsetHeight-r)/2}},J=function(e,t){var n=e.setup.wheel,o=n.disabled,r=n.wheelDisabled,i=n.touchPadDisabled,a=n.excluded,s=e.isInitialized,l=e.isPanning,c=t.target;return!(!(s&&!l&&!o&&c)||r&&!t.ctrlKey||i&&t.ctrlKey||V(c,a))};function ee(e,t,n){var o=t.getBoundingClientRect(),r=0,i=0;if("clientX"in e)r=(e.clientX-o.left)/n,i=(e.clientY-o.top)/n;else{var a=e.touches[0];r=(a.clientX-o.left)/n,i=(a.clientY-o.top)/n}return(Number.isNaN(r)||Number.isNaN(i))&&console.error("No mouse or touch offset found"),{x:r,y:i}}var et=function(e,t,n,o,r){var a=e.transformState.scale,s=e.wrapperComponent,l=e.setup,c=l.maxScale,u=l.minScale,d=l.zoomAnimation,p=l.disablePadding,f=d.size,m=d.disabled;if(!s)throw Error("Wrapper is not mounted");var h=a+t*n;return r?h:w(i(h,3),u,c,f,!o&&!m&&!p)},en=function(e,t){var n=e.previousWheelEvent,o=e.transformState.scale,r=e.setup,i=r.maxScale,a=r.minScale;return!!n&&(!!(o<i)||!!(o>a)||Math.sign(n.deltaY)!==Math.sign(t.deltaY)||!!(n.deltaY>0)&&!!(n.deltaY<t.deltaY)||!!(n.deltaY<0)&&!!(n.deltaY>t.deltaY)||Math.sign(n.deltaY)!==Math.sign(t.deltaY))},eo=function(e,t){var n=e.setup.pinch,o=n.disabled,r=n.excluded,i=e.isInitialized,a=t.target;return!(!(i&&!o&&a)||V(a,r))},er=function(e){var t=e.setup.pinch.disabled,n=e.isInitialized,o=e.pinchStartDistance;return!!n&&!t&&!!o},ei=function(e,t,n){var o=n.getBoundingClientRect(),r=e.touches,a=i(r[0].clientX-o.left,5),s=i(r[0].clientY-o.top,5);return{x:(a+i(r[1].clientX-o.left,5))/2/t,y:(s+i(r[1].clientY-o.top,5))/2/t}},ea=function(e){return Math.sqrt(Math.pow(e.touches[0].pageX-e.touches[1].pageX,2)+Math.pow(e.touches[0].pageY-e.touches[1].pageY,2))},es=function(e,t){var n=e.pinchStartScale,o=e.pinchStartDistance,r=e.setup,a=r.maxScale,s=r.minScale,l=r.zoomAnimation,c=r.disablePadding,u=l.size,d=l.disabled;if(!n||null===o||!t)throw Error("Pinch touches distance was not provided");return t<0?e.transformState.scale:w(i(t/o*n,2),s,a,u,!d&&!c)},el=function(e,t){var n=e.props,o=n.onWheelStart,r=n.onZoomStart;e.wheelStopEventTimer||(c(e),a(Z(e),t,o),a(Z(e),t,r))},ec=function(e,t){var n,o=e.props,r=o.onWheel,i=o.onZoom,s=e.contentComponent,l=e.setup,c=e.transformState.scale,u=l.limitToBounds,d=l.centerZoomedOut,p=l.zoomAnimation,f=l.wheel,m=l.disablePadding,v=l.smooth,w=p.size,y=p.disabled,b=f.step,x=f.smoothStep;if(!s)throw Error("Component not mounted");t.preventDefault(),t.stopPropagation();var C=et(e,(n=t?t.deltaY<0?1:-1:0,n),v?x*Math.abs(t.deltaY):b,!t.ctrlKey);if(c!==C){var S=h(e,C),T=ee(t,s,c),P=g(e,T.x,T.y,C,S,u&&(y||0===w||d||m)),E=P.x,k=P.y;e.previousWheelEvent=t,e.setTransformState(C,E,k),a(Z(e),t,r),a(Z(e),t,i)}},eu=function(e,t){var n=e.props,o=n.onWheelStop,r=n.onZoomStop;Q(e.wheelAnimationTimer),e.wheelAnimationTimer=setTimeout(function(){e.mounted&&(N(e,t.x,t.y),e.wheelAnimationTimer=null)},100),en(e,t)&&(Q(e.wheelStopEventTimer),e.wheelStopEventTimer=setTimeout(function(){e.mounted&&(e.wheelStopEventTimer=null,a(Z(e),t,o),a(Z(e),t,r))},160))},ed=function(e){for(var t=0,n=0,o=0;o<2;o+=1)t+=e.touches[o].clientX,n+=e.touches[o].clientY;return{x:t/2,y:n/2}},ep=function(e,t){var n=ea(t);e.pinchStartDistance=n,e.lastDistance=n,e.pinchStartScale=e.transformState.scale,e.isPanning=!1;var o=ed(t);e.pinchLastCenterX=o.x,e.pinchLastCenterY=o.y,c(e)},ef=function(e,t){var n=e.contentComponent,o=e.pinchStartDistance,r=e.wrapperComponent,i=e.transformState.scale,a=e.setup,s=a.limitToBounds,l=a.centerZoomedOut,c=a.zoomAnimation,u=a.alignmentAnimation,d=c.disabled,p=c.size;if(null!==o&&n){var f=ei(t,i,n);if(Number.isFinite(f.x)&&Number.isFinite(f.y)){var m=ea(t),w=es(e,m),y=ed(t),b=y.x-(e.pinchLastCenterX||0),x=y.y-(e.pinchLastCenterY||0);if(w!==i||0!==b||0!==x){e.pinchLastCenterX=y.x,e.pinchLastCenterY=y.y;var C=h(e,w),S=g(e,f.x,f.y,w,C,s&&(d||0===p||l)),T=S.x,E=S.y;e.pinchMidpoint=f,e.lastDistance=m;var k=u.sizeX,R=u.sizeY,D=v(T+b,E+x,C,s,P(e,k),P(e,R),r),Y=D.x,A=D.y;e.setTransformState(w,Y,A)}}}},em=function(e){var t=e.pinchMidpoint;e.velocity=null,e.lastDistance=null,e.pinchMidpoint=null,e.pinchStartScale=null,e.pinchStartDistance=null,N(e,null==t?void 0:t.x,null==t?void 0:t.y)},eh=function(e,t){var n=e.props.onZoomStop,o=e.setup.doubleClick.animationTime;Q(e.doubleClickStopEventTimer),e.doubleClickStopEventTimer=setTimeout(function(){e.doubleClickStopEventTimer=null,a(Z(e),t,n)},o)},ev=function(e,t){var n=e.props,o=n.onZoomStart,r=n.onZoom,i=e.setup.doubleClick,s=i.animationTime,l=i.animationType;a(Z(e),t,o),H(e,s,l,function(){return a(Z(e),t,r)}),eh(e,t)},eg=function(e,t){var n=e.isInitialized,o=e.setup,r=e.wrapperComponent,i=o.doubleClick,a=i.disabled,s=i.excluded,l=t.target,c=null==r?void 0:r.contains(l);return!(!(n&&l&&c&&!a)||V(l,s))},ew=function(e){var t=this;this.mounted=!0,this.pinchLastCenterX=null,this.pinchLastCenterY=null,this.onChangeCallbacks=new Set,this.onInitCallbacks=new Set,this.wrapperComponent=null,this.contentComponent=null,this.isInitialized=!1,this.bounds=null,this.previousWheelEvent=null,this.wheelStopEventTimer=null,this.wheelAnimationTimer=null,this.isPanning=!1,this.isWheelPanning=!1,this.startCoords=null,this.lastTouch=null,this.distance=null,this.lastDistance=null,this.pinchStartDistance=null,this.pinchStartScale=null,this.pinchMidpoint=null,this.doubleClickStopEventTimer=null,this.velocity=null,this.velocityTime=null,this.lastMousePosition=null,this.animate=!1,this.animation=null,this.maxBounds=null,this.pressedKeys={},this.mount=function(){t.initializeWindowEvents()},this.unmount=function(){t.cleanupWindowEvents()},this.update=function(e){t.props=e,h(t,t.transformState.scale),t.setup=_(e)},this.initializeWindowEvents=function(){var e,n,o=U(),r=null===(e=t.wrapperComponent)||void 0===e?void 0:e.ownerDocument,i=null==r?void 0:r.defaultView;null===(n=t.wrapperComponent)||void 0===n||n.addEventListener("wheel",t.onWheelPanning,o),null==i||i.addEventListener("mousedown",t.onPanningStart,o),null==i||i.addEventListener("mousemove",t.onPanning,o),null==i||i.addEventListener("mouseup",t.onPanningStop,o),null==r||r.addEventListener("mouseleave",t.clearPanning,o),null==i||i.addEventListener("keyup",t.setKeyUnPressed,o),null==i||i.addEventListener("keydown",t.setKeyPressed,o)},this.cleanupWindowEvents=function(){var e,n,o=U(),r=null===(e=t.wrapperComponent)||void 0===e?void 0:e.ownerDocument,i=null==r?void 0:r.defaultView;null==i||i.removeEventListener("mousedown",t.onPanningStart,o),null==i||i.removeEventListener("mousemove",t.onPanning,o),null==i||i.removeEventListener("mouseup",t.onPanningStop,o),null==r||r.removeEventListener("mouseleave",t.clearPanning,o),null==i||i.removeEventListener("keyup",t.setKeyUnPressed,o),null==i||i.removeEventListener("keydown",t.setKeyPressed,o),document.removeEventListener("mouseleave",t.clearPanning,o),c(t),null===(n=t.observer)||void 0===n||n.disconnect()},this.handleInitializeWrapperEvents=function(e){var n=U();e.addEventListener("wheel",t.onWheelZoom,n),e.addEventListener("dblclick",t.onDoubleClick,n),e.addEventListener("touchstart",t.onTouchPanningStart,n),e.addEventListener("touchmove",t.onTouchPanning,n),e.addEventListener("touchend",t.onTouchPanningStop,n)},this.handleInitialize=function(e,n){var o=!1,r=t.setup.centerOnInit,i=function(e,t){for(var n=0;n<e.length;n++)if(e[n].target===t)return!0;return!1};t.applyTransformation(),t.onInitCallbacks.forEach(function(e){e(Z(t))}),t.observer=new ResizeObserver(function(a){if(i(a,e)||i(a,n)){if(r&&!o){var s=n.offsetWidth,l=n.offsetHeight;(s>0||l>0)&&(o=!0,t.setCenter())}else c(t),h(t,t.transformState.scale),Y(t,0)}}),t.observer.observe(e),t.observer.observe(n)},this.onWheelZoom=function(e){!t.setup.disabled&&J(t,e)&&t.isPressingKeys(t.setup.wheel.activationKeys)&&(el(t,e),ec(t,e),eu(t,e))},this.onWheelPanning=function(e){var n=t.setup,o=n.disabled,r=n.wheel,i=n.panning;if(t.wrapperComponent&&t.contentComponent&&!o&&r.wheelDisabled&&!i.disabled&&i.wheelPanning&&!e.ctrlKey){e.preventDefault(),e.stopPropagation();var a=t.transformState,s=a.positionX,l=a.positionY,c=s-e.deltaX,u=l-e.deltaY,d=i.lockAxisX?s:c,p=i.lockAxisY?l:u,f=t.setup.alignmentAnimation,m=f.sizeX,h=f.sizeY,v=P(t,m),g=P(t,h);(d!==s||p!==l)&&S(t,d,p,v,g)}},this.onPanningStart=function(e){var n=t.setup.disabled,o=t.props.onPanningStart;!n&&y(t,e)&&t.isPressingKeys(t.setup.panning.activationKeys)&&(0!==e.button||t.setup.panning.allowLeftClickPan)&&(1!==e.button||t.setup.panning.allowMiddleClickPan)&&(2!==e.button||t.setup.panning.allowRightClickPan)&&(e.preventDefault(),e.stopPropagation(),c(t),D(t,e),a(Z(t),e,o))},this.onPanning=function(e){var n=t.setup.disabled,o=t.props.onPanning;!n&&b(t)&&t.isPressingKeys(t.setup.panning.activationKeys)&&(e.preventDefault(),e.stopPropagation(),A(t,e.clientX,e.clientY),a(Z(t),e,o))},this.onPanningStop=function(e){var n=t.props.onPanningStop;t.isPanning&&(function(e){if(e.isPanning){var t=e.setup.panning.velocityDisabled,n=e.velocity,o=e.wrapperComponent,r=e.contentComponent;e.isPanning=!1,e.animate=!1,e.animation=null;var i=null==o?void 0:o.getBoundingClientRect(),a=null==r?void 0:r.getBoundingClientRect(),l=(null==i?void 0:i.width)||0,c=(null==i?void 0:i.height)||0,d=(null==a?void 0:a.width)||0,p=(null==a?void 0:a.height)||0;!t&&n&&(null==n?void 0:n.total)>.1&&(l<d||c<p)?function(e){var t,n,o,r,i=e.velocity,a=e.bounds,l=e.setup,c=e.wrapperComponent;if(k(e)&&i&&a&&c){var d=i.velocityX,p=i.velocityY,f=i.total,m=a.maxPositionX,h=a.minPositionX,v=a.maxPositionY,g=a.minPositionY,w=l.limitToBounds,y=l.alignmentAnimation,b=l.zoomAnimation,x=l.panning,C=x.lockAxisY,S=x.lockAxisX,T=b.animationType,E=y.sizeX,D=y.sizeY,Y=y.velocityAlignmentTime,A=Math.max((n=(t=e.setup.velocityAnimation).equalToMove,o=t.animationTime,r=t.sensitivity,n?o*f*r:o),Y),L=P(e,E),N=P(e,D),X=L*c.offsetWidth/100,j=N*c.offsetHeight/100,O=m+X,I=h-X,z=v+j,W=g-j,_=e.transformState,M=new Date().getTime();u(e,T,A,function(t){var n=e.transformState,o=n.scale,r=n.positionX,i=n.positionY,a=new Date().getTime()-M,l=1-(0,s[y.animationType])(Math.min(1,a/Y)),c=1-t,u=r+d*c,f=i+p*c,b=R(u,_.positionX,r,S,w,h,m,I,O,l),x=R(f,_.positionY,i,C,w,g,v,W,z,l);(r!==u||i!==f)&&e.setTransformState(o,b,x)})}}(e):Y(e)}}(t),a(Z(t),e,n))},this.onPinchStart=function(e){var n=t.setup.disabled,o=t.props,r=o.onPinchingStart,i=o.onZoomStart;!n&&eo(t,e)&&(ep(t,e),c(t),a(Z(t),e,r),a(Z(t),e,i))},this.onPinch=function(e){var n=t.setup.disabled,o=t.props,r=o.onPinching,i=o.onZoom;!n&&er(t)&&(e.preventDefault(),e.stopPropagation(),ef(t,e),a(Z(t),e,r),a(Z(t),e,i))},this.onPinchStop=function(e){var n=t.props,o=n.onPinchingStop,r=n.onZoomStop;t.pinchStartScale&&(em(t),a(Z(t),e,o),a(Z(t),e,r))},this.onTouchPanningStart=function(e){var n=t.setup.disabled,o=t.props.onPanningStart;if(!n&&y(t,e)&&!(t.lastTouch&&+new Date-t.lastTouch<200&&1===e.touches.length)){t.lastTouch=+new Date,c(t);var r=e.touches,i=1===r.length,s=2===r.length;i&&(c(t),D(t,e),a(Z(t),e,o)),s&&t.onPinchStart(e)}},this.onTouchPanning=function(e){var n=t.setup.disabled,o=t.props.onPanning;if(t.isPanning&&1===e.touches.length){if(n||!b(t))return;e.preventDefault(),e.stopPropagation();var r=e.touches[0];A(t,r.clientX,r.clientY),a(Z(t),e,o)}else e.touches.length>1&&t.onPinch(e)},this.onTouchPanningStop=function(e){t.onPanningStop(e),t.onPinchStop(e)},this.onDoubleClick=function(e){!t.setup.disabled&&eg(t,e)&&function(e,t){var n,o=e.setup,r=e.doubleClickStopEventTimer,i=e.transformState,s=e.contentComponent,l=i.scale,c=e.props,u=c.onZoomStart,p=c.onZoom,f=o.doubleClick,m=f.disabled,h=f.mode,v=f.step,g=f.animationTime,w=f.animationType;if(!m&&!r){if("reset"===h)return ev(e,t);if(!s)return console.error("No ContentComponent found");var y=(n=e.transformState.scale,"toggle"===h?1===n?1:-1:"zoomOut"===h?-1:1),b=M(e,y,v);if(l!==b){a(Z(e),t,u);var x=ee(t,s,l),C=L(e,b,x.x,x.y);if(!C)return console.error("Error during zoom event. New transformation state was not calculated.");a(Z(e),t,p),d(e,C,g,w),eh(e,t)}}}(t,e)},this.clearPanning=function(e){t.isPanning&&t.onPanningStop(e)},this.setKeyPressed=function(e){t.pressedKeys[e.key]=!0},this.setKeyUnPressed=function(e){t.pressedKeys[e.key]=!1},this.isPressingKeys=function(e){return!e.length||!!e.find(function(e){return t.pressedKeys[e]})},this.setTransformState=function(e,n,o){var r=t.props.onTransformed;if(Number.isNaN(e)||Number.isNaN(n)||Number.isNaN(o))console.error("Detected NaN set state values");else{e!==t.transformState.scale&&(t.transformState.previousScale=t.transformState.scale,t.transformState.scale=e),t.transformState.positionX=n,t.transformState.positionY=o,t.applyTransformation();var i=Z(t);t.onChangeCallbacks.forEach(function(e){return e(i)}),a(i,{scale:e,positionX:n,positionY:o},r)}},this.setCenter=function(){if(t.wrapperComponent&&t.contentComponent){var e=$(t.transformState.scale,t.wrapperComponent,t.contentComponent);t.setTransformState(e.scale,e.positionX,e.positionY)}},this.handleTransformStyles=function(e,n,o){return t.props.customTransform?t.props.customTransform(e,n,o):"translate(".concat(e,"px, ").concat(n,"px) scale(").concat(o,")")},this.applyTransformation=function(){if(t.mounted&&t.contentComponent){var e=t.transformState,n=e.scale,o=e.positionX,r=e.positionY,i=t.handleTransformStyles(o,r,n);t.contentComponent.style.transform=i}},this.getContext=function(){return Z(t)},this.onChange=function(e){return t.onChangeCallbacks.has(e)||t.onChangeCallbacks.add(e),function(){t.onChangeCallbacks.delete(e)}},this.onInit=function(e){return t.onInitCallbacks.has(e)||t.onInitCallbacks.add(e),function(){t.onInitCallbacks.delete(e)}},this.init=function(e,n){t.cleanupWindowEvents(),t.wrapperComponent=e,t.contentComponent=n,h(t,t.transformState.scale),t.handleInitializeWrapperEvents(e),t.handleInitialize(e,n),t.initializeWindowEvents(),t.isInitialized=!0,a(Z(t),void 0,t.props.onInit)},this.props=e,this.setup=_(this.props),this.transformState=W(this.props)},ey=r().createContext(null),eb=r().forwardRef(function(e,t){var n,i,a=(0,o.useRef)(new ew(e)).current,s=(n=e.children,i=F(a),"function"==typeof n?n(i):n);return(0,o.useImperativeHandle)(t,function(){return F(a)},[a]),(0,o.useEffect)(function(){a.update(e)},[a,e]),r().createElement(ey.Provider,{value:a},s)});r().forwardRef(function(e,t){var n,i=(0,o.useRef)(null),a=(0,o.useContext)(ey);return(0,o.useEffect)(function(){return a.onChange(function(e){i.current&&(i.current.style.transform=a.handleTransformStyles(0,0,1/e.instance.transformState.scale))})},[a]),r().createElement("div",X({},e,{ref:(n=[i,t],function(e){n.forEach(function(t){"function"==typeof t?t(e):null!=t&&(t.current=e)})})}))});var ex={width:0,height:0,y:0,x:0,top:0,bottom:0,left:0,right:0},eC={wrapper:"transform-component-module_wrapper__SPB86",content:"transform-component-module_content__FBWxo"};!function(e,t){void 0===t&&(t={});var n=t.insertAt;if(e&&"undefined"!=typeof document){var o=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css","top"===n&&o.firstChild?o.insertBefore(r,o.firstChild):o.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}}(".transform-component-module_wrapper__SPB86 {\n  position: relative;\n  width: -moz-fit-content;\n  width: fit-content;\n  height: -moz-fit-content;\n  height: fit-content;\n  overflow: hidden;\n  -webkit-touch-callout: none; /* iOS Safari */\n  -webkit-user-select: none; /* Safari */\n  -khtml-user-select: none; /* Konqueror HTML */\n  -moz-user-select: none; /* Firefox */\n  -ms-user-select: none; /* Internet Explorer/Edge */\n  user-select: none;\n  margin: 0;\n  padding: 0;\n  transform: translate3d(0, 0, 0);\n}\n.transform-component-module_content__FBWxo {\n  display: flex;\n  flex-wrap: wrap;\n  width: -moz-fit-content;\n  width: fit-content;\n  height: -moz-fit-content;\n  height: fit-content;\n  margin: 0;\n  padding: 0;\n  transform-origin: 0% 0%;\n}\n.transform-component-module_content__FBWxo img {\n  pointer-events: none;\n}\n");var eS=function(e){var t=e.children,n=e.wrapperClass,i=e.contentClass,a=e.wrapperStyle,s=e.contentStyle,l=e.wrapperProps,c=e.contentProps,u=(0,o.useContext)(ey),d=u.init,p=u.cleanupWindowEvents,f=(0,o.useRef)(null),m=(0,o.useRef)(null);return(0,o.useEffect)(function(){var e=f.current,t=m.current;return null!==e&&null!==t&&d&&(null==d||d(e,t)),function(){null==p||p()}},[]),r().createElement("div",X({},void 0===l?{}:l,{ref:f,className:"".concat(z.wrapperClass," ").concat(eC.wrapper," ").concat(void 0===n?"":n),style:a}),r().createElement("div",X({},void 0===c?{}:c,{ref:m,className:"".concat(z.contentClass," ").concat(eC.content," ").concat(void 0===i?"":i),style:s}),t))},eT=function(){var e=useContext(ey);if(!e)throw Error("Transform context must be placed inside TransformWrapper");return e}},89509:(e,t,n)=>{n.d(t,{A:()=>o});let o=(0,n(22752).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}};