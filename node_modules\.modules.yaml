hoistPattern:
  - '*'
hoistedDependencies:
  core-util-is@1.0.3:
    core-util-is: private
  immediate@3.0.6:
    immediate: private
  inherits@2.0.4:
    inherits: private
  isarray@1.0.0:
    isarray: private
  lie@3.3.0:
    lie: private
  pako@1.0.11:
    pako: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  readable-stream@2.3.8:
    readable-stream: private
  safe-buffer@5.1.2:
    safe-buffer: private
  setimmediate@1.0.5:
    setimmediate: private
  string_decoder@1.1.1:
    string_decoder: private
  util-deprecate@1.0.2:
    util-deprecate: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.1
pendingBuilds: []
prunedAt: Wed, 11 Jun 2025 06:21:15 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped: []
storeDir: D:\.pnpm-store\v10
virtualStoreDir: D:\pycode\support_chart2\node_modules\.pnpm
virtualStoreDirMaxLength: 60
