"use strict";exports.id=823,exports.ids=[823],exports.modules={1144:(e,t,n)=>{n.d(t,{q:()=>r});function r(e,[t,n]){return Math.min(n,Math.max(t,e))}},25097:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,a={},l=0,c=function(e){return e&&(e.host||c(e.parentNode))},u=function(e,t,n,r){var u=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=c(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var s=a[n],f=[],d=new Set,p=new Set(u),h=function(e){!(!e||d.has(e))&&(d.add(e),h(e.parentNode))};u.forEach(h);var m=function(e){!(!e||p.has(e))&&Array.prototype.forEach.call(e.children,function(e){if(d.has(e))m(e);else try{var t=e.getAttribute(r),a=null!==t&&"false"!==t,l=(o.get(e)||0)+1,c=(s.get(e)||0)+1;o.set(e,l),s.set(e,c),f.push(e),1===l&&a&&i.set(e,!0),1===c&&e.setAttribute(n,"true"),a||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),d.clear(),l++,function(){f.forEach(function(e){var t=o.get(e)-1,a=s.get(e)-1;o.set(e,t),s.set(e,a),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),a||e.removeAttribute(n)}),--l||(o=new WeakMap,o=new WeakMap,i=new WeakMap,a={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live], script"))),u(o,i,n,"aria-hidden")):function(){return null}}},25130:(e,t,n)=>{n.d(t,{bL:()=>E,zi:()=>R});var r=n(13072),o=n(85271),i=n(25182),a=n(5016),l=n(75634),c=n(82785),u=n(56486),s=n(22839),f=n(45781),d="Switch",[p,h]=(0,a.A)(d),[m,v]=p(d),g=r.forwardRef((e,t)=>{let{__scopeSwitch:n,name:a,checked:c,defaultChecked:u,required:d,disabled:p,value:h="on",onCheckedChange:v,form:g,...y}=e,[w,E]=r.useState(null),R=(0,i.s)(t,e=>E(e)),A=r.useRef(!1),C=!w||g||!!w.closest("form"),[S=!1,L]=(0,l.i)({prop:c,defaultProp:u,onChange:v});return(0,f.jsxs)(m,{scope:n,checked:S,disabled:p,children:[(0,f.jsx)(s.sG.button,{type:"button",role:"switch","aria-checked":S,"aria-required":d,"data-state":x(S),"data-disabled":p?"":void 0,disabled:p,value:h,...y,ref:R,onClick:(0,o.m)(e.onClick,e=>{L(e=>!e),C&&(A.current=e.isPropagationStopped(),A.current||e.stopPropagation())})}),C&&(0,f.jsx)(b,{control:w,bubbles:!A.current,name:a,value:h,checked:S,required:d,disabled:p,form:g,style:{transform:"translateX(-100%)"}})]})});g.displayName=d;var y="SwitchThumb",w=r.forwardRef((e,t)=>{let{__scopeSwitch:n,...r}=e,o=v(y,n);return(0,f.jsx)(s.sG.span,{"data-state":x(o.checked),"data-disabled":o.disabled?"":void 0,...r,ref:t})});w.displayName=y;var b=e=>{let{control:t,checked:n,bubbles:o=!0,...i}=e,a=r.useRef(null),l=(0,c.Z)(n),s=(0,u.X)(t);return r.useEffect(()=>{let e=a.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(l!==n&&t){let r=new Event("click",{bubbles:o});t.call(e,n),e.dispatchEvent(r)}},[l,n,o]),(0,f.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:n,...i,tabIndex:-1,ref:a,style:{...e.style,...s,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function x(e){return e?"checked":"unchecked"}var E=g,R=w},28108:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(22752).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},32890:(e,t,n)=>{n.d(t,{b:()=>l,s:()=>a});var r=n(13072),o=n(22839),i=n(45781),a=r.forwardRef((e,t)=>(0,i.jsx)(o.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));a.displayName="VisuallyHidden";var l=a},39365:(e,t,n)=>{n.d(t,{Mz:()=>eZ,i3:()=>eJ,UC:()=>eU,bL:()=>e_,Bk:()=>eM});var r=n(13072);let o=["top","right","bottom","left"],i=Math.min,a=Math.max,l=Math.round,c=Math.floor,u=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},f={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(p(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>f[e])}function w(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function b(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function x(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function E(e,t,n){let r,{reference:o,floating:i}=e,a=g(t),l=m(g(t)),c=v(l),u=p(t),s="y"===a,f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,y=o[c]/2-i[c]/2;switch(u){case"top":r={x:f,y:o.y-i.height};break;case"bottom":r={x:f,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-i.width,y:d};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[l]-=y*(n&&s?-1:1);break;case"end":r[l]+=y*(n&&s?-1:1)}return r}let R=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),c=await (null==a.isRTL?void 0:a.isRTL(t)),u=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:f}=E(u,r,c),d=r,p={},h=0;for(let n=0;n<l.length;n++){let{name:i,fn:m}=l[n],{x:v,y:g,data:y,reset:w}=await m({x:s,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:u,platform:a,elements:{reference:e,floating:t}});s=null!=v?v:s,f=null!=g?g:f,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(u=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:f}=E(u,d,c)),n=-1)}return{x:s,y:f,placement:d,strategy:o,middlewareData:p}};async function A(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:c}=e,{boundary:u="clippingAncestors",rootBoundary:s="viewport",elementContext:f="floating",altBoundary:p=!1,padding:h=0}=d(t,e),m=b(h),v=l[p?"floating"===f?"reference":"floating":f],g=x(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(v)))||n?v:v.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:u,rootBoundary:s,strategy:c})),y="floating"===f?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),E=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},R=x(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:y,offsetParent:w,strategy:c}):y);return{top:(g.top-R.top+m.top)/E.y,bottom:(R.bottom-g.bottom+m.bottom)/E.y,left:(g.left-R.left+m.left)/E.x,right:(R.right-g.right+m.right)/E.x}}function C(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function S(e){return o.some(t=>e[t]>=0)}async function L(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=p(n),l=h(n),c="y"===g(n),u=["left","top"].includes(a)?-1:1,s=i&&c?-1:1,f=d(t,e),{mainAxis:m,crossAxis:v,alignmentAxis:y}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return l&&"number"==typeof y&&(v="end"===l?-1*y:y),c?{x:v*s,y:m*u}:{x:m*u,y:v*s}}function T(){return"undefined"!=typeof window}function P(e){return N(e)?(e.nodeName||"").toLowerCase():"#document"}function k(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function O(e){var t;return null==(t=(N(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function N(e){return!!T()&&(e instanceof Node||e instanceof k(e).Node)}function M(e){return!!T()&&(e instanceof Element||e instanceof k(e).Element)}function D(e){return!!T()&&(e instanceof HTMLElement||e instanceof k(e).HTMLElement)}function j(e){return!!T()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof k(e).ShadowRoot)}function W(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=z(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function F(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function H(e){let t=B(),n=M(e)?z(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function B(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function I(e){return["html","body","#document"].includes(P(e))}function z(e){return k(e).getComputedStyle(e)}function $(e){return M(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function X(e){if("html"===P(e))return e;let t=e.assignedSlot||e.parentNode||j(e)&&e.host||O(e);return j(t)?t.host:t}function Y(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=X(t);return I(n)?t.ownerDocument?t.ownerDocument.body:t.body:D(n)&&W(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=k(o);if(i){let e=G(a);return t.concat(a,a.visualViewport||[],W(o)?o:[],e&&n?Y(e):[])}return t.concat(o,Y(o,[],n))}function G(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function V(e){let t=z(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=D(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,c=l(n)!==i||l(r)!==a;return c&&(n=i,r=a),{width:n,height:r,$:c}}function q(e){return M(e)?e:e.contextElement}function K(e){let t=q(e);if(!D(t))return u(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=V(t),a=(i?l(n.width):n.width)/r,c=(i?l(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),c&&Number.isFinite(c)||(c=1),{x:a,y:c}}let _=u(0);function Z(e){let t=k(e);return B()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:_}function U(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=q(e),l=u(1);t&&(r?M(r)&&(l=K(r)):l=K(e));let c=(void 0===(o=n)&&(o=!1),r&&(!o||r===k(a))&&o)?Z(a):u(0),s=(i.left+c.x)/l.x,f=(i.top+c.y)/l.y,d=i.width/l.x,p=i.height/l.y;if(a){let e=k(a),t=r&&M(r)?k(r):r,n=e,o=G(n);for(;o&&r&&t!==n;){let e=K(o),t=o.getBoundingClientRect(),r=z(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,f*=e.y,d*=e.x,p*=e.y,s+=i,f+=a,o=G(n=k(o))}}return x({width:d,height:p,x:s,y:f})}function J(e,t){let n=$(e).scrollLeft;return t?t.left+n:U(O(e)).left+n}function Q(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:J(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=k(e),r=O(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,c=0;if(o){i=o.width,a=o.height;let e=B();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,c=o.offsetTop)}return{width:i,height:a,x:l,y:c}}(e,n);else if("document"===t)r=function(e){let t=O(e),n=$(e),r=e.ownerDocument.body,o=a(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=a(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+J(e),c=-n.scrollTop;return"rtl"===z(r).direction&&(l+=a(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:c}}(O(e));else if(M(t))r=function(e,t){let n=U(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=D(e)?K(e):u(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y;return{width:a,height:l,x:o*i.x,y:r*i.y}}(t,n);else{let n=Z(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return x(r)}function et(e){return"static"===z(e).position}function en(e,t){if(!D(e)||"fixed"===z(e).position)return null;if(t)return t(e);let n=e.offsetParent;return O(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=k(e);if(F(e))return n;if(!D(e)){let t=X(e);for(;t&&!I(t);){if(M(t)&&!et(t))return t;t=X(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(P(r))&&et(r);)r=en(r,t);return r&&I(r)&&et(r)&&!H(r)?n:r||function(e){let t=X(e);for(;D(t)&&!I(t);){if(H(t))return t;if(F(t))break;t=X(t)}return null}(e)||n}let eo=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=D(t),o=O(t),i="fixed"===n,a=U(e,!0,i,t),l={scrollLeft:0,scrollTop:0},c=u(0);if(r||!r&&!i){if(("body"!==P(t)||W(o))&&(l=$(t)),r){let e=U(t,!0,i,t);c.x=e.x+t.clientLeft,c.y=e.y+t.clientTop}else o&&(c.x=J(o))}i&&!r&&o&&(c.x=J(o));let s=!o||r||i?u(0):Q(o,l);return{x:a.left+l.scrollLeft-c.x-s.x,y:a.top+l.scrollTop-c.y-s.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ei={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=O(r),l=!!t&&F(t.floating);if(r===a||l&&i)return n;let c={scrollLeft:0,scrollTop:0},s=u(1),f=u(0),d=D(r);if((d||!d&&!i)&&(("body"!==P(r)||W(a))&&(c=$(r)),D(r))){let e=U(r);s=K(r),f.x=e.x+r.clientLeft,f.y=e.y+r.clientTop}let p=!a||d||i?u(0):Q(a,c,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-c.scrollLeft*s.x+f.x+p.x,y:n.y*s.y-c.scrollTop*s.y+f.y+p.y}},getDocumentElement:O,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,l=[..."clippingAncestors"===n?F(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=Y(e,[],!1).filter(e=>M(e)&&"body"!==P(e)),o=null,i="fixed"===z(e).position,a=i?X(e):e;for(;M(a)&&!I(a);){let t=z(a),n=H(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||W(a)&&!n&&function e(t,n){let r=X(t);return!(r===n||!M(r)||I(r))&&("fixed"===z(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=X(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],c=l[0],u=l.reduce((e,n)=>{let r=ee(t,n,o);return e.top=a(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=a(r.left,e.left),e},ee(t,c,o));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:er,getElementRects:eo,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=V(e);return{width:t,height:n}},getScale:K,isElement:M,isRTL:function(e){return"rtl"===z(e).direction}};function ea(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let el=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:l,platform:c,elements:u,middlewareData:s}=t,{element:f,padding:p=0}=d(e,t)||{};if(null==f)return{};let y=b(p),w={x:n,y:r},x=m(g(o)),E=v(x),R=await c.getDimensions(f),A="y"===x,C=A?"clientHeight":"clientWidth",S=l.reference[E]+l.reference[x]-w[x]-l.floating[E],L=w[x]-l.reference[x],T=await (null==c.getOffsetParent?void 0:c.getOffsetParent(f)),P=T?T[C]:0;P&&await (null==c.isElement?void 0:c.isElement(T))||(P=u.floating[C]||l.floating[E]);let k=P/2-R[E]/2-1,O=i(y[A?"top":"left"],k),N=i(y[A?"bottom":"right"],k),M=P-R[E]-N,D=P/2-R[E]/2+(S/2-L/2),j=a(O,i(D,M)),W=!s.arrow&&null!=h(o)&&D!==j&&l.reference[E]/2-(D<O?O:N)-R[E]/2<0,F=W?D<O?D-O:D-M:0;return{[x]:w[x]+F,data:{[x]:j,centerOffset:D-j-F,...W&&{alignmentOffset:F}},reset:W}}}),ec=(e,t,n)=>{let r=new Map,o={platform:ei,...n},i={...o.platform,_c:r};return R(e,t,{...o,platform:i})};var eu=n(76129),es="undefined"!=typeof document?r.useLayoutEffect:function(){};function ef(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ef(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ef(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ed(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ed(e);return Math.round(t*n)/n}function eh(e){let t=r.useRef(e);return es(()=>{t.current=e}),t}let em=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?el({element:n.current,padding:r}).fn(t):{}:n?el({element:n,padding:r}).fn(t):{}}}),ev=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,c=await L(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+c.x,y:i+c.y,data:{...c,placement:a}}}}}(e),options:[e,t]}),eg=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:l=!0,crossAxis:c=!1,limiter:u={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=d(e,t),f={x:n,y:r},h=await A(t,s),v=g(p(o)),y=m(v),w=f[y],b=f[v];if(l){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=w+h[e],r=w-h[t];w=a(n,i(w,r))}if(c){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=b+h[e],r=b-h[t];b=a(n,i(b,r))}let x=u.fn({...t,[y]:w,[v]:b});return{...x,data:{x:x.x-n,y:x.y-r,enabled:{[y]:l,[v]:c}}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:c=!0,crossAxis:u=!0}=d(e,t),s={x:n,y:r},f=g(o),h=m(f),v=s[h],y=s[f],w=d(l,t),b="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(c){let e="y"===h?"height":"width",t=i.reference[h]-i.floating[e]+b.mainAxis,n=i.reference[h]+i.reference[e]-b.mainAxis;v<t?v=t:v>n&&(v=n)}if(u){var x,E;let e="y"===h?"width":"height",t=["top","left"].includes(p(o)),n=i.reference[f]-i.floating[e]+(t&&(null==(x=a.offset)?void 0:x[f])||0)+(t?0:b.crossAxis),r=i.reference[f]+i.reference[e]+(t?0:(null==(E=a.offset)?void 0:E[f])||0)-(t?b.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[h]:v,[f]:y}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a;let{placement:l,middlewareData:c,rects:u,initialPlacement:s,platform:f,elements:b}=t,{mainAxis:x=!0,crossAxis:E=!0,fallbackPlacements:R,fallbackStrategy:C="bestFit",fallbackAxisSideDirection:S="none",flipAlignment:L=!0,...T}=d(e,t);if(null!=(n=c.arrow)&&n.alignmentOffset)return{};let P=p(l),k=g(s),O=p(s)===s,N=await (null==f.isRTL?void 0:f.isRTL(b.floating)),M=R||(O||!L?[w(s)]:function(e){let t=w(e);return[y(e),t,y(t)]}(s)),D="none"!==S;!R&&D&&M.push(...function(e,t,n,r){let o=h(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(y)))),i}(s,L,S,N));let j=[s,...M],W=await A(t,T),F=[],H=(null==(r=c.flip)?void 0:r.overflows)||[];if(x&&F.push(W[P]),E){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),o=m(g(e)),i=v(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=w(a)),[a,w(a)]}(l,u,N);F.push(W[e[0]],W[e[1]])}if(H=[...H,{placement:l,overflows:F}],!F.every(e=>e<=0)){let e=((null==(o=c.flip)?void 0:o.index)||0)+1,t=j[e];if(t&&("alignment"!==E||k===g(t)||H.every(e=>e.overflows[0]>0&&g(e.placement)===k)))return{data:{index:e,overflows:H},reset:{placement:t}};let n=null==(i=H.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(C){case"bestFit":{let e=null==(a=H.filter(e=>{if(D){let t=g(e.placement);return t===k||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=s}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,l;let{placement:c,rects:u,platform:s,elements:f}=t,{apply:m=()=>{},...v}=d(e,t),y=await A(t,v),w=p(c),b=h(c),x="y"===g(c),{width:E,height:R}=u.floating;"top"===w||"bottom"===w?(o=w,l=b===(await (null==s.isRTL?void 0:s.isRTL(f.floating))?"start":"end")?"left":"right"):(l=w,o="end"===b?"top":"bottom");let C=R-y.top-y.bottom,S=E-y.left-y.right,L=i(R-y[o],C),T=i(E-y[l],S),P=!t.middlewareData.shift,k=L,O=T;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(O=S),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(k=C),P&&!b){let e=a(y.left,0),t=a(y.right,0),n=a(y.top,0),r=a(y.bottom,0);x?O=E-2*(0!==e||0!==t?e+t:a(y.left,y.right)):k=R-2*(0!==n||0!==r?n+r:a(y.top,y.bottom))}await m({...t,availableWidth:O,availableHeight:k});let N=await s.getDimensions(f.floating);return E!==N.width||R!==N.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=d(e,t);switch(r){case"referenceHidden":{let e=C(await A(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:S(e)}}}case"escaped":{let e=C(await A(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:S(e)}}}default:return{}}}}}(e),options:[e,t]}),eE=(e,t)=>({...em(e),options:[e,t]});var eR=n(22839),eA=n(45781),eC=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eA.jsx)(eR.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eA.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eC.displayName="Arrow";var eS=n(25182),eL=n(5016),eT=n(22883),eP=n(39068),ek=n(56486),eO="Popper",[eN,eM]=(0,eL.A)(eO),[eD,ej]=eN(eO),eW=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eA.jsx)(eD,{scope:t,anchor:o,onAnchorChange:i,children:n})};eW.displayName=eO;var eF="PopperAnchor",eH=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,a=ej(eF,n),l=r.useRef(null),c=(0,eS.s)(t,l);return r.useEffect(()=>{a.onAnchorChange(o?.current||l.current)}),o?null:(0,eA.jsx)(eR.sG.div,{...i,ref:c})});eH.displayName=eF;var eB="PopperContent",[eI,ez]=eN(eB),e$=r.forwardRef((e,t)=>{let{__scopePopper:n,side:o="bottom",sideOffset:l=0,align:u="center",alignOffset:s=0,arrowPadding:f=0,avoidCollisions:d=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:m="partial",hideWhenDetached:v=!1,updatePositionStrategy:g="optimized",onPlaced:y,...w}=e,b=ej(eB,n),[x,E]=r.useState(null),R=(0,eS.s)(t,e=>E(e)),[A,C]=r.useState(null),S=(0,ek.X)(A),L=S?.width??0,T=S?.height??0,P="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},k=Array.isArray(p)?p:[p],N=k.length>0,M={padding:P,boundary:k.filter(eV),altBoundary:N},{refs:D,floatingStyles:j,placement:W,isPositioned:F,middlewareData:H}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:a,floating:l}={},transform:c=!0,whileElementsMounted:u,open:s}=e,[f,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);ef(p,o)||h(o);let[m,v]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==R.current&&(R.current=e,v(e))},[]),b=r.useCallback(e=>{e!==A.current&&(A.current=e,y(e))},[]),x=a||m,E=l||g,R=r.useRef(null),A=r.useRef(null),C=r.useRef(f),S=null!=u,L=eh(u),T=eh(i),P=eh(s),k=r.useCallback(()=>{if(!R.current||!A.current)return;let e={placement:t,strategy:n,middleware:p};T.current&&(e.platform=T.current),ec(R.current,A.current,e).then(e=>{let t={...e,isPositioned:!1!==P.current};O.current&&!ef(C.current,t)&&(C.current=t,eu.flushSync(()=>{d(t)}))})},[p,t,n,T,P]);es(()=>{!1===s&&C.current.isPositioned&&(C.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[s]);let O=r.useRef(!1);es(()=>(O.current=!0,()=>{O.current=!1}),[]),es(()=>{if(x&&(R.current=x),E&&(A.current=E),x&&E){if(L.current)return L.current(x,E,k);k()}},[x,E,k,L,S]);let N=r.useMemo(()=>({reference:R,floating:A,setReference:w,setFloating:b}),[w,b]),M=r.useMemo(()=>({reference:x,floating:E}),[x,E]),D=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!M.floating)return e;let t=ep(M.floating,f.x),r=ep(M.floating,f.y);return c?{...e,transform:"translate("+t+"px, "+r+"px)",...ed(M.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,c,M.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:k,refs:N,elements:M,floatingStyles:D}),[f,k,N,M,D])}({strategy:"fixed",placement:o+("center"!==u?"-"+u:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:u=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=q(e),h=l||u?[...p?Y(p):[],...Y(t)]:[];h.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)});let m=p&&f?function(e,t){let n,r=null,o=O(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function u(s,f){void 0===s&&(s=!1),void 0===f&&(f=1),l();let d=e.getBoundingClientRect(),{left:p,top:h,width:m,height:v}=d;if(s||t(),!m||!v)return;let g=c(h),y=c(o.clientWidth-(p+m)),w={rootMargin:-g+"px "+-y+"px "+-c(o.clientHeight-(h+v))+"px "+-c(p)+"px",threshold:a(0,i(1,f))||1},b=!0;function x(t){let r=t[0].intersectionRatio;if(r!==f){if(!b)return u();r?u(!1,r):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==r||ea(d,e.getBoundingClientRect())||u(),b=!1}try{r=new IntersectionObserver(x,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(x,w)}r.observe(e)}(!0),l}(p,n):null,v=-1,g=null;s&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!d&&g.observe(p),g.observe(t));let y=d?U(e):null;return d&&function t(){let r=U(e);y&&!ea(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{l&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=g)||e.disconnect(),g=null,d&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===g}),elements:{reference:b.anchor},middleware:[ev({mainAxis:l+T,alignmentAxis:s}),d&&eg({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?ey():void 0,...M}),d&&ew({...M}),eb({...M,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),A&&eE({element:A,padding:f}),eq({arrowWidth:L,arrowHeight:T}),v&&ex({strategy:"referenceHidden",...M})]}),[B,I]=eK(W),z=(0,eT.c)(y);(0,eP.N)(()=>{F&&z?.()},[F,z]);let $=H.arrow?.x,X=H.arrow?.y,G=H.arrow?.centerOffset!==0,[V,K]=r.useState();return(0,eP.N)(()=>{x&&K(window.getComputedStyle(x).zIndex)},[x]),(0,eA.jsx)("div",{ref:D.setFloating,"data-radix-popper-content-wrapper":"",style:{...j,transform:F?j.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:V,"--radix-popper-transform-origin":[H.transformOrigin?.x,H.transformOrigin?.y].join(" "),...H.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eA.jsx)(eI,{scope:n,placedSide:B,onArrowChange:C,arrowX:$,arrowY:X,shouldHideArrow:G,children:(0,eA.jsx)(eR.sG.div,{"data-side":B,"data-align":I,...w,ref:R,style:{...w.style,animation:F?void 0:"none"}})})})});e$.displayName=eB;var eX="PopperArrow",eY={top:"bottom",right:"left",bottom:"top",left:"right"},eG=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=ez(eX,n),i=eY[o.placedSide];return(0,eA.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eA.jsx)(eC,{...r,ref:t,style:{...r.style,display:"block"}})})});function eV(e){return null!==e}eG.displayName=eX;var eq=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[c,u]=eK(n),s={start:"0%",center:"50%",end:"100%"}[u],f=(o.arrow?.x??0)+a/2,d=(o.arrow?.y??0)+l/2,p="",h="";return"bottom"===c?(p=i?s:`${f}px`,h=`${-l}px`):"top"===c?(p=i?s:`${f}px`,h=`${r.floating.height+l}px`):"right"===c?(p=`${-l}px`,h=i?s:`${d}px`):"left"===c&&(p=`${r.floating.width+l}px`,h=i?s:`${d}px`),{data:{x:p,y:h}}}});function eK(e){let[t,n="center"]=e.split("-");return[t,n]}var e_=eW,eZ=eH,eU=e$,eJ=eG},49679:(e,t,n)=>{n.d(t,{n:()=>f});var r=n(13072),o=n(25182),i=n(22839),a=n(22883),l=n(45781),c="focusScope.autoFocusOnMount",u="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},f=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:f=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[w,b]=r.useState(null),x=(0,a.c)(v),E=(0,a.c)(g),R=r.useRef(null),A=(0,o.s)(t,e=>b(e)),C=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(f){let e=function(e){if(C.paused||!w)return;let t=e.target;w.contains(t)?R.current=t:h(R.current,{select:!0})},t=function(e){if(C.paused||!w)return;let t=e.relatedTarget;null===t||w.contains(t)||h(R.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[f,w,C.paused]),r.useEffect(()=>{if(w){m.add(C);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(c,s);w.addEventListener(c,x),w.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(h(r,{select:t}),document.activeElement!==n)return}(d(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(w))}return()=>{w.removeEventListener(c,x),setTimeout(()=>{let t=new CustomEvent(u,s);w.addEventListener(u,E),w.dispatchEvent(t),t.defaultPrevented||h(e??document.body,{select:!0}),w.removeEventListener(u,E),m.remove(C)},0)}}},[w,x,E,C]);let S=r.useCallback(e=>{if(!n&&!f||C.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=d(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&h(i,{select:!0})):(e.preventDefault(),n&&h(o,{select:!0})):r===t&&e.preventDefault()}},[n,f,C.paused]);return(0,l.jsx)(i.sG.div,{tabIndex:-1,...y,ref:A,onKeyDown:S})});function d(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function h(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}f.displayName="FocusScope";var m=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=v(e,t)).unshift(t)},remove(t){e=v(e,t),e[0]?.resume()}}}();function v(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},56735:(e,t,n)=>{n.d(t,{A:()=>G});var r,o=function(){return(o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var a=("function"==typeof SuppressedError&&SuppressedError,n(13072)),l="right-scroll-bar-position",c="width-before-scroll-bar";function u(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var s="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,f=new WeakMap;function d(e){return e}var p=function(e){void 0===e&&(e={});var t,n,r,i,a=(t=null,void 0===n&&(n=d),r=[],i=!1,{read:function(){if(i)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,i);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(i=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){i=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var o=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(o)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return a.options=o({async:!0,ssr:!1},e),a}(),h=function(){},m=a.forwardRef(function(e,t){var n,r,l,c,d=a.useRef(null),m=a.useState({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:h}),v=m[0],g=m[1],y=e.forwardProps,w=e.children,b=e.className,x=e.removeScrollBar,E=e.enabled,R=e.shards,A=e.sideCar,C=e.noRelative,S=e.noIsolation,L=e.inert,T=e.allowPinchZoom,P=e.as,k=e.gapMode,O=i(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),N=(n=[d,t],r=function(e){return n.forEach(function(t){return u(t,e)})},(l=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return l.value},set current(value){var e=l.value;e!==value&&(l.value=value,l.callback(value,e))}}}})[0]).callback=r,c=l.facade,s(function(){var e=f.get(c);if(e){var t=new Set(e),r=new Set(n),o=c.current;t.forEach(function(e){r.has(e)||u(e,null)}),r.forEach(function(e){t.has(e)||u(e,o)})}f.set(c,n)},[n]),c),M=o(o({},O),v);return a.createElement(a.Fragment,null,E&&a.createElement(A,{sideCar:p,removeScrollBar:x,shards:R,noRelative:C,noIsolation:S,inert:L,setCallbacks:g,allowPinchZoom:!!T,lockRef:d,gapMode:k}),y?a.cloneElement(a.Children.only(w),o(o({},M),{ref:N})):a.createElement(void 0===P?"div":P,o({},M,{className:b,ref:N}),w))});m.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},m.classNames={fullWidth:c,zeroRight:l};var v=function(e){var t=e.sideCar,n=i(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,o({},n))};v.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=o:i.appendChild(document.createTextNode(o)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},y=function(){var e=g();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},w=function(){var e=y();return function(t){return e(t.styles,t.dynamic),null}},b={left:0,top:0,right:0,gap:0},x=function(e){return parseInt(e||"",10)||0},E=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[x(n),x(r),x(o)]},R=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return b;var t=E(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},A=w(),C="data-scroll-locked",S=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,u=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(u,"px ").concat(r,";\n  }\n  body[").concat(C,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(u,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(l," {\n    right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(c," {\n    margin-right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(C,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},L=function(){var e=parseInt(document.body.getAttribute(C)||"0",10);return isFinite(e)?e:0},T=function(){a.useEffect(function(){return document.body.setAttribute(C,(L()+1).toString()),function(){var e=L()-1;e<=0?document.body.removeAttribute(C):document.body.setAttribute(C,e.toString())}},[])},P=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;T();var i=a.useMemo(function(){return R(o)},[o]);return a.createElement(A,{styles:S(i,!t,o,n?"":"!important")})},k=!1;if("undefined"!=typeof window)try{var O=Object.defineProperty({},"passive",{get:function(){return k=!0,!0}});window.addEventListener("test",O,O),window.removeEventListener("test",O,O)}catch(e){k=!1}var N=!!k&&{passive:!1},M=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},D=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),j(e,r)){var o=W(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},j=function(e,t){return"v"===e?M(t,"overflowY"):M(t,"overflowX")},W=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},F=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,c=n.target,u=t.contains(c),s=!1,f=l>0,d=0,p=0;do{if(!c)break;var h=W(e,c),m=h[0],v=h[1]-h[2]-a*m;(m||v)&&j(e,c)&&(d+=v,p+=m);var g=c.parentNode;c=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!u&&c!==document.body||u&&(t.contains(c)||t===c));return f&&(o&&1>Math.abs(d)||!o&&l>d)?s=!0:!f&&(o&&1>Math.abs(p)||!o&&-l>p)&&(s=!0),s},H=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},B=function(e){return[e.deltaX,e.deltaY]},I=function(e){return e&&"current"in e?e.current:e},z=0,$=[];let X=(p.useMedium(function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(z++)[0],i=a.useState(w)[0],l=a.useRef(e);a.useEffect(function(){l.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(I),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var c=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=H(e),a=n.current,c="deltaX"in e?e.deltaX:a[0]-i[0],u="deltaY"in e?e.deltaY:a[1]-i[1],s=e.target,f=Math.abs(c)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===f&&"range"===s.type)return!1;var d=D(f,s);if(!d)return!0;if(d?o=f:(o="v"===f?"h":"v",d=D(f,s)),!d)return!1;if(!r.current&&"changedTouches"in e&&(c||u)&&(r.current=o),!o)return!0;var p=r.current||o;return F(p,t,e,"h"===p?c:u,!0)},[]),u=a.useCallback(function(e){if($.length&&$[$.length-1]===i){var n="deltaY"in e?B(e):H(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(I).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?c(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=a.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),f=a.useCallback(function(e){n.current=H(e),r.current=void 0},[]),d=a.useCallback(function(t){s(t.type,B(t),t.target,c(t,e.lockRef.current))},[]),p=a.useCallback(function(t){s(t.type,H(t),t.target,c(t,e.lockRef.current))},[]);a.useEffect(function(){return $.push(i),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:p}),document.addEventListener("wheel",u,N),document.addEventListener("touchmove",u,N),document.addEventListener("touchstart",f,N),function(){$=$.filter(function(e){return e!==i}),document.removeEventListener("wheel",u,N),document.removeEventListener("touchmove",u,N),document.removeEventListener("touchstart",f,N)}},[]);var h=e.removeScrollBar,m=e.inert;return a.createElement(a.Fragment,null,m?a.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?a.createElement(P,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}),v);var Y=a.forwardRef(function(e,t){return a.createElement(m,o({},e,{ref:t,sideCar:X}))});Y.classNames=m.classNames;let G=Y},57427:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(13072),o=0;function i(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??a()),document.body.insertAdjacentElement("beforeend",e[1]??a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},75673:(e,t,n)=>{n.d(t,{Z:()=>c});var r=n(13072),o=n(76129),i=n(22839),a=n(39068),l=n(45781),c=r.forwardRef((e,t)=>{let{container:n,...c}=e,[u,s]=r.useState(!1);(0,a.N)(()=>s(!0),[]);let f=n||u&&globalThis?.document?.body;return f?o.createPortal((0,l.jsx)(i.sG.div,{...c,ref:t}),f):null});c.displayName="Portal"},98535:(e,t,n)=>{n.d(t,{qW:()=>d});var r,o=n(13072),i=n(85271),a=n(22839),l=n(25182),c=n(22883),u=n(45781),s="dismissableLayer.update",f=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),d=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:d,onPointerDownOutside:m,onFocusOutside:v,onInteractOutside:g,onDismiss:y,...w}=e,b=o.useContext(f),[x,E]=o.useState(null),R=x?.ownerDocument??globalThis?.document,[,A]=o.useState({}),C=(0,l.s)(t,e=>E(e)),S=Array.from(b.layers),[L]=[...b.layersWithOutsidePointerEventsDisabled].slice(-1),T=S.indexOf(L),P=x?S.indexOf(x):-1,k=b.layersWithOutsidePointerEventsDisabled.size>0,O=P>=T,N=function(e,t=globalThis?.document){let n=(0,c.c)(e),r=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){h("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",i.current),i.current=r,t.addEventListener("click",i.current,{once:!0})):r()}else t.removeEventListener("click",i.current);r.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",i.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...b.branches].some(e=>e.contains(t));!O||n||(m?.(e),g?.(e),e.defaultPrevented||y?.())},R),M=function(e,t=globalThis?.document){let n=(0,c.c)(e),r=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!r.current&&h("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;[...b.branches].some(e=>e.contains(t))||(v?.(e),g?.(e),e.defaultPrevented||y?.())},R);return function(e,t=globalThis?.document){let n=(0,c.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{P===b.layers.size-1&&(d?.(e),!e.defaultPrevented&&y&&(e.preventDefault(),y()))},R),o.useEffect(()=>{if(x)return n&&(0===b.layersWithOutsidePointerEventsDisabled.size&&(r=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),b.layersWithOutsidePointerEventsDisabled.add(x)),b.layers.add(x),p(),()=>{n&&1===b.layersWithOutsidePointerEventsDisabled.size&&(R.body.style.pointerEvents=r)}},[x,R,n,b]),o.useEffect(()=>()=>{x&&(b.layers.delete(x),b.layersWithOutsidePointerEventsDisabled.delete(x),p())},[x,b]),o.useEffect(()=>{let e=()=>A({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,u.jsx)(a.sG.div,{...w,ref:C,style:{pointerEvents:k?O?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,M.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,M.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,N.onPointerDownCapture)})});function p(){let e=new CustomEvent(s);document.dispatchEvent(e)}function h(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,a.hO)(o,i):o.dispatchEvent(i)}d.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(f),r=o.useRef(null),i=(0,l.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,u.jsx)(a.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch"}};