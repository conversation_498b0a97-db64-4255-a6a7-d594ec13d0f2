# 日志分析模块开发者文档

## 1. 架构概览

日志分析模块集成在 `hotel-dashboard` 项目中，采用基于 React 的前端组件和 Web Worker 的架构，以实现高效的日志文件处理和可视化。

**主要组件及其交互：**

*   **[`hotel-dashboard/app/(dashboard)/log-analysis/page.tsx`](hotel-dashboard/app/(dashboard)/log-analysis/page.tsx:1):** 日志分析功能的主页面，负责整体布局和协调各个子组件。
*   **[`hotel-dashboard/components/log-analysis/LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx:1):** 用户界面组件，用于处理日志文件的上传。它接收用户选择的文件，并将文件内容发送给 Web Worker进行解析。
*   **[`hotel-dashboard/workers/logParser.worker.ts`](hotel-dashboard/workers/logParser.worker.ts:1):** Web Worker 脚本，在后台线程中执行日志文件的解析任务。这可以防止耗时的解析操作阻塞主 UI 线程。它接收文件内容，解析数据，并将处理后的数据块发送回主线程。
*   **[`hotel-dashboard/components/log-analysis/LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx:1):** 接收并展示由 Web Worker 解析出的数据块列表。用户可以通过复选框选择一个或多个数据块，并可使用“全选”/“全不选”按钮进行批量操作。选定的数据块 ID 通过回调函数传递给父组件 ([`page.tsx`](hotel-dashboard/app/(dashboard)/log-analysis/page.tsx:1))。
*   **[`hotel-dashboard/components/log-analysis/LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx:1):** 负责将选定的数据块中的数据显示为图表。它使用 `recharts` 库来渲染胶厚、准直数据以及相关的事件标记。
*   **[`hotel-dashboard/lib/exportUtils.ts`](hotel-dashboard/lib/exportUtils.ts:1):** 提供将 HTML 元素导出为图片（PNG/JPEG）的辅助函数。

**交互流程概述：**

1.  用户在 [`LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx:1) 中上传日志文件。
2.  [`LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx:1) 将文件内容发送给 [`logParser.worker.ts`](hotel-dashboard/workers/logParser.worker.ts:1)。
3.  [`logParser.worker.ts`](hotel-dashboard/workers/logParser.worker.ts:1) 解析文件，并将处理后的数据块 ( `ProcessedBlock[]` ) 发送回主线程。
4.  [`LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx:1) (或父组件 [`page.tsx`](hotel-dashboard/app/(dashboard)/log-analysis/page.tsx:1)) 接收数据块，并传递给 [`LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx:1)。
5.  [`LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx:1) 展示数据块列表，用户通过复选框选择要分析的数据块。
6.  [`LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx:1) 将选定的数据块 ID 列表通过回调传递给父组件 [`page.tsx`](hotel-dashboard/app/(dashboard)/log-analysis/page.tsx:1)。
7.  [`page.tsx`](hotel-dashboard/app/(dashboard)/log-analysis/page.tsx:1) 根据选定的 ID 过滤出完整的数据块，并将这些数据块 (作为 `dataChunks` prop) 传递给 [`LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx:1)。
8.  [`LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx:1) 接收 `dataChunks` prop，格式化数据并渲染图表。

参考 Memory Bank 中的 [`decisionLog.md`](memory-bank/decisionLog.md:1) 获取更详细的架构决策和理由。

## 2. 数据流

详细的数据处理流程如下：

1.  **文件上传与读取 (在 [`LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx:1) 中):**
    *   用户通过一个自定义的、样式化的文件输入界面选择日志文件。该界面由一个 `<label>` 元素包裹一个隐藏的 `<input type="file">` 实现。用户点击标签的特定区域（例如，标有“上传文件”的部分）来触发文件选择对话框。
    *   选择文件后，`handleActualFileChange` 函数会更新组件状态，包括 `selectedFile` (实际文件对象) 和 `selectedFileName` (用于显示的文件名)。
    *   使用 `FileReader` API 异步读取文件内容，通常作为文本字符串。
    *   文件内容被发送到 [`logParser.worker.ts`](hotel-dashboard/workers/logParser.worker.ts:1) 进行处理。

2.  **日志解析 (在 [`logParser.worker.ts`](hotel-dashboard/workers/logParser.worker.ts:1) 中):**
    *   Worker 接收到原始日志文本。
    *   执行核心解析逻辑，将文本数据转换为结构化的 `ProcessedBlock[]` 数组。每个 `ProcessedBlock` 代表日志文件中的一个有效数据段，包含原始数据点、时间戳、以及可能的事件信息。
    *   解析逻辑可能包括：
        *   按行分割日志。
        *   使用正则表达式或字符串匹配提取关键数据（如时间戳、胶厚值、准直值、事件标识）。
        *   将提取的数据转换为适当的类型（例如，将日期字符串转换为 `Date` 对象或数字时间戳）。
        *   识别并标记特定的事件，如放气阀打开。
    *   解析完成后，Worker 将 `ProcessedBlock[]` 数组通过 `postMessage` 发送回主线程。

3.  **数据块接收与管理 (在 [`LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx:1) 或 [`page.tsx`](hotel-dashboard/app/(dashboard)/log-analysis/page.tsx:1) -> [`LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx:1) 中):**
    *   主线程监听来自 Worker 的消息。
    *   收到 `ProcessedBlock[]` 数据后，更新应用状态，并将这些数据块传递给 [`LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx:1)。
    *   [`LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx:1) 渲染数据块列表，允许用户通过复选框选择一个或多个数据块。它还提供“全选”和“全不选”功能。
    *   当用户的选择发生变化时，[`LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx:1) 通过回调函数将其选定的数据块 ID 数组传递给父组件 [`page.tsx`](hotel-dashboard/app/(dashboard)/log-analysis/page.tsx:1)。

4.  **数据传递到图表 (在 [`page.tsx`](hotel-dashboard/app/(dashboard)/log-analysis/page.tsx:1) 和 [`LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx:1) 中):**
    *   父组件 [`page.tsx`](hotel-dashboard/app/(dashboard)/log-analysis/page.tsx:1) 接收来自 [`LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx:1) 的选定数据块 ID 数组。
    *   [`page.tsx`](hotel-dashboard/app/(dashboard)/log-analysis/page.tsx:1) 根据这些 ID 从其持有的完整 `ProcessedBlock[]` (或 `FormattedBlockData[]`) 列表中筛选出相应的数据块。
    *   筛选后的完整数据块数组作为 `dataChunks` prop 传递给 [`LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx:1)。
    *   [`LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx:1) 接收 `dataChunks` prop (即用户选择的 `FormattedBlockData[]`)。
    *   对选定的数据块进行进一步处理和格式化，以适应 `recharts` 图表库的输入要求。这可能包括：
        *   合并来自多个选定块的数据点。
        *   为每个数据系列（如胶厚、准直）创建唯一的 `dataKey`。
        *   将时间戳转换为 `recharts` X轴兼容的格式（通常是数字时间戳）。
        *   提取事件标记（如放气阀打开事件）并准备用于 `<ReferenceLine>`。
    *   使用格式化后的数据渲染图表，包括线条、X/Y轴、图例和事件标记。

## 3. Web Worker (`logParser.worker.ts`)

### 3.1 职责和核心解析逻辑

[`logParser.worker.ts`](hotel-dashboard/workers/logParser.worker.ts:1) 的主要职责是在一个独立的后台线程中处理计算密集型的日志文件解析任务，从而避免阻塞浏览器主线程，确保用户界面的响应性。

**核心解析逻辑概述：**

1.  **接收消息：** Worker 通过 `self.onmessage` 监听来自主线程的消息。预期消息包含要解析的日志文件内容（通常是字符串）。
2.  **数据解析：**
    *   根据预定义的日志格式，逐行或按块解析文本数据。
    *   提取关键信息，如时间戳、传感器读数（胶厚、准直等）、事件标记。
    *   将提取的字符串数据转换为适当的数据类型（数字、日期对象）。
    *   将解析后的数据组织成 `ProcessedBlock` 结构。一个日志文件可能被解析成多个 `ProcessedBlock`。
3.  **数据格式化：** 在将数据发送回主线程之前，可能会进行初步的数据格式化，例如调用 `formatBlockDataForFrontend` 函数，该函数准备 `data_points` (包含时间戳、胶厚、准直) 和 `valve_open_events`。
4.  **发送结果：** 解析完成后，Worker 通过 `self.postMessage` 将包含 `ProcessedBlock[]` (或经过 `formatBlockDataForFrontend` 处理的 `FormattedBlockData[]`) 的结果发送回主线程。
5.  **错误处理：** Worker 应包含错误处理逻辑，以便在解析失败时通知主线程。

### 3.2 与主线程的通信协议

*   **主线程到 Worker：**
    *   **消息格式：** 通常是一个对象，包含一个 `type` 字段（例如 `'PARSE_LOG'`) 和一个 `payload` 字段（包含日志文件内容的字符串）。
      ```typescript
      // 主线程发送消息示例
      worker.postMessage({
        type: 'PARSE_LOG',
        payload: fileContentString
      });
      ```

*   **Worker 到主线程：**
    *   **成功消息格式：** 一个对象，包含一个 `type` 字段（例如 `'PARSE_SUCCESS'`) 和一个 `payload` 字段（包含 `ProcessedBlock[]` 或 `FormattedBlockData[]`）。
      ```typescript
      // Worker 发送成功消息示例
      self.postMessage({
        type: 'PARSE_SUCCESS',
        payload: processedData // ProcessedBlock[] or FormattedBlockData[]
      });
      ```
    *   **错误消息格式：** 一个对象，包含一个 `type` 字段（例如 `'PARSE_ERROR'`) 和一个 `error` 字段（包含错误信息或错误对象）。
      ```typescript
      // Worker 发送错误消息示例
      self.postMessage({
        type: 'PARSE_ERROR',
        error: 'Failed to parse log file: Invalid format.'
      });
      ```

### 3.3 定义的关键数据结构/接口

(以下接口定义基于典型实现，具体请参考 [`logParser.worker.ts`](hotel-dashboard/workers/logParser.worker.ts:1) 和相关类型定义文件)

```typescript
interface LogDataPoint {
  timestamp: number; // 通常是毫秒级时间戳
  glue_thickness?: number;
  collimation_x?: number;
  collimation_y?: number;
  collimation_diff?: number; // 通常是 collimation_x 和 collimation_y 的某种组合或差异
  // 其他可能的传感器数据
}

interface ValveOpenEvent {
  timestamp: number; // 事件发生的时间戳
  label: string;     // 事件标签，例如 "放气阀打开"
}

// Worker 内部处理的原始数据块
interface ProcessedBlock {
  id: string; // 唯一标识符
  block_number: number;
  start_time: string; // 原始时间戳字符串
  end_time: string;   // 原始时间戳字符串
  data_points: Array<{
    time: string; // 原始时间戳字符串
    glue_thickness?: number;
    collimation_x?: number;
    collimation_y?: number;
    // ... 其他原始字段
  }>;
  valve_open_events_raw?: Array<{ time: string; [key: string]: any }>; // 原始事件数据
  // 其他元数据
}

// Worker 发送给前端的格式化数据块
interface FormattedBlockData {
  id: string;
  block_number: number;
  data_points: Array<{
    timestamp: number; // 数字时间戳
    glue_thickness?: number;
    collimation_diff?: number; // 计算后的准直差异
  }>;
  valve_open_events: ValveOpenEvent[];
}
```

## 4. 关键组件详解

### 4.1 [`LogFileUpload.tsx`](hotel-dashboard/components/log-analysis/LogFileUpload.tsx:1)

*   **Props:**
    *   `onProcessingStart: () => void;` (可选): 文件开始处理时的回调。
    *   `onProcessingComplete: (data: FormattedBlockData[]) => void;`: 文件成功处理完成后的回调，传递解析后的数据。
    *   `onProcessingError: (error: string) => void;`: 文件处理出错时的回调。
*   **State:**
    *   `isProcessing: boolean;`: 指示文件是否正在处理中。
    *   `selectedFile: File | null;`: 用户选择的实际文件对象。
    *   `selectedFileName: string;`: 用于在UI上显示的已选择文件名，默认为“未选择文件”。
    *   `error: string | null;`: 处理过程中发生的错误信息。
    *   `fileStats: { totalLines: number | null, firstLogTime: string | null, lastLogTime: string | null } | null;`: 存储已处理文件的统计信息，如总行数、最早和最晚日志时间。
*   **主要功能：**
    *   提供一个自定义样式的两段式文件输入界面：左侧为“上传文件”按钮区域，右侧显示选定文件名或“未选择文件”。这是通过一个 `<label>` 元素和一个隐藏的 `<input type="file" className="sr-only">` 实现的。
    *   通过 `handleActualFileChange` 函数处理隐藏输入框的 `onChange` 事件，更新 `selectedFile` 和 `selectedFileName` 状态。
    *   读取文件内容（包括使用 `jschardet` 进行编码检测和 `TextDecoder` 进行解码）。
    *   实例化并管理 `logParser.worker.ts`。
    *   向 Worker 发送文件内容进行解析。
    *   监听来自 Worker 的消息（成功或错误）。
    *   在 Worker 成功返回 `ProcessedBlock[]` 后，调用内部函数（例如 `calculateAndSetFileStats`）计算日志文件的总行数、最早和最晚的日志时间。
    *   更新 `fileStats` 状态以存储这些统计信息。
    *   在 UI 上显示这些统计信息，通常在文件名下方。
    *   调用相应的 props 回调函数，将结果或错误传递给父组件。
    *   显示处理状态、错误信息以及文件统计信息。
    *   UI 间距已调整（例如 `CardContent` 使用 `space-y-6 p-6`，文件选择区域使用 `gap-3`）以优化视觉效果。

### 4.2 [`LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx:1)

*   **Props:**
    *   `processedData: FormattedBlockData[];`: 从 Worker 获取并由父组件传入的已处理数据块数组。
    *   `selectedBlockIds: string[];`: (通常由父组件管理) 当前选中的数据块 ID 数组。
    *   `onSelectionChange: (selectedIds: string[]) => void;`: 当用户选择的数据块发生变化时的回调，将新的选中 ID 数组通知父组件。
    *   `onSelectAll: () => void;` (可选): 点击“全选”按钮时的回调。
    *   `onDeselectAll: () => void;` (可选): 点击“全不选”按钮时的回调。
 *   **State:**
    *   (通常，选择状态由父组件通过 `selectedBlockIds` prop 控制，但组件内部可能需要管理临时的选择状态或UI交互)
    *   `displayAreaRef: React.RefObject<HTMLDivElement>;`: 用于引用包含所有可选择数据块列表的 DOM 元素，以便将其传递给导出函数。
    *   `isExportingAll: boolean;`: 状态，用于跟踪整个区域是否正在导出，以禁用按钮并显示加载状态。
 *   **主要功能：**
    *   接收 `processedData` 数组并在界面上渲染数据块列表。
    *   每个数据块通常显示一些摘要信息（如块编号、时间范围）。
    *   为每个数据块提供一个复选框，允许用户选择。
    *   提供“全选”和“全不选”按钮，触发相应的 `onSelectAll` 和 `onDeselectAll` 回调 (或者直接调用 `onSelectionChange` 传递所有/空 ID)。
    *   当用户的选择（单个复选框、全选、全不选）发生变化时，调用 `onSelectionChange` 回调，将新的选中 ID 数组通知父组件 ([`page.tsx`](hotel-dashboard/app/(dashboard)/log-analysis/page.tsx:1))。
    *   **图片导出功能 (导出整个显示区域):**
        *   在卡片头部 ([`CardHeader`](hotel-dashboard/components/ui/card.tsx:1)) 添加了一个“导出全部图片”按钮 ([`Button`](hotel-dashboard/components/ui/button.tsx:1))。
        *   按钮的 `disabled` 状态由 `isExportingAll` 和 `hasContentToExport` (确保有数据块可导出) 控制。
        *   按钮文本根据 `isExportingAll` 状态显示“导出中...”或“导出全部图片”。
        *   包含数据块列表的 `div` 通过 `displayAreaRef` 引用。
        *   `handleExportAllImages` 异步函数：
            *   检查 `displayAreaRef.current` 是否存在，以及是否有数据块 (`processedDataChunks`)。
            *   设置 `isExportingAll` 为 `true`。
            *   调用 [`exportElementAsImage`](hotel-dashboard/lib/exportUtils.ts:4) 函数，传入 `displayAreaRef.current`、动态生成的文件名 (例如 `log-analysis-summary-${Date.now()}.png`)。
            *   使用 `try...catch...finally` 结构处理导出过程，并在 `finally` 块中将 `isExportingAll` 设置回 `false`。
            *   错误处理主要由 [`exportElementAsImage`](hotel-dashboard/lib/exportUtils.ts:4) 内部的 `toast` 完成。

### 4.3 [`LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx:1)

*   **Props:**
    *   `dataChunks: FormattedBlockData[];`: 用户选择的、需要进行可视化展示的数据块数组。此 prop 由父组件 ([`page.tsx`](hotel-dashboard/app/(dashboard)/log-analysis/page.tsx:1)) 根据从 [`LogDisplayArea.tsx`](hotel-dashboard/components/log-analysis/LogDisplayArea.tsx:1) 收到的选定 ID 进行筛选后传入。
*   **State:**
    *   `chartData: any[];` (或其他适合 `recharts` 的结构): 经过格式化后，用于 `recharts` 渲染的最终数据。
    *   `linesToShow: Array<{ dataKey: string; stroke: string; name: string; yAxisId?: string }>;`: 定义图表中要显示的线条及其属性。
    *   `referenceLines: ValveOpenEvent[];`: 用于在图表上标记的事件。
    *   `chartContainerRef: React.RefObject<HTMLDivElement>;`: 用于引用图表容器 DOM 元素，以便将其传递给导出函数。
    *   `isExporting: boolean;`: 状态，用于跟踪图表是否正在导出，以禁用按钮并显示加载状态。
*   **主要功能：**
    *   **数据格式化与合并：**
        *   当 `dataChunks` prop 更新时，对传入的数据进行处理。
        *   将来自多个选定数据块的 `data_points` 合并到一个统一的时间序列中。
        *   为每个数据块的不同数据系列（如 `glue_thickness_block_1`, `collimation_diff_block_1`, `glue_thickness_block_2` 等）生成唯一的 `dataKey`。
        *   确保所有数据点按时间戳正确排序。
        *   提取所有选定块中的 `valve_open_events`。
    *   **图表渲染逻辑 (`recharts`):**
        *   使用 `<ComposedChart>` 或 `<LineChart>` 作为基础。
        *   动态生成 `<Line>` 组件，每个组件对应一个数据系列（例如，一个块的胶厚，另一个块的胶厚）。
            *   使用 `dataKey` 属性指定数据源。
            *   使用 `stroke` 属性设置线条颜色（通常从预定义的颜色数组中循环获取）。
            *   使用 `name` 属性设置图例中显示的名称。
            *   使用 `yAxisId` 关联到特定的 Y 轴（如果需要多个 Y 轴）。
            *   设置 `connectNulls={true}` 以处理数据点不连续的情况。
        *   配置 `<XAxis>`：
            *   `type="number"`，`dataKey="timestamp"` (使用数字时间戳)。
            *   `domain={['dataMin', 'dataMax']}`。
            *   `tickFormatter` 将数字时间戳转换为可读的日期时间字符串。
        *   配置一个或多个 `<YAxis>`：
            *   根据数据显示的需要（例如，一个用于胶厚，一个用于准直）。
            *   使用 `yAxisId` 与 `<Line>` 组件关联。
        *   配置 `<Tooltip>` 以在鼠标悬停时显示数据点详情。
        *   配置 `<Legend>` 以显示图例。
        *   使用 `<ReferenceLine>` 组件在图表上标记 `valve_open_events` 的时间点。
            *   `x` 属性设置为事件的数字时间戳。
            *   `stroke` 设置线条颜色。
            *   `<Label>` 子组件用于显示事件名称。
    *   **颜色管理：** 使用预定义的颜色数组 (`COLORS`) 为不同数据块的系列分配不同的颜色，以便区分。
    *   **图片导出功能:**
        *   在卡片头部 ([`CardHeader`](hotel-dashboard/components/ui/card.tsx:1)) 添加了一个“导出图片”按钮 ([`Button`](hotel-dashboard/components/ui/button.tsx:1))。
        *   按钮的 `disabled` 状态由 `isExporting` 和 `hasChartData` (确保有数据可导出) 控制。
        *   按钮文本根据 `isExporting` 状态显示“导出中...”或“导出图片”。
        *   图表内容被包裹在一个 `div` 中，并通过 `chartContainerRef` 引用。
        *   `handleExportChart` 异步函数：
            *   检查 `chartContainerRef.current` 是否存在，以及是否有图表数据 (`chartData`)。
            *   设置 `isExporting` 为 `true`。
            *   调用 [`exportElementAsImage`](hotel-dashboard/lib/exportUtils.ts:4) 函数，传入 `chartContainerRef.current`、动态生成的文件名 (例如 `log-chart-${Date.now()}.png`)。
            *   使用 `try...catch...finally` 结构处理导出过程，并在 `finally` 块中将 `isExporting` 设置回 `false`。
            *   错误处理主要由 [`exportElementAsImage`](hotel-dashboard/lib/exportUtils.ts:4) 内部的 `toast` 完成。

### 4.4 [`hotel-dashboard/lib/exportUtils.ts`](hotel-dashboard/lib/exportUtils.ts:1)

*   **用途:** 提供将 HTML 元素导出为图片（PNG 或 JPEG 格式）的辅助函数。
*   **依赖项:**
    *   `dom-to-image-more`: 用于将 DOM 元素转换为图片数据。
    *   `../components/ui/use-toast`: 用于显示操作结果的通知。
*   **主要函数:**
    *   **`exportElementAsImage(element: HTMLElement, fileName: string, type: 'png' | 'jpeg' = 'png'): Promise<void>`**
        *   **参数:**
            *   `element`: 需要导出的 HTML 元素。
            *   `fileName`: 导出的图片文件名 (不含扩展名，例如 "my-chart")。扩展名会根据 `type` 参数自动添加。
            *   `type` (可选): 图片类型，可以是 `'png'` (默认) 或 `'jpeg'`。
        *   **功能:**
            *   使用 `dom-to-image-more` 库将指定的 `element` 转换为图片数据 URL。
            *   为了解决内容截断问题，并确保捕获元素的完整内容，在调用 `domtoimage.toPng` 或 `domtoimage.toJpeg` 时，向 `options` 对象添加了以下关键属性：
                *   `width: element.scrollWidth`: 设置捕获区域的宽度为元素内容的实际滚动宽度。
                *   `height: element.scrollHeight`: 设置捕获区域的高度为元素内容的实际滚动高度。
                *   `style: { 'overflow': 'visible !important', border: 'none !important', margin: '0 !important', padding: '0 !important', 'box-shadow': 'none !important' }`: 强制元素的 `overflow` 样式为 `visible`，并重置边框、边距、内边距和阴影，以消除不必要的边框并改善视觉一致性，防止内容被隐藏或裁剪。
                *   `cacheBust: true`: 禁用缓存，确保每次都捕获元素的最新状态，避免使用旧的或不正确的缓存图像。
            *   文件名现在在函数内部根据传入的 `fileName` (不带扩展名) 和 `type` 参数动态生成，例如 `my-chart.png`。
            *   对于 PNG 格式 (默认)，仍然会设置白色背景 (`bgcolor: '#ffffff'`)，以确保具有透明背景的图表（如 `recharts` 图表）能够正确导出，避免背景变黑或透明。
            *   对于 JPEG 格式，可以设置图片质量 (当前实现中为 `quality: 0.95`，如果库支持的话，但主要焦点是上述解决截断的选项)。
            *   调用 `downloadImage` 函数以下载生成的图片。
            *   使用 `toast` 组件向用户显示导出成功或失败的通知。
            *   错误会在控制台打印，并通过 `toast` 通知用户。
    *   **`downloadImage(dataUrl: string, fileName: string): void`**
        *   **参数:**
            *   `dataUrl`: 图片的 Base64 数据 URL。
            *   `fileName`: 下载时使用的文件名。
        *   **功能:**
            *   创建一个临时的 `<a>` (锚点) 元素。
            *   将 `<a>` 元素的 `href` 属性设置为 `dataUrl`。
            *   将 `<a>` 元素的 `download` 属性设置为 `fileName`，这会提示浏览器下载文件而不是导航到 URL。
            *   通过编程方式点击该 `<a>` 元素来触发文件下载。
            *   下载完成后，从文档中移除临时的 `<a>` 元素。

## 6. 扩展和自定义

*   **支持新的日志格式：**
    *   主要修改 [`logParser.worker.ts`](hotel-dashboard/workers/logParser.worker.ts:1) 中的解析逻辑。
    *   可能需要更新 `ProcessedBlock` 和 `FormattedBlockData` 接口以适应新的数据字段。
    *   如果新的数据字段需要在图表中显示，则需要更新 [`LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx:1) 的数据准备和渲染逻辑。
*   **添加新的图表类型：**
    *   在 [`LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx:1) 中，可以利用 `recharts` 的 `<ComposedChart>` 来组合不同的图表类型（如 `<Bar>`, `<Area>`）。
    *   根据新图表类型的需要调整数据格式化逻辑。
    *   可能需要添加新的 UI 元素让用户选择图表类型。
*   **修改事件标记：**
    *   更新 [`logParser.worker.ts`](hotel-dashboard/workers/logParser.worker.ts:1) 以识别和提取新的事件类型。
    *   更新 `ValveOpenEvent` 接口（如果需要）。
    *   在 [`LogChartView.tsx`](hotel-dashboard/components/log-analysis/LogChartView.tsx:1) 中调整 `<ReferenceLine>` 的创建逻辑以显示新的事件。
*   **国际化 (i18n)：**
    *   对于所有面向用户的文本（标签、按钮、图表标题、工具提示等），应使用 i18n 库进行管理。

## 7. 代码位置

*   **React 组件:** [`hotel-dashboard/components/log-analysis/`](hotel-dashboard/components/log-analysis/)
*   **页面:** [`hotel-dashboard/app/(dashboard)/log-analysis/page.tsx`](hotel-dashboard/app/(dashboard)/log-analysis/page.tsx:1)
*   **Web Worker:** [`hotel-dashboard/workers/logParser.worker.ts`](hotel-dashboard/workers/logParser.worker.ts:1)
*   **类型定义:** (可能在组件内部，或共享的 `types` 目录中)