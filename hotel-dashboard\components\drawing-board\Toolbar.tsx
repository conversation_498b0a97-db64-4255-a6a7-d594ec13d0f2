import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { ShapeType } from '@/types/drawing-board';

interface ToolbarProps {
  canvasSize: { width: number; height: number };
  setCanvasSize: (size: { width: number; height: number }) => void;
  setGrid: (grid: { rows: number; cols: number }) => void;
  grid: { rows: number; cols: number };
  selectedColor: string;
  setSelectedColor: (color: string) => void;
  selectedShapeType: ShapeType;
  setSelectedShapeType: (type: ShapeType) => void;
  diameter: number;
  setDiameter: (d: number) => void;
  replaceColor: (oldColor: string, newColor: string) => void;
  resetCanvas: () => void;
  exportCanvas: (format: 'png' | 'jpeg') => void;
  binarizationEnabled: boolean;
  setBinarizationEnabled: (enabled: boolean) => void;
}

const Toolbar: React.FC<ToolbarProps> = ({
  canvasSize,
  setCanvasSize,
  setGrid,
  grid,
  selectedColor,
  setSelectedColor,
  selectedShapeType,
  setSelectedShapeType,
  diameter,
  setDiameter,
  replaceColor,
  resetCanvas,
  exportCanvas,
  binarizationEnabled,
  setBinarizationEnabled,
}) => {
  const [oldColor, setOldColor] = React.useState('#000000');
  const [newColor, setNewColor] = React.useState('#ff0000');
  const [cellSize, setCellSize] = React.useState({ width: 0, height: 0 });

  React.useEffect(() => {
    if (grid.cols > 0 && grid.rows > 0) {
      setCellSize({
        width: canvasSize.width / grid.cols,
        height: canvasSize.height / grid.rows,
      });
    }
  }, [canvasSize, grid]);

  return (
    <Card className="w-full md:w-80">
      <CardHeader>
        <CardTitle>工具栏</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label>画布尺寸 (px)</Label>
          <div className="flex gap-2">
            <Input
              type="number"
              defaultValue={1920}
              onChange={(e) =>
                setCanvasSize({
                  width: parseInt(e.target.value),
                  height: canvasSize.height,
                })
              }
              placeholder="宽度"
            />
            <Input
              type="number"
              defaultValue={1080}
              onChange={(e) =>
                setCanvasSize({
                  width: canvasSize.width,
                  height: parseInt(e.target.value),
                })
              }
              placeholder="高度"
            />
          </div>
        </div>
        <div className="space-y-2">
          <Label>网格 (列x行)</Label>
          <div className="flex gap-2">
            <Input
              type="number"
              value={grid.cols}
              onChange={(e) => setGrid({ ...grid, cols: parseInt(e.target.value) })}
              placeholder="列"
            />
            <Input
              type="number"
              value={grid.rows}
              onChange={(e) => setGrid({ ...grid, rows: parseInt(e.target.value) })}
              placeholder="行"
            />
          </div>
        </div>
        <div className="space-y-1">
            <Label>单元格尺寸</Label>
            <div className="text-sm text-muted-foreground p-2 border rounded-md">
                <div>宽: {cellSize.width.toFixed(2)} px</div>
                <div>高: {cellSize.height.toFixed(2)} px</div>
            </div>
        </div>
        <div className="space-y-2">
          <Label>图形类型</Label>
          <Select value={selectedShapeType} onValueChange={(v) => setSelectedShapeType(v as ShapeType)}>
            <SelectTrigger>
              <SelectValue placeholder="选择图形" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="circle">圆形</SelectItem>
              <SelectItem value="square">MTF</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label>直径</Label>
          <Input
            type="number"
            value={diameter}
            onChange={(e) => setDiameter(parseInt(e.target.value, 10) || 0)}
          />
        </div>
        <div className="space-y-2">
          <Label>图形颜色</Label>
          <Input
            type="color"
            value={selectedColor}
            onChange={(e) => setSelectedColor(e.target.value)}
            className="w-full"
          />
        </div>
        <div className="space-y-2 border-t pt-4">
          <Label>替换颜色</Label>
          <div className="flex items-center gap-2">
            <Input placeholder="旧颜色" type="color" value={oldColor} onChange={e => setOldColor(e.target.value)} />
            <span>{'>'}</span>
            <Input placeholder="新颜色" type="color" value={newColor} onChange={e => setNewColor(e.target.value)} />
          </div>
          <Button onClick={() => replaceColor(oldColor, newColor)} className="w-full">
            替换
          </Button>
        </div>
        <div className="space-y-2 border-t pt-4">
            <Label>画布操作</Label>
            <Button onClick={resetCanvas} className="w-full" variant="destructive">
                重置画布
            </Button>
            <Button onClick={() => exportCanvas('png')} className="w-full">
                导出为 PNG
            </Button>
            <Button onClick={() => exportCanvas('jpeg')} className="w-full">
                导出为 JPEG
            </Button>
            <div className="flex items-center justify-between pt-2">
               <Label htmlFor="binarization-switch">导出时二值化</Label>
               <Switch
                   id="binarization-switch"
                   checked={binarizationEnabled}
                   onCheckedChange={setBinarizationEnabled}
               />
           </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default Toolbar;