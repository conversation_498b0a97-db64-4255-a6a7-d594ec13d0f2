(()=>{var e={};e.id=888,e.ids=[888],e.modules={4761:()=>{},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},28113:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>c,routeModule:()=>o,serverHooks:()=>d,workAsyncStorage:()=>i,workUnitAsyncStorage:()=>n});var s=r(44713),a=r(18198),u=r(84557),p=r(49281);let o=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/feature/update/route",pathname:"/api/feature/update",filename:"route",bundlePath:"app/api/feature/update/route"},resolvedPagePath:"D:\\pycode\\support_chart2\\hotel-dashboard\\app\\api\\feature\\update\\route.ts",nextConfigOutput:"",userland:p}),{workAsyncStorage:i,workUnitAsyncStorage:n,serverHooks:d}=o;function c(){return(0,u.patchFetch)({workAsyncStorage:i,workUnitAsyncStorage:n})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44713:(e,t,r)=>{"use strict";e.exports=r(44870)},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},49281:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68313:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[557],()=>r(28113));module.exports=s})();