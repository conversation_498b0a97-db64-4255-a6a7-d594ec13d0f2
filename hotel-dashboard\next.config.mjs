/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  webpack: (config, { isServer }) => {
    // Only externalize canvas on the server side to avoid build issues
    if (isServer) {
      config.externals.push('canvas');
    }
    return config;
  },
}

export default nextConfig
